var e={622:(e,t,n)=>{n.d(t,{Ob:()=>B,Qv:()=>V,XX:()=>I,fF:()=>o,h:()=>b,q6:()=>z,uA:()=>k,zO:()=>s});var r,o,i,s,u,_,c,l,a,f,p,h,v={},d=[],y=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,g=Array.isArray;function m(e,t){for(var n in t)e[n]=t[n];return e}function w(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function b(e,t,n){var o,i,s,u={};for(s in t)"key"==s?o=t[s]:"ref"==s?i=t[s]:u[s]=t[s];if(arguments.length>2&&(u.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===u[s]&&(u[s]=e.defaultProps[s]);return x(e,u,o,i,null)}function x(e,t,n,r,s){var u={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,constructor:void 0,__v:null==s?++i:s,__i:-1,__u:0};return null==s&&null!=o.vnode&&o.vnode(u),u}function S(e){return e.children}function k(e,t){this.props=e,this.context=t}function E(e,t){if(null==t)return e.__?E(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?E(e):null}function P(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return P(e)}}function C(e){(!e.__d&&(e.__d=!0)&&u.push(e)&&!O.__r++||_!==o.debounceRendering)&&((_=o.debounceRendering)||c)(O)}function O(){var e,t,n,r,i,s,_,c;for(u.sort(l);e=u.shift();)e.__d&&(t=u.length,r=void 0,s=(i=(n=e).__v).__e,_=[],c=[],n.__P&&((r=m({},i)).__v=i.__v+1,o.vnode&&o.vnode(r),W(n.__P,r,i,n.__n,n.__P.namespaceURI,32&i.__u?[s]:null,_,null==s?E(i):s,!!(32&i.__u),c),r.__v=i.__v,r.__.__k[r.__i]=r,F(_,r,c),r.__e!=s&&P(r)),u.length>t&&u.sort(l));O.__r=0}function T(e,t,n,r,o,i,s,u,_,c,l){var a,f,p,h,y,g=r&&r.__k||d,m=t.length;for(n.__d=_,$(n,t,g),_=n.__d,a=0;a<m;a++)null!=(p=n.__k[a])&&(f=-1===p.__i?v:g[p.__i]||v,p.__i=a,W(e,p,f,o,i,s,u,_,c,l),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&L(f.ref,null,p),l.push(p.ref,p.__c||h,p)),null==y&&null!=h&&(y=h),65536&p.__u||f.__k===p.__k?_=M(p,_,e):"function"==typeof p.type&&void 0!==p.__d?_=p.__d:h&&(_=h.nextSibling),p.__d=void 0,p.__u&=-196609);n.__d=_,n.__e=y}function $(e,t,n){var r,o,i,s,u,_=t.length,c=n.length,l=c,a=0;for(e.__k=[],r=0;r<_;r++)null!=(o=t[r])&&"boolean"!=typeof o&&"function"!=typeof o?(s=r+a,(o=e.__k[r]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?x(null,o,null,null,null):g(o)?x(S,{children:o},null,null,null):void 0===o.constructor&&o.__b>0?x(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=e,o.__b=e.__b+1,i=null,-1!==(u=o.__i=N(o,n,s,l))&&(l--,(i=n[u])&&(i.__u|=131072)),null==i||null===i.__v?(-1==u&&a--,"function"!=typeof o.type&&(o.__u|=65536)):u!==s&&(u==s-1?a--:u==s+1?a++:(u>s?a--:a++,o.__u|=65536))):o=e.__k[r]=null;if(l)for(r=0;r<c;r++)null!=(i=n[r])&&0==(131072&i.__u)&&(i.__e==e.__d&&(e.__d=E(i)),D(i,i))}function M(e,t,n){var r,o;if("function"==typeof e.type){for(r=e.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=e,t=M(r[o],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=E(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8===t.nodeType);return t}function N(e,t,n,r){var o=e.key,i=e.type,s=n-1,u=n+1,_=t[n];if(null===_||_&&o==_.key&&i===_.type&&0==(131072&_.__u))return n;if(r>(null!=_&&0==(131072&_.__u)?1:0))for(;s>=0||u<t.length;){if(s>=0){if((_=t[s])&&0==(131072&_.__u)&&o==_.key&&i===_.type)return s;s--}if(u<t.length){if((_=t[u])&&0==(131072&_.__u)&&o==_.key&&i===_.type)return u;u++}}return-1}function j(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||y.test(t)?n:n+"px"}function H(e,t,n,r,o){var i;e:if("style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||j(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||j(e.style,t,n[t])}else if("o"===t[0]&&"n"===t[1])i=t!==(t=t.replace(/(PointerCapture)$|Capture$/i,"$1")),t=t.toLowerCase()in e||"onFocusOut"===t||"onFocusIn"===t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?r?n.u=r.u:(n.u=a,e.addEventListener(t,i?p:f,i)):e.removeEventListener(t,i?p:f,i);else{if("http://www.w3.org/2000/svg"==o)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!==t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function U(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=a++;else if(t.t<n.u)return;return n(o.event?o.event(t):t)}}}function W(e,t,n,r,i,s,u,_,c,l){var a,f,p,h,v,d,y,w,b,x,E,P,C,O,$,M,N=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),s=[_=t.__e=n.__e]),(a=o.__b)&&a(t);e:if("function"==typeof N)try{if(w=t.props,b="prototype"in N&&N.prototype.render,x=(a=N.contextType)&&r[a.__c],E=a?x?x.props.value:a.__:r,n.__c?y=(f=t.__c=n.__c).__=f.__E:(b?t.__c=f=new N(w,E):(t.__c=f=new k(w,E),f.constructor=N,f.render=R),x&&x.sub(f),f.props=w,f.state||(f.state={}),f.context=E,f.__n=r,p=f.__d=!0,f.__h=[],f._sb=[]),b&&null==f.__s&&(f.__s=f.state),b&&null!=N.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=m({},f.__s)),m(f.__s,N.getDerivedStateFromProps(w,f.__s))),h=f.props,v=f.state,f.__v=t,p)b&&null==N.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),b&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(b&&null==N.getDerivedStateFromProps&&w!==h&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(w,E),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(w,f.__s,E)||t.__v===n.__v)){for(t.__v!==n.__v&&(f.props=w,f.state=f.__s,f.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),P=0;P<f._sb.length;P++)f.__h.push(f._sb[P]);f._sb=[],f.__h.length&&u.push(f);break e}null!=f.componentWillUpdate&&f.componentWillUpdate(w,f.__s,E),b&&null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(h,v,d)}))}if(f.context=E,f.props=w,f.__P=e,f.__e=!1,C=o.__r,O=0,b){for(f.state=f.__s,f.__d=!1,C&&C(t),a=f.render(f.props,f.state,f.context),$=0;$<f._sb.length;$++)f.__h.push(f._sb[$]);f._sb=[]}else do{f.__d=!1,C&&C(t),a=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++O<25);f.state=f.__s,null!=f.getChildContext&&(r=m(m({},r),f.getChildContext())),b&&!p&&null!=f.getSnapshotBeforeUpdate&&(d=f.getSnapshotBeforeUpdate(h,v)),T(e,g(M=null!=a&&a.type===S&&null==a.key?a.props.children:a)?M:[M],t,n,r,i,s,u,_,c,l),f.base=t.__e,t.__u&=-161,f.__h.length&&u.push(f),y&&(f.__E=f.__=null)}catch(e){if(t.__v=null,c||null!=s){for(t.__u|=c?160:128;_&&8===_.nodeType&&_.nextSibling;)_=_.nextSibling;s[s.indexOf(_)]=null,t.__e=_}else t.__e=n.__e,t.__k=n.__k;o.__e(e,t,n)}else null==s&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=A(n.__e,t,n,r,i,s,u,c,l);(a=o.diffed)&&a(t)}function F(e,t,n){t.__d=void 0;for(var r=0;r<n.length;r++)L(n[r],n[++r],n[++r]);o.__c&&o.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){o.__e(e,t.__v)}}))}function A(e,t,n,i,s,u,_,c,l){var a,f,p,h,d,y,m,b=n.props,x=t.props,S=t.type;if("svg"===S?s="http://www.w3.org/2000/svg":"math"===S?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=u)for(a=0;a<u.length;a++)if((d=u[a])&&"setAttribute"in d==!!S&&(S?d.localName===S:3===d.nodeType)){e=d,u[a]=null;break}if(null==e){if(null===S)return document.createTextNode(x);e=document.createElementNS(s,S,x.is&&x),c&&(o.__m&&o.__m(t,u),c=!1),u=null}if(null===S)b===x||c&&e.data===x||(e.data=x);else{if(u=u&&r.call(e.childNodes),b=n.props||v,!c&&null!=u)for(b={},a=0;a<e.attributes.length;a++)b[(d=e.attributes[a]).name]=d.value;for(a in b)if(d=b[a],"children"==a);else if("dangerouslySetInnerHTML"==a)p=d;else if(!(a in x)){if("value"==a&&"defaultValue"in x||"checked"==a&&"defaultChecked"in x)continue;H(e,a,null,d,s)}for(a in x)d=x[a],"children"==a?h=d:"dangerouslySetInnerHTML"==a?f=d:"value"==a?y=d:"checked"==a?m=d:c&&"function"!=typeof d||b[a]===d||H(e,a,d,b[a],s);if(f)c||p&&(f.__html===p.__html||f.__html===e.innerHTML)||(e.innerHTML=f.__html),t.__k=[];else if(p&&(e.innerHTML=""),T(e,g(h)?h:[h],t,n,i,"foreignObject"===S?"http://www.w3.org/1999/xhtml":s,u,_,u?u[0]:n.__k&&E(n,0),c,l),null!=u)for(a=u.length;a--;)w(u[a]);c||(a="value","progress"===S&&null==y?e.removeAttribute("value"):void 0!==y&&(y!==e[a]||"progress"===S&&!y||"option"===S&&y!==b[a])&&H(e,a,y,b[a],s),a="checked",void 0!==m&&m!==e[a]&&H(e,a,m,b[a],s))}return e}function L(e,t,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==t||(e.__u=e(t))}else e.current=t}catch(e){o.__e(e,n)}}function D(e,t,n){var r,i;if(o.unmount&&o.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||L(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){o.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&D(r[i],t,n||"function"!=typeof e.type);n||w(e.__e),e.__c=e.__=e.__e=e.__d=void 0}function R(e,t,n){return this.constructor(e,n)}function I(e,t,n){var i,s,u,_;o.__&&o.__(e,t),s=(i="function"==typeof n)?null:n&&n.__k||t.__k,u=[],_=[],W(t,e=(!i&&n||t).__k=b(S,null,[e]),s||v,v,t.namespaceURI,!i&&n?[n]:s?null:t.firstChild?r.call(t.childNodes):null,u,!i&&n?n:s?s.__e:t.firstChild,i,_),F(u,e,_)}function V(e,t){I(e,t,V)}function B(e,t,n){var o,i,s,u,_=m({},e.props);for(s in e.type&&e.type.defaultProps&&(u=e.type.defaultProps),t)"key"==s?o=t[s]:"ref"==s?i=t[s]:_[s]=void 0===t[s]&&void 0!==u?u[s]:t[s];return arguments.length>2&&(_.children=arguments.length>3?r.call(arguments,2):n),x(e.type,_,o||e.key,i||e.ref,null)}function z(e,t){var n={__c:t="__cC"+h++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.forEach((function(e){e.__e=!0,C(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}r=d.slice,o={__e:function(e,t,n,r){for(var o,i,s;t=t.__;)if((o=t.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(e)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(e,r||{}),s=o.__d),s)return o.__E=o}catch(t){e=t}throw e}},i=0,s=function(e){return null!=e&&null==e.constructor},k.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=m({},this.state),"function"==typeof e&&(e=e(m({},n),this.props)),e&&m(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),C(this))},k.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),C(this))},k.prototype.render=S,u=[],c="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,l=function(e,t){return e.__v.__b-t.__v.__b},O.__r=0,a=0,f=U(!1),p=U(!0),h=0}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r](i,i.exports,n),i.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var r={};(()=>{n.d(r,{zj:()=>at,SD:()=>ve,V6:()=>de,$K:()=>ye,vT:()=>ft,jb:()=>zt,yT:()=>me,M_:()=>ht,hb:()=>Pe,vJ:()=>ke,ip:()=>Se,Nf:()=>Ee,Kr:()=>Ce,li:()=>w,J0:()=>y,FH:()=>xe,v4:()=>be});var e,t,o,i,s=n(622),u=0,_=[],c=s.fF,l=c.__b,a=c.__r,f=c.diffed,p=c.__c,h=c.unmount,v=c.__;function d(e,n){c.__h&&c.__h(t,e,u||n),u=0;var r=t.__H||(t.__H={__:[],__h:[]});return e>=r.__.length&&r.__.push({}),r.__[e]}function y(n){return u=1,function(n,r,o){var i=d(e++,2);if(i.t=n,!i.__c&&(i.__=[o?o(r):$(void 0,r),function(e){var t=i.__N?i.__N[0]:i.__[0],n=i.t(t,e);t!==n&&(i.__N=[n,i.__[1]],i.__c.setState({}))}],i.__c=t,!t.u)){var s=function(e,t,n){if(!i.__c.__H)return!0;var r=i.__c.__H.__.filter((function(e){return!!e.__c}));if(r.every((function(e){return!e.__N})))return!u||u.call(this,e,t,n);var o=!1;return r.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),!(!o&&i.__c.props===e)&&(!u||u.call(this,e,t,n))};t.u=!0;var u=t.shouldComponentUpdate,_=t.componentWillUpdate;t.componentWillUpdate=function(e,t,n){if(this.__e){var r=u;u=void 0,s(e,t,n),u=r}_&&_.call(this,e,t,n)},t.shouldComponentUpdate=s}return i.__N||i.__}($,n)}function g(n,r){var o=d(e++,3);!c.__s&&T(o.__H,r)&&(o.__=n,o.i=r,t.__H.__h.push(o))}function m(n,r){var o=d(e++,4);!c.__s&&T(o.__H,r)&&(o.__=n,o.i=r,t.__h.push(o))}function w(e){return u=5,b((function(){return{current:e}}),[])}function b(t,n){var r=d(e++,7);return T(r.__H,n)&&(r.__=t(),r.__H=n,r.__h=t),r.__}function x(e,t){return u=8,b((function(){return e}),t)}function S(n){var r=t.context[n.__c],o=d(e++,9);return o.c=n,r?(null==o.__&&(o.__=!0,r.sub(t)),r.props.value):n.__}function k(){for(var e;e=_.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(C),e.__H.__h.forEach(O),e.__H.__h=[]}catch(t){e.__H.__h=[],c.__e(t,e.__v)}}c.__b=function(e){t=null,l&&l(e)},c.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),v&&v(e,t)},c.__r=function(n){a&&a(n),e=0;var r=(t=n.__c).__H;r&&(o===t?(r.__h=[],t.__h=[],r.__.forEach((function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0}))):(r.__h.forEach(C),r.__h.forEach(O),r.__h=[],e=0)),o=t},c.diffed=function(e){f&&f(e);var n=e.__c;n&&n.__H&&(n.__H.__h.length&&(1!==_.push(n)&&i===c.requestAnimationFrame||((i=c.requestAnimationFrame)||P)(k)),n.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.i=void 0}))),o=t=null},c.__c=function(e,t){t.some((function(e){try{e.__h.forEach(C),e.__h=e.__h.filter((function(e){return!e.__||O(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],c.__e(n,e.__v)}})),p&&p(e,t)},c.unmount=function(e){h&&h(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{C(e)}catch(e){t=e}})),n.__H=void 0,t&&c.__e(t,n.__v))};var E="function"==typeof requestAnimationFrame;function P(e){var t,n=function(){clearTimeout(r),E&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);E&&(t=requestAnimationFrame(n))}function C(e){var n=t,r=e.__c;"function"==typeof r&&(e.__c=void 0,r()),t=n}function O(e){var n=t;e.__c=e.__(),t=n}function T(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function $(e,t){return"function"==typeof t?t(e):t}var M=Symbol.for("preact-signals");function N(){if(F>1)F--;else{for(var e,t=!1;void 0!==W;){var n=W;for(W=void 0,A++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&V(n))try{n.c()}catch(n){t||(e=n,t=!0)}n=r}}if(A=0,F--,t)throw e}}function j(e){if(F>0)return e();F++;try{return e()}finally{N()}}var H=void 0;var U,W=void 0,F=0,A=0,L=0;function D(e){if(void 0!==H){var t=e.n;if(void 0===t||t.t!==H)return t={i:0,S:e,p:H.s,n:void 0,t:H,e:void 0,x:void 0,r:t},void 0!==H.s&&(H.s.n=t),H.s=t,e.n=t,32&H.f&&e.S(t),t;if(-1===t.i)return t.i=0,void 0!==t.n&&(t.n.p=t.p,void 0!==t.p&&(t.p.n=t.n),t.p=H.s,t.n=void 0,H.s.n=t,H.s=t),t}}function R(e){this.v=e,this.i=0,this.n=void 0,this.t=void 0}function I(e){return new R(e)}function V(e){for(var t=e.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function B(e){for(var t=e.s;void 0!==t;t=t.n){var n=t.S.n;if(void 0!==n&&(t.r=n),t.S.n=t,t.i=-1,void 0===t.n){e.s=t;break}}}function z(e){for(var t=e.s,n=void 0;void 0!==t;){var r=t.p;-1===t.i?(t.S.U(t),void 0!==r&&(r.n=t.n),void 0!==t.n&&(t.n.p=r)):n=t,t.S.n=t.r,void 0!==t.r&&(t.r=void 0),t=r}e.s=n}function q(e){R.call(this,void 0),this.x=e,this.s=void 0,this.g=L-1,this.f=4}function J(e){return new q(e)}function K(e){var t=e.u;if(e.u=void 0,"function"==typeof t){F++;var n=H;H=void 0;try{t()}catch(t){throw e.f&=-2,e.f|=8,G(e),t}finally{H=n,N()}}}function G(e){for(var t=e.s;void 0!==t;t=t.n)t.S.U(t);e.x=void 0,e.s=void 0,K(e)}function X(e){if(H!==this)throw new Error("Out-of-order effect");z(this),H=e,this.f&=-2,8&this.f&&G(this),N()}function Q(e){this.x=e,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function Y(e){var t=new Q(e);try{t.c()}catch(e){throw t.d(),e}return t.d.bind(t)}function Z(e,t){s.fF[e]=t.bind(null,s.fF[e]||function(){})}function ee(e){U&&U(),U=e&&e.S()}function te(e){var t=this,n=e.data,r=function(e){return b((function(){return I(e)}),[])}(n);r.value=n;var o=b((function(){for(var e=t.__v;e=e.__;)if(e.__c){e.__c.__$f|=4;break}return t.__$u.c=function(){var e;(0,s.zO)(o.peek())||3!==(null==(e=t.base)?void 0:e.nodeType)?(t.__$f|=1,t.setState({})):t.base.data=o.peek()},J((function(){var e=r.value.value;return 0===e?0:!0===e?"":e||""}))}),[]);return o.value}function ne(e,t,n,r){var o=t in e&&void 0===e.ownerSVGElement,i=I(n);return{o:function(e,t){i.value=e,r=t},d:Y((function(){var n=i.value.value;r[t]!==n&&(r[t]=n,o?e[t]=n:n?e.setAttribute(t,n):e.removeAttribute(t))}))}}R.prototype.brand=M,R.prototype.h=function(){return!0},R.prototype.S=function(e){this.t!==e&&void 0===e.e&&(e.x=this.t,void 0!==this.t&&(this.t.e=e),this.t=e)},R.prototype.U=function(e){if(void 0!==this.t){var t=e.e,n=e.x;void 0!==t&&(t.x=n,e.e=void 0),void 0!==n&&(n.e=t,e.x=void 0),e===this.t&&(this.t=n)}},R.prototype.subscribe=function(e){var t=this;return Y((function(){var n=t.value,r=H;H=void 0;try{e(n)}finally{H=r}}))},R.prototype.valueOf=function(){return this.value},R.prototype.toString=function(){return this.value+""},R.prototype.toJSON=function(){return this.value},R.prototype.peek=function(){var e=H;H=void 0;try{return this.value}finally{H=e}},Object.defineProperty(R.prototype,"value",{get:function(){var e=D(this);return void 0!==e&&(e.i=this.i),this.v},set:function(e){if(e!==this.v){if(A>100)throw new Error("Cycle detected");this.v=e,this.i++,L++,F++;try{for(var t=this.t;void 0!==t;t=t.x)t.t.N()}finally{N()}}}}),(q.prototype=new R).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===L)return!0;if(this.g=L,this.f|=1,this.i>0&&!V(this))return this.f&=-2,!0;var e=H;try{B(this),H=this;var t=this.x();(16&this.f||this.v!==t||0===this.i)&&(this.v=t,this.f&=-17,this.i++)}catch(e){this.v=e,this.f|=16,this.i++}return H=e,z(this),this.f&=-2,!0},q.prototype.S=function(e){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}R.prototype.S.call(this,e)},q.prototype.U=function(e){if(void 0!==this.t&&(R.prototype.U.call(this,e),void 0===this.t)){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}},q.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var e=this.t;void 0!==e;e=e.x)e.t.N()}},Object.defineProperty(q.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var e=D(this);if(this.h(),void 0!==e&&(e.i=this.i),16&this.f)throw this.v;return this.v}}),Q.prototype.c=function(){var e=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();"function"==typeof t&&(this.u=t)}finally{e()}},Q.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,K(this),B(this),F++;var e=H;return H=this,X.bind(this,e)},Q.prototype.N=function(){2&this.f||(this.f|=2,this.o=W,W=this)},Q.prototype.d=function(){this.f|=8,1&this.f||G(this)},te.displayName="_st",Object.defineProperties(R.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:te},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),Z("__b",(function(e,t){if("string"==typeof t.type){var n,r=t.props;for(var o in r)if("children"!==o){var i=r[o];i instanceof R&&(n||(t.__np=n={}),n[o]=i,r[o]=i.peek())}}e(t)})),Z("__r",(function(e,t){ee();var n,r=t.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=n=function(e){var t;return Y((function(){t=this})),t.c=function(){r.__$f|=1,r.setState({})},t}())),r,ee(n),e(t)})),Z("__e",(function(e,t,n,r){ee(),void 0,e(t,n,r)})),Z("diffed",(function(e,t){var n;if(ee(),void 0,"string"==typeof t.type&&(n=t.__e)){var r=t.__np,o=t.props;if(r){var i=n.U;if(i)for(var s in i){var u=i[s];void 0===u||s in r||(u.d(),i[s]=void 0)}else n.U=i={};for(var _ in r){var c=i[_],l=r[_];void 0===c?(c=ne(n,_,l,o),i[_]=c):c.o(l,o)}}}e(t)})),Z("unmount",(function(e,t){if("string"==typeof t.type){var n=t.__e;if(n){var r=n.U;if(r)for(var o in n.U=void 0,r){var i=r[o];i&&i.d()}}}else{var s=t.__c;if(s){var u=s.__$u;u&&(s.__$u=void 0,u.d())}}e(t)})),Z("__h",(function(e,t,n,r){(r<3||9===r)&&(t.__$f|=2),e(t,n,r)})),s.uA.prototype.shouldComponentUpdate=function(e,t){var n=this.__$u;if(!(n&&void 0!==n.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(var r in t)return!0;for(var o in e)if("__source"!==o&&e[o]!==this.props[o])return!0;for(var i in this.props)if(!(i in e))return!0;return!1};const re=[],oe=()=>re.slice(-1)[0],ie=e=>{re.push(e)},se=()=>{re.pop()},ue=[],_e=()=>ue.slice(-1)[0],ce=e=>{ue.push(e)},le=()=>{ue.pop()},ae=new WeakMap,fe=()=>{throw new Error("Please use `data-wp-bind` to modify the attributes of an element.")},pe={get(e,t,n){const r=Reflect.get(e,t,n);return r&&"object"==typeof r?he(r):r},set:fe,deleteProperty:fe},he=e=>(ae.has(e)||ae.set(e,new Proxy(e,pe)),ae.get(e)),ve=e=>_e().context[e||oe()],de=()=>{const e=_e();const{ref:t,attributes:n}=e;return Object.freeze({ref:t.current,attributes:he(n)})},ye=e=>_e().serverContext[e||oe()],ge=e=>new Promise((t=>{const n=()=>{clearTimeout(r),window.cancelAnimationFrame(o),setTimeout((()=>{e(),t()}))},r=setTimeout(n,100),o=window.requestAnimationFrame(n)})),me=()=>new Promise((e=>{setTimeout(e,0)}));function we(e){g((()=>{let t=null,n=!1;return t=function(e,t){let n=()=>{};const r=Y((function(){return n=this.c.bind(this),this.x=e,this.c=t,e()}));return{flush:n,dispose:r}}(e,(async()=>{t&&!n&&(n=!0,await ge(t.flush),n=!1)})),t.dispose}),[])}function be(e){const t=_e(),n=oe();return"GeneratorFunction"===e?.constructor?.name?async(...r)=>{const o=e(...r);let i,s;for(;;){ie(n),ce(t);try{s=o.next(i)}finally{le(),se()}try{i=await s.value}catch(e){ie(n),ce(t),o.throw(e)}finally{le(),se()}if(s.done)break}return i}:(...r)=>{ie(n),ce(t);try{return e(...r)}finally{se(),le()}}}function xe(e){we(be(e))}function Se(e){g(be(e),[])}function ke(e,t){g(be(e),t)}function Ee(e,t){m(be(e),t)}function Pe(e,t){return x(be(e),t)}function Ce(e,t){return b(be(e),t)}new Set;const Oe=e=>{0},Te=e=>Boolean(e&&"object"==typeof e&&e.constructor===Object),$e=new WeakMap,Me=new WeakMap,Ne=new WeakMap,je=new Set([Object,Array]),He=(e,t,n)=>{if(!Fe(t))throw Error("This object cannot be proxified.");if(!$e.has(t)){const r=new Proxy(t,n);$e.set(t,r),Me.set(r,t),Ne.set(r,e)}return $e.get(t)},Ue=e=>$e.get(e),We=e=>Ne.get(e),Fe=e=>"object"==typeof e&&null!==e&&(!Ne.has(e)&&je.has(e.constructor)),Ae={};class Le{constructor(e){this.owner=e,this.computedsByScope=new WeakMap}setValue(e){this.update({value:e})}setGetter(e){this.update({get:e})}getComputed(){const e=_e()||Ae;if(this.valueSignal||this.getterSignal||this.update({}),!this.computedsByScope.has(e)){const t=()=>{const e=this.getterSignal?.value;return e?e.call(this.owner):this.valueSignal?.value};ie(We(this.owner)),this.computedsByScope.set(e,J(be(t))),se()}return this.computedsByScope.get(e)}update({get:e,value:t}){this.valueSignal?t===this.valueSignal.peek()&&e===this.getterSignal.peek()||j((()=>{this.valueSignal.value=t,this.getterSignal.value=e})):(this.valueSignal=I(t),this.getterSignal=I(e))}}const De=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter((e=>"symbol"==typeof e))),Re=new WeakMap,Ie=(e,t)=>Re.has(e)&&Re.get(e).has(t),Ve=new WeakSet,Be=(e,t,n)=>{Re.has(e)||Re.set(e,new Map),t="number"==typeof t?`${t}`:t;const r=Re.get(e);if(!r.has(t)){const o=We(e),i=new Le(e);if(r.set(t,i),n){const{get:t,value:r}=n;if(t)i.setGetter(t);else{const t=Ve.has(e);i.setValue(Fe(r)?Ke(o,r,{readOnly:t}):r)}}}return r.get(t)},ze=new WeakMap;let qe=!1;const Je={get(e,t,n){if(qe||!e.hasOwnProperty(t)&&t in e||"symbol"==typeof t&&De.has(t))return Reflect.get(e,t,n);const r=Object.getOwnPropertyDescriptor(e,t),o=Be(n,t,r).getComputed().value;if("function"==typeof o){const e=We(n);return(...t)=>{ie(e);try{return o.call(n,...t)}finally{se()}}}return o},set(e,t,n,r){if(Ve.has(r))return!1;ie(We(r));try{return Reflect.set(e,t,n,r)}finally{se()}},defineProperty(e,t,n){if(Ve.has(Ue(e)))return!1;const r=!(t in e),o=Reflect.defineProperty(e,t,n);if(o){const o=Ue(e),i=Be(o,t),{get:s,value:u}=n;if(s)i.setGetter(s);else{const e=We(o);i.setValue(Fe(u)?Ke(e,u):u)}if(r&&ze.has(e)&&ze.get(e).value++,Array.isArray(e)&&Re.get(o)?.has("length")){Be(o,"length").setValue(e.length)}}return o},deleteProperty(e,t){if(Ve.has(Ue(e)))return!1;const n=Reflect.deleteProperty(e,t);if(n){Be(Ue(e),t).setValue(void 0),ze.has(e)&&ze.get(e).value++}return n},ownKeys:e=>(ze.has(e)||ze.set(e,I(0)),ze._=ze.get(e).value,Reflect.ownKeys(e))},Ke=(e,t,n)=>{const r=He(e,t,Je);return n?.readOnly&&Ve.add(r),r},Ge=(e,t,n=!0)=>{if(!Te(e)||!Te(t))return;let r=!1;for(const o in t){const i=!(o in e);r=r||i;const s=Object.getOwnPropertyDescriptor(t,o),u=Ue(e),_=!!u&&Ie(u,o)&&Be(u,o);if("function"==typeof s.get||"function"==typeof s.set)(n||i)&&(Object.defineProperty(e,o,{...s,configurable:!0,enumerable:!0}),s.get&&_&&_.setGetter(s.get));else if(Te(t[o])){if((i||n&&!Te(e[o]))&&(e[o]={},_)){const t=We(u);_.setValue(Ke(t,e[o]))}Te(e[o])&&Ge(e[o],t[o],n)}else if((n||i)&&(Object.defineProperty(e,o,s),_)){const{value:e}=s,t=We(u);_.setValue(Fe(e)?Ke(t,e):e)}}r&&ze.has(e)&&ze.get(e).value++},Xe=(e,t,n=!0)=>j((()=>{return Ge((r=e,Me.get(r)||e),t,n);var r})),Qe=new WeakSet,Ye={get:(e,t,n)=>{const r=Reflect.get(e,t),o=We(n);if(void 0===r&&Qe.has(n)){const n={};return Reflect.set(e,t,n),Ze(o,n,!1)}if("function"==typeof r){ie(o);const e=be(r);return se(),e}return Te(r)&&Fe(r)?Ze(o,r,!1):r}},Ze=(e,t,n=!0)=>{const r=He(e,t,Ye);return r&&n&&Qe.add(r),r},et=new WeakMap,tt=new WeakMap,nt=new WeakSet,rt=Reflect.getOwnPropertyDescriptor,ot={get:(e,t)=>{const n=tt.get(e),r=e[t];return t in e?r:n[t]},set:(e,t,n)=>{const r=tt.get(e);return(t in e||!(t in r)?e:r)[t]=n,!0},ownKeys:e=>[...new Set([...Object.keys(tt.get(e)),...Object.keys(e)])],getOwnPropertyDescriptor:(e,t)=>rt(e,t)||rt(tt.get(e),t)},it=(e,t={})=>{if(nt.has(e))throw Error("This object cannot be proxified.");if(tt.set(e,t),!et.has(e)){const t=new Proxy(e,ot);et.set(e,t),nt.add(t)}return et.get(e)},st=new Map,ut=new Map,_t=new Map,ct=new Map,lt=new Map,at=e=>ct.get(e||oe())||{},ft=e=>{const t=e||oe();return lt.has(t)||lt.set(t,Ke(t,{},{readOnly:!0})),lt.get(t)},pt="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";function ht(e,{state:t={},...n}={},{lock:r=!1}={}){if(st.has(e)){if(r===pt||_t.has(e)){const t=_t.get(e);if(!(r===pt||!0!==r&&r===t))throw t?Error("Cannot unlock a private store with an invalid lock code"):Error("Cannot lock a public store")}else _t.set(e,r);const o=ut.get(e);Xe(o,n),Xe(o.state,t)}else{r!==pt&&_t.set(e,r);const o={state:Ke(e,Te(t)?t:{}),...n},i=Ze(e,o);ut.set(e,o),st.set(e,i)}return st.get(e)}const vt=(e=document)=>{var t;const n=null!==(t=e.getElementById("wp-script-module-data-@wordpress/interactivity"))&&void 0!==t?t:e.getElementById("wp-interactivity-data");if(n?.textContent)try{return JSON.parse(n.textContent)}catch{}return{}},dt=e=>{Te(e?.state)&&Object.entries(e.state).forEach((([e,t])=>{const n=ht(e,{},{lock:pt});Xe(n.state,t,!1),Xe(ft(e),t)})),Te(e?.config)&&Object.entries(e.config).forEach((([e,t])=>{ct.set(e,t)}))},yt=vt();function gt(e){return null!==e.suffix}function mt(e){return null===e.suffix}dt(yt);const wt=(0,s.q6)({client:{},server:{}}),bt={},xt={},St=(e,t,{priority:n=10}={})=>{bt[e]=t,xt[e]=n},kt=({scope:e})=>(t,...n)=>{let{value:r,namespace:o}=t;if("string"!=typeof r)throw new Error("The `value` prop should be a string path");const i="!"===r[0]&&!!(r=r.slice(1));ce(e);const s=((e,t)=>{if(!t)return void Oe();let n=st.get(t);void 0===n&&(n=ht(t,void 0,{lock:pt}));const r={...n,context:_e().context[t]};try{return e.split(".").reduce(((e,t)=>e[t]),r)}catch(e){}})(r,o),u="function"==typeof s?s(...n):s;return le(),i?!u:u},Et=({directives:e,priorityLevels:[t,...n],element:r,originalProps:o,previousScope:i})=>{const u=w({}).current;u.evaluate=x(kt({scope:u}),[]);const{client:_,server:c}=S(wt);u.context=_,u.serverContext=c,u.ref=i?.ref||w(null),r=(0,s.Ob)(r,{ref:u.ref}),u.attributes=r.props;const l=n.length>0?(0,s.h)(Et,{directives:e,priorityLevels:n,element:r,originalProps:o,previousScope:u}):r,a={...o,children:l},f={directives:e,props:a,element:r,context:wt,evaluate:u.evaluate};ce(u);for(const e of t){const t=bt[e]?.(f);void 0!==t&&(a.children=t)}return le(),a.children},Pt=s.fF.vnode;function Ct(e){return Te(e)?Object.fromEntries(Object.entries(e).map((([e,t])=>[e,Ct(t)]))):Array.isArray(e)?e.map((e=>Ct(e))):e}s.fF.vnode=e=>{if(e.props.__directives){const t=e.props,n=t.__directives;n.key&&(e.key=n.key.find(mt).value),delete t.__directives;const r=(e=>{const t=Object.keys(e).reduce(((e,t)=>{if(bt[t]){const n=xt[t];(e[n]=e[n]||[]).push(t)}return e}),{});return Object.entries(t).sort((([e],[t])=>parseInt(e)-parseInt(t))).map((([,e])=>e))})(n);r.length>0&&(e.props={directives:n,priorityLevels:r,originalProps:t,type:e.type,element:(0,s.h)(e.type,t),top:!0},e.type=Et)}Pt&&Pt(e)};const Ot=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Tt=/\/\*[^]*?\*\/|  +/g,$t=/\n+/g,Mt=e=>({directives:t,evaluate:n})=>{t[`on-${e}`].filter(gt).forEach((t=>{const r=t.suffix.split("--",1)[0];Se((()=>{const o=e=>n(t,e),i="window"===e?window:document;return i.addEventListener(r,o),()=>i.removeEventListener(r,o)}))}))},Nt=e=>({directives:t,evaluate:n})=>{t[`on-async-${e}`].filter(gt).forEach((t=>{const r=t.suffix.split("--",1)[0];Se((()=>{const o=async e=>{await me(),n(t,e)},i="window"===e?window:document;return i.addEventListener(r,o,{passive:!0}),()=>i.removeEventListener(r,o)}))}))},jt="wp",Ht=`data-${jt}-ignore`,Ut=`data-${jt}-interactive`,Wt=`data-${jt}-`,Ft=[],At=new RegExp(`^data-${jt}-([a-z0-9]+(?:-[a-z0-9]+)*)(?:--([a-z0-9_-]+))?$`,"i"),Lt=/^([\w_\/-]+)::(.+)$/,Dt=new WeakSet;function Rt(e){const t=document.createTreeWalker(e,205);return function e(n){const{nodeType:r}=n;if(3===r)return[n.data];if(4===r){var o;const e=t.nextSibling();return n.replaceWith(new window.Text(null!==(o=n.nodeValue)&&void 0!==o?o:"")),[n.nodeValue,e]}if(8===r||7===r){const e=t.nextSibling();return n.remove(),[null,e]}const i=n,{attributes:u}=i,_=i.localName,c={},l=[],a=[];let f=!1,p=!1;for(let e=0;e<u.length;e++){const t=u[e].name,n=u[e].value;if(t[Wt.length]&&t.slice(0,Wt.length)===Wt)if(t===Ht)f=!0;else{var h,v;const e=Lt.exec(n),r=null!==(h=e?.[1])&&void 0!==h?h:null;let o=null!==(v=e?.[2])&&void 0!==v?v:n;try{const e=JSON.parse(o);d=e,o=Boolean(d&&"object"==typeof d&&d.constructor===Object)?e:o}catch{}if(t===Ut){p=!0;const e="string"==typeof o?o:"string"==typeof o?.namespace?o.namespace:null;Ft.push(e)}else a.push([t,r,o])}else if("ref"===t)continue;c[t]=n}var d;if(f&&!p)return[(0,s.h)(_,{...c,innerHTML:i.innerHTML,__directives:{ignore:!0}})];if(p&&Dt.add(i),a.length&&(c.__directives=a.reduce(((e,[t,n,r])=>{const o=At.exec(t);if(null===o)return Oe(),e;const i=o[1]||"",s=o[2]||null;var u;return e[i]=e[i]||[],e[i].push({namespace:null!=n?n:null!==(u=Ft[Ft.length-1])&&void 0!==u?u:null,value:r,suffix:s}),e}),{})),"template"===_)c.content=[...i.content.childNodes].map((e=>Rt(e)));else{let n=t.firstChild();if(n){for(;n;){const[r,o]=e(n);r&&l.push(r),n=o||t.nextSibling()}t.parentNode()}}return p&&Ft.pop(),[(0,s.h)(_,c,l)]}(t.currentNode)}const It=new WeakMap,Vt=e=>{if(!e.parentElement)throw Error("The passed region should be an element with a parent.");return It.has(e)||It.set(e,((e,t)=>{const n=(t=[].concat(t))[t.length-1].nextSibling;function r(t,r){e.insertBefore(t,r||n)}return e.__k={nodeType:1,parentNode:e,firstChild:t[0],childNodes:t,insertBefore:r,appendChild:r,removeChild(t){e.removeChild(t)}}})(e.parentElement,e)),It.get(e)},Bt=new WeakMap,zt=e=>{if("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."===e)return{directivePrefix:jt,getRegionRootFragment:Vt,initialVdom:Bt,toVdom:Rt,directive:St,getNamespace:oe,h:s.h,cloneElement:s.Ob,render:s.XX,proxifyState:Ke,parseServerData:vt,populateServerData:dt,batch:j};throw new Error("Forbidden access.")};St("context",(({directives:{context:e},props:{children:t},context:n})=>{const{Provider:r}=n,o=e.find(mt),{client:i,server:u}=S(n),_=o.namespace,c=w(Ke(_,{})),l=w(Ke(_,{},{readOnly:!0})),a=b((()=>{const e={client:{...i},server:{...u}};if(o){const{namespace:t,value:n}=o;Te(n)||Oe(),Xe(c.current,Ct(n),!1),Xe(l.current,Ct(n)),e.client[t]=it(c.current,i[t]),e.server[t]=it(l.current,u[t])}return e}),[o,i,u]);return(0,s.h)(r,{value:a},t)}),{priority:5}),St("watch",(({directives:{watch:e},evaluate:t})=>{e.forEach((e=>{xe((()=>t(e)))}))})),St("init",(({directives:{init:e},evaluate:t})=>{e.forEach((e=>{Se((()=>t(e)))}))})),St("on",(({directives:{on:e},element:t,evaluate:n})=>{const r=new Map;e.filter(gt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{e.forEach((e=>{o&&o(t),n(e,t)}))}}))})),St("on-async",(({directives:{"on-async":e},element:t,evaluate:n})=>{const r=new Map;e.filter(gt).forEach((e=>{const t=e.suffix.split("--")[0];r.has(t)||r.set(t,new Set),r.get(t).add(e)})),r.forEach(((e,r)=>{const o=t.props[`on${r}`];t.props[`on${r}`]=t=>{o&&o(t),e.forEach((async e=>{await me(),n(e,t)}))}}))})),St("on-window",Mt("window")),St("on-document",Mt("document")),St("on-async-window",Nt("window")),St("on-async-document",Nt("document")),St("class",(({directives:{class:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e),i=t.props.class||"",s=new RegExp(`(^|\\s)${r}(\\s|$)`,"g");o?s.test(i)||(t.props.class=i?`${i} ${r}`:r):t.props.class=i.replace(s," ").trim(),Se((()=>{o?t.ref.current.classList.add(r):t.ref.current.classList.remove(r)}))}))})),St("style",(({directives:{style:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e);t.props.style=t.props.style||{},"string"==typeof t.props.style&&(t.props.style=(e=>{const t=[{}];let n,r;for(;n=Ot.exec(e.replace(Tt,""));)n[4]?t.shift():n[3]?(r=n[3].replace($t," ").trim(),t.unshift(t[0][r]=t[0][r]||{})):t[0][n[1]]=n[2].replace($t," ").trim();return t[0]})(t.props.style)),o?t.props.style[r]=o:delete t.props.style[r],Se((()=>{o?t.ref.current.style[r]=o:t.ref.current.style.removeProperty(r)}))}))})),St("bind",(({directives:{bind:e},element:t,evaluate:n})=>{e.filter(gt).forEach((e=>{const r=e.suffix,o=n(e);t.props[r]=o,Se((()=>{const e=t.ref.current;if("style"!==r){if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in e)try{return void(e[r]=null==o?"":o)}catch(e){}null==o||!1===o&&"-"!==r[4]?e.removeAttribute(r):e.setAttribute(r,o)}else"string"==typeof o&&(e.style.cssText=o)}))}))})),St("ignore",(({element:{type:e,props:{innerHTML:t,...n}}})=>{const r=b((()=>t),[]);return(0,s.h)(e,{dangerouslySetInnerHTML:{__html:r},...n})})),St("text",(({directives:{text:e},element:t,evaluate:n})=>{const r=e.find(mt);if(r)try{const e=n(r);t.props.children="object"==typeof e?null:e.toString()}catch(e){t.props.children=null}else t.props.children=null})),St("run",(({directives:{run:e},evaluate:t})=>{e.forEach((e=>t(e)))})),St("each",(({directives:{each:e,"each-key":t},context:n,element:r,evaluate:o})=>{if("template"!==r.type)return;const{Provider:i}=n,u=S(n),[_]=e,{namespace:c}=_,l=o(_),a=gt(_)?_.suffix.replace(/^-+|-+$/g,"").toLowerCase().replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()})):"item";return l.map((e=>{const n=it(Ke(c,{}),u.client[c]),o={client:{...u.client,[c]:n},server:{...u.server}};o.client[c][a]=e;const _={..._e(),context:o.client,serverContext:o.server},l=t?kt({scope:_})(t[0]):e;return(0,s.h)(i,{value:o,key:l},r.props.content)}))}),{priority:20}),St("each-child",(()=>null),{priority:1}),(async()=>{const e=document.querySelectorAll(`[data-${jt}-interactive]`);for(const t of e)if(!Dt.has(t)){await me();const e=Vt(t),n=Rt(t);Bt.set(t,n),await me(),(0,s.Qv)(n,e)}})()})();var o=r.zj,i=r.SD,s=r.V6,u=r.$K,_=r.vT,c=r.jb,l=r.yT,a=r.M_,f=r.hb,p=r.vJ,h=r.ip,v=r.Nf,d=r.Kr,y=r.li,g=r.J0,m=r.FH,w=r.v4;export{o as getConfig,i as getContext,s as getElement,u as getServerContext,_ as getServerState,c as privateApis,l as splitTask,a as store,f as useCallback,p as useEffect,h as useInit,v as useLayoutEffect,d as useMemo,y as useRef,g as useState,m as useWatch,w as withScope};
/*! This file is auto-generated */
(()=>{"use strict";var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{privateApis:()=>j});const n=window.wp.element;function r(){return r=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},r.apply(this,arguments)}var o;!function(t){t.Pop="POP",t.Push="PUSH",t.Replace="REPLACE"}(o||(o={}));var a=function(t){return t};var i="beforeunload",u="popstate";function c(t){t.preventDefault(),t.returnValue=""}function s(){var t=[];return{get length(){return t.length},push:function(e){return t.push(e),function(){t=t.filter((function(t){return t!==e}))}},call:function(e){t.forEach((function(t){return t&&t(e)}))}}}function l(){return Math.random().toString(36).substr(2,8)}function f(t){var e=t.pathname,n=void 0===e?"/":e,r=t.search,o=void 0===r?"":r,a=t.hash,i=void 0===a?"":a;return o&&"?"!==o&&(n+="?"===o.charAt(0)?o:"?"+o),i&&"#"!==i&&(n+="#"===i.charAt(0)?i:"#"+i),n}function h(t){var e={};if(t){var n=t.indexOf("#");n>=0&&(e.hash=t.substr(n),t=t.substr(0,n));var r=t.indexOf("?");r>=0&&(e.search=t.substr(r),t=t.substr(0,r)),t&&(e.pathname=t)}return e}const p=window.wp.url,d=function(t){void 0===t&&(t={});var e=t.window,n=void 0===e?document.defaultView:e,p=n.history;function d(){var t=n.location,e=t.pathname,r=t.search,o=t.hash,i=p.state||{};return[i.idx,a({pathname:e,search:r,hash:o,state:i.usr||null,key:i.key||"default"})]}var v=null;n.addEventListener(u,(function(){if(v)P.call(v),v=null;else{var t=o.Pop,e=d(),n=e[0],r=e[1];if(P.length){if(null!=n){var a=y-n;a&&(v={action:t,location:r,retry:function(){L(-1*a)}},L(a))}}else j(t)}}));var w=o.Pop,g=d(),y=g[0],m=g[1],b=s(),P=s();function S(t){return"string"==typeof t?t:f(t)}function O(t,e){return void 0===e&&(e=null),a(r({pathname:m.pathname,hash:"",search:""},"string"==typeof t?h(t):t,{state:e,key:l()}))}function x(t,e){return[{usr:t.state,key:t.key,idx:e},S(t)]}function k(t,e,n){return!P.length||(P.call({action:t,location:e,retry:n}),!1)}function j(t){w=t;var e=d();y=e[0],m=e[1],b.call({action:w,location:m})}function L(t){p.go(t)}null==y&&(y=0,p.replaceState(r({},p.state,{idx:y}),""));var _={get action(){return w},get location(){return m},createHref:S,push:function t(e,r){var a=o.Push,i=O(e,r);if(k(a,i,(function(){t(e,r)}))){var u=x(i,y+1),c=u[0],s=u[1];try{p.pushState(c,"",s)}catch(t){n.location.assign(s)}j(a)}},replace:function t(e,n){var r=o.Replace,a=O(e,n);if(k(r,a,(function(){t(e,n)}))){var i=x(a,y),u=i[0],c=i[1];p.replaceState(u,"",c),j(r)}},go:L,back:function(){L(-1)},forward:function(){L(1)},listen:function(t){return b.push(t)},block:function(t){var e=P.push(t);return 1===P.length&&n.addEventListener(i,c),function(){e(),P.length||n.removeEventListener(i,c)}}};return _}(),v=d.push,w=d.replace;function g(t){if(t.hasOwnProperty("wp_theme_preview"))return t;const e=new URLSearchParams(d.location.search).get("wp_theme_preview");return null===e?t:{...t,wp_theme_preview:e}}const y=new WeakMap;d.push=function(t,e){const n=(0,p.buildQueryString)(g(t));return v.call(d,{search:n},e)},d.replace=function(t,e){const n=(0,p.buildQueryString)(g(t));return w.call(d,{search:n},e)},d.getLocationWithParams=function(){const t=d.location;let e=y.get(t);return e||(e={...t,params:Object.fromEntries(new URLSearchParams(t.search))},y.set(t,e)),e};const m=d,b=window.ReactJSXRuntime,P=(0,n.createContext)(),S=(0,n.createContext)();const O=window.wp.privateApis,{lock:x,unlock:k}=(0,O.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/router"),j={};x(j,{useHistory:function(){return(0,n.useContext)(S)},useLocation:function(){return(0,n.useContext)(P)},RouterProvider:function({children:t}){const e=(0,n.useSyncExternalStore)(m.listen,m.getLocationWithParams,m.getLocationWithParams);return(0,b.jsx)(S.Provider,{value:m,children:(0,b.jsx)(P.Provider,{value:e,children:t})})}}),(window.wp=window.wp||{}).router=e})();
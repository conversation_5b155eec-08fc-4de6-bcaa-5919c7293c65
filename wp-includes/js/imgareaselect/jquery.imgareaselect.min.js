!function(Se){var ze=Math.abs,ke=Math.max,Ce=Math.min,Ae=Math.floor;function We(){return Se("<div/>")}Se.imgAreaSelect=function(o,s){var T,L,r,c,d,a,t,j,D,R,X,i,Y,$,q,B,n,Q,u,l,h,f,F,m,p,y,g,G,v=Se(o),b=We(),x=We(),w=We().add(We()).add(We()).add(We()),S=We().add(We()).add(We()).add(We()),z=Se([]),k={left:0,top:0},C={left:0,top:0},A=0,J="absolute",W={x1:0,y1:0,x2:0,y2:0,width:0,height:0},U=document.documentElement,V=navigator.userAgent;function I(e){return e+k.left-C.left}function K(e){return e+k.top-C.top}function P(e){return e-k.left+C.left}function N(e){return e-k.top+C.top}function Z(e){return ke(e.pageX||0,ee(e).x)-C.left}function _(e){return ke(e.pageY||0,ee(e).y)-C.top}function ee(e){e=e.originalEvent||{};return e.touches&&e.touches.length?{x:e.touches[0].pageX,y:e.touches[0].pageY}:{x:0,y:0}}function H(e){var t=e||R,e=e||X;return{x1:Ae(W.x1*t),y1:Ae(W.y1*e),x2:Ae(W.x2*t),y2:Ae(W.y2*e),width:Ae(W.x2*t)-Ae(W.x1*t),height:Ae(W.y2*e)-Ae(W.y1*e)}}function te(e,t,o,i,n){var s=n||R,n=n||X;(W={x1:Ae(e/s||0),y1:Ae(t/n||0),x2:Ae(o/s||0),y2:Ae(i/n||0)}).width=W.x2-W.x1,W.height=W.y2-W.y1}function M(){T&&v.width()&&(k={left:Ae(v.offset().left),top:Ae(v.offset().top)},d=v.innerWidth(),a=v.innerHeight(),k.top+=v.outerHeight()-a>>1,k.left+=v.outerWidth()-d>>1,Y=Ae(s.minWidth/R)||0,$=Ae(s.minHeight/X)||0,q=Ae(Ce(s.maxWidth/R||1<<24,d)),B=Ae(Ce(s.maxHeight/X||1<<24,a)),"1.3.2"!=Se().jquery||"fixed"!=J||U.getBoundingClientRect||(k.top+=ke(document.body.scrollTop,U.scrollTop),k.left+=ke(document.body.scrollLeft,U.scrollLeft)),C=/absolute|relative/.test(t.css("position"))?{left:Ae(t.offset().left)-t.scrollLeft(),top:Ae(t.offset().top)-t.scrollTop()}:"fixed"==J?{left:Se(document).scrollLeft(),top:Se(document).scrollTop()}:{left:0,top:0},r=I(0),c=K(0),W.x2>d||W.y2>a)&&ae()}function oe(e){if(Q){switch(b.css({left:I(W.x1),top:K(W.y1)}).add(x).width(y=W.width).height(g=W.height),x.add(w).add(z).css({left:0,top:0}),w.width(ke(y-w.outerWidth()+w.innerWidth(),0)).height(ke(g-w.outerHeight()+w.innerHeight(),0)),Se(S[0]).css({left:r,top:c,width:W.x1,height:a}),Se(S[1]).css({left:r+W.x1,top:c,width:y,height:W.y1}),Se(S[2]).css({left:r+W.x2,top:c,width:d-W.x2,height:a}),Se(S[3]).css({left:r+W.x1,top:c+W.y2,width:y,height:a-W.y2}),y-=z.outerWidth(),g-=z.outerHeight(),z.length){case 8:Se(z[4]).css({left:y>>1}),Se(z[5]).css({left:y,top:g>>1}),Se(z[6]).css({left:y>>1,top:g}),Se(z[7]).css({top:g>>1});case 4:z.slice(1,3).css({left:y}),z.slice(2,4).css({top:g})}!1!==e&&(Se.imgAreaSelect.onKeyPress!=ve&&Se(document).off(Se.imgAreaSelect.keyPress,Se.imgAreaSelect.onKeyPress),s.keys)&&Se(document).on(Se.imgAreaSelect.keyPress,function(){Se.imgAreaSelect.onKeyPress=ve}),O&&w.outerWidth()-w.innerWidth()==2&&(w.css("margin",0),setTimeout(function(){w.css("margin","auto")},0))}}function ie(e){M(),oe(e),ne()}function ne(){u=I(W.x1),l=K(W.y1),h=I(W.x2),f=K(W.y2)}function se(e,t){s.fadeSpeed?e.fadeOut(s.fadeSpeed,t):e.hide()}function E(e){var t=P(Z(e))-W.x1,e=N(_(e))-W.y1;G||(M(),G=!0,b.one("mouseout",function(){G=!1})),i="",s.resizable&&(e<=s.resizeMargin?i="n":e>=W.height-s.resizeMargin&&(i="s"),t<=s.resizeMargin?i+="w":t>=W.width-s.resizeMargin&&(i+="e")),b.css("cursor",i?i+"-resize":s.movable?"move":""),L&&L.toggle()}function re(e){Se("body").css("cursor",""),!s.autoHide&&W.width*W.height!=0||se(b.add(S),function(){Se(this).hide()}),Se(document).off("mousemove touchmove",ue),b.on("mousemove touchmove",E),s.onSelectEnd(o,H())}function ce(e){return"mousedown"==e.type&&1!=e.which||(E(e),M(),i?(Se("body").css("cursor",i+"-resize"),u=I(W[/w/.test(i)?"x2":"x1"]),l=K(W[/n/.test(i)?"y2":"y1"]),Se(document).on("mousemove touchmove",ue).one("mouseup touchend",re),b.off("mousemove touchmove",E)):s.movable?(j=r+W.x1-Z(e),D=c+W.y1-_(e),b.off("mousemove touchmove",E),Se(document).on("mousemove touchmove",he).one("mouseup touchend",function(){s.onSelectEnd(o,H()),Se(document).off("mousemove touchmove",he),b.on("mousemove touchmove",E)})):v.mousedown(e)),!1}function de(e){n&&(e?(h=ke(r,Ce(r+d,u+ze(f-l)*n*(u<h||-1))),f=Ae(ke(c,Ce(c+a,l+ze(h-u)/n*(l<f||-1)))),h=Ae(h)):(f=ke(c,Ce(c+a,l+ze(h-u)/n*(l<f||-1))),h=Ae(ke(r,Ce(r+d,u+ze(f-l)*n*(u<h||-1)))),f=Ae(f)))}function ae(){null!=u&&null!=h&&null!=l&&null!=f||ne(),u=Ce(u,r+d),l=Ce(l,c+a),ze(h-u)<Y&&((h=u-Y*(h<u||-1))<r?u=r+Y:r+d<h&&(u=r+d-Y)),ze(f-l)<$&&((f=l-$*(f<l||-1))<c?l=c+$:c+a<f&&(l=c+a-$)),h=ke(r,Ce(h,r+d)),f=ke(c,Ce(f,c+a)),de(ze(h-u)<ze(f-l)*n),ze(h-u)>q&&(h=u-q*(h<u||-1),de()),ze(f-l)>B&&(f=l-B*(f<l||-1),de(!0)),W={x1:P(Ce(u,h)),x2:P(ke(u,h)),y1:N(Ce(l,f)),y2:N(ke(l,f)),width:ze(h-u),height:ze(f-l)},oe(),s.onSelectChange(o,H())}function ue(e){return h=/w|e|^$/.test(i)||n?Z(e):I(W.x2),f=/n|s|^$/.test(i)||n?_(e):K(W.y2),ae(),!1}function le(e,t){h=(u=e)+W.width,f=(l=t)+W.height,Se.extend(W,{x1:P(u),y1:N(l),x2:P(h),y2:N(f)}),oe(),s.onSelectChange(o,H())}function he(e){return u=ke(r,Ce(j+Z(e),r+d-W.width)),l=ke(c,Ce(D+_(e),c+a-W.height)),le(u,l),e.preventDefault(),!1}function fe(){Se(document).off("mousemove touchmove",fe),M(),h=u,f=l,ae(),i="",S.is(":visible")||b.add(S).hide().fadeIn(s.fadeSpeed||0),Q=!0,Se(document).off("mouseup touchend",me).on("mousemove touchmove",ue).one("mouseup touchend",re),b.off("mousemove touchmove",E),s.onSelectStart(o,H())}function me(){Se(document).off("mousemove touchmove",fe).off("mouseup touchend",me),se(b.add(S)),te(P(u),N(l),P(u),N(l)),this instanceof Se.imgAreaSelect||(s.onSelectChange(o,H()),s.onSelectEnd(o,H()))}function pe(e){return 1<e.which||S.is(":animated")||(M(),j=u=Z(e),D=l=_(e),Se(document).on({"mousemove touchmove":fe,"mouseup touchend":me})),!1}function ye(){ie(!1)}function ge(){T=!0,xe(s=Se.extend({classPrefix:"imgareaselect",movable:!0,parent:"body",resizable:!0,resizeMargin:10,onInit:function(){},onSelectStart:function(){},onSelectChange:function(){},onSelectEnd:function(){}},s)),b.add(S).css({visibility:""}),s.show&&(Q=!0,M(),oe(),b.add(S).hide().fadeIn(s.fadeSpeed||0)),setTimeout(function(){s.onInit(o,H())},0)}var ve=function(e){var t,o=s.keys,i=e.keyCode,n=isNaN(o.alt)||!e.altKey&&!e.originalEvent.altKey?!isNaN(o.ctrl)&&e.ctrlKey?o.ctrl:!isNaN(o.shift)&&e.shiftKey?o.shift:isNaN(o.arrows)?10:o.arrows:o.alt;if("resize"==o.arrows||"resize"==o.shift&&e.shiftKey||"resize"==o.ctrl&&e.ctrlKey||"resize"==o.alt&&(e.altKey||e.originalEvent.altKey)){switch(i){case 37:n=-n;case 39:t=ke(u,h),u=Ce(u,h),h=ke(t+n,u),de();break;case 38:n=-n;case 40:t=ke(l,f),l=Ce(l,f),f=ke(t+n,l),de(!0);break;default:return}ae()}else switch(u=Ce(u,h),l=Ce(l,f),i){case 37:le(ke(u-n,r),l);break;case 38:le(u,ke(l-n,c));break;case 39:le(u+Ce(n,d-P(h)),l);break;case 40:le(u,l+Ce(n,a-N(f)));break;default:return}return!1};function be(e,t){for(var o in t)void 0!==s[o]&&e.css(t[o],s[o])}function xe(e){if(e.parent&&(t=Se(e.parent)).append(b.add(S)),Se.extend(s,e),M(),null!=e.handles){for(z.remove(),z=Se([]),m=e.handles?"corners"==e.handles?4:8:0;m--;)z=z.add(We());z.addClass(s.classPrefix+"-handle").css({position:"absolute",fontSize:"0",zIndex:A+1||1}),0<=!parseInt(z.css("width"))&&z.width(10).height(10),(p=s.borderWidth)&&z.css({borderWidth:p,borderStyle:"solid"}),be(z,{borderColor1:"border-color",borderColor2:"background-color",borderOpacity:"opacity"})}for(R=s.imageWidth/d||1,X=s.imageHeight/a||1,null!=e.x1&&(te(e.x1,e.y1,e.x2,e.y2),e.show=!e.hide),e.keys&&(s.keys=Se.extend({shift:1,ctrl:"resize"},e.keys)),S.addClass(s.classPrefix+"-outer"),x.addClass(s.classPrefix+"-selection"),m=0;m++<4;)Se(w[m-1]).addClass(s.classPrefix+"-border"+m);be(x,{selectionColor:"background-color",selectionOpacity:"opacity"}),be(w,{borderOpacity:"opacity",borderWidth:"border-width"}),be(S,{outerColor:"background-color",outerOpacity:"opacity"}),(p=s.borderColor1)&&Se(w[0]).css({borderStyle:"solid",borderColor:p}),(p=s.borderColor2)&&Se(w[1]).css({borderStyle:"dashed",borderColor:p}),b.append(x.add(w).add(L)).append(z),O&&((p=(S.css("filter")||"").match(/opacity=(\d+)/))&&S.css("opacity",p[1]/100),p=(w.css("filter")||"").match(/opacity=(\d+)/))&&w.css("opacity",p[1]/100),e.hide?se(b.add(S)):e.show&&T&&(Q=!0,b.add(S).fadeIn(s.fadeSpeed||0),ie()),n=(F=(s.aspectRatio||"").split(/:/))[0]/F[1],v.add(S).off("mousedown",pe),s.disable||!1===s.enable?(b.off({"mousemove touchmove":E,"mousedown touchstart":ce}),Se(window).off("resize",ye)):(!s.enable&&!1!==s.disable||((s.resizable||s.movable)&&b.on({"mousemove touchmove":E,"mousedown touchstart":ce}),Se(window).on("resize",ye)),s.persistent||v.add(S).on("mousedown touchstart",pe)),s.enable=s.disable=void 0}this.remove=function(){xe({disable:!0}),b.add(S).remove()},this.getOptions=function(){return s},this.setOptions=xe,this.getSelection=H,this.setSelection=te,this.cancelSelection=me,this.update=ie;for(var O=(/msie ([\w.]+)/i.exec(V)||[])[1],we=/opera/i.test(V),V=/webkit/i.test(V)&&!/chrome/i.test(V),e=v;e.length;)A=ke(A,isNaN(e.css("z-index"))?A:e.css("z-index")),"fixed"==e.css("position")&&(J="fixed"),e=e.parent(":not(body)");A=s.zIndex||A,O&&v.attr("unselectable","on"),Se.imgAreaSelect.keyPress=O||V?"keydown":"keypress",we&&(L=We().css({width:"100%",height:"100%",position:"absolute",zIndex:A+2||2})),b.add(S).css({visibility:"hidden",position:J,overflow:"hidden",zIndex:A||"0"}),b.css({zIndex:A+2||2}),x.add(w).css({position:"absolute",fontSize:"0"}),o.complete||"complete"==o.readyState||!v.is("img")?ge():v.one("load",ge),!T&&O&&7<=O&&(o.src=o.src)},Se.fn.imgAreaSelect=function(e){return e=e||{},this.each(function(){Se(this).data("imgAreaSelect")?e.remove?(Se(this).data("imgAreaSelect").remove(),Se(this).removeData("imgAreaSelect")):Se(this).data("imgAreaSelect").setOptions(e):e.remove||(void 0===e.enable&&void 0===e.disable&&(e.enable=!0),Se(this).data("imgAreaSelect",new Se.imgAreaSelect(this,e)))}),e.instance?Se(this).data("imgAreaSelect"):this}}(jQuery);
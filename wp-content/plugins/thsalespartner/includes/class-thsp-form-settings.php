<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class THSP_Form_Settings {

    public function __construct() {
        add_action( 'admin_menu', array( $this, 'add_submenu_page' ), 12 );
        add_action( 'admin_init', array( $this, 'form_settings_init' ) );
    }

    /**
     * Add Formular Oferta submenu under Sales Partner
     */
    public function add_submenu_page() {
        add_submenu_page(
            'thsalespartner-main',
            __( 'Form Settings', 'thsalespartner' ),
            __( 'Form Settings', 'thsalespartner' ),
            'manage_options',
            'thsp-formular-oferta',
            array( $this, 'formular_oferta_page_html' )
        );
    }

    /**
     * Initialize form settings
     */
    public function form_settings_init() {
        // Register settings group
        register_setting( 'thsp_form_settings_group', 'thsp_form_settings', array(
            'sanitize_callback' => array( $this, 'sanitize_form_settings' ),
            'default'           => array(),
        ) );

        // Add settings section
        add_settings_section(
            'thsp_form_general_section',
            __( 'Form Fields Settings', 'thsalespartner' ),
            array( $this, 'form_settings_section_callback' ),
            'thsp-formular-oferta'
        );

        // Add settings fields for existing options
        // Display "Locatie" Field
        add_settings_field(
            'thsp_display_location_field',
            __( 'Display "Locatie" Field', 'thsalespartner' ),
            array( $this, 'display_location_field_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Display "Informatii Aditionale" Field
        add_settings_field(
            'thsp_display_message_field',
            __( 'Display "Informatii Aditionale" Field', 'thsalespartner' ),
            array( $this, 'display_message_field_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Display "METODA CONTACT PREFERATA" Field
        add_settings_field(
            'thsp_display_contact_method',
            __( 'Display "METODA CONTACT PREFERATA*" Field', 'thsalespartner' ),
            array( $this, 'display_contact_method_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Checkbox1 Label
        add_settings_field(
            'thsp_checkbox1_label',
            __( 'Checkbox1 Label (Terms and Conditions)', 'thsalespartner' ),
            array( $this, 'checkbox1_label_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Checkbox2 Label
        add_settings_field(
            'thsp_checkbox2_label',
            __( 'Checkbox2 Label (Other Conditions)', 'thsalespartner' ),
            array( $this, 'checkbox2_label_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Manage Locations
        add_settings_field(
            'thsp_manage_locations',
            __( 'Manage Locations', 'thsalespartner' ),
            array( $this, 'manage_locations_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );

        // Required Fields Table
        add_settings_field(
            'thsp_required_fields',
            __( 'Required Fields', 'thsalespartner' ),
            array( $this, 'required_fields_table_html' ),
            'thsp-formular-oferta',
            'thsp_form_general_section'
        );
    }

    /**
     * Callback for form settings section
     */
    public function form_settings_section_callback() {
        echo '<p>' . __( 'Configure which fields are displayed and which are required in the lead form.', 'thsalespartner' ) . '</p>';
    }

    /**
     * HTML for displaying "Locatie" field checkbox
     */
    public function display_location_field_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $checked = isset( $settings['thsp_display_location_field'] ) && $settings['thsp_display_location_field'] === '1' ? 'checked' : '';
        echo '<input type="checkbox" name="thsp_form_settings[thsp_display_location_field]" value="1" ' . $checked . ' />';
    }

    /**
     * HTML for displaying "Informatii Aditionale" field checkbox
     */
    public function display_message_field_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $checked = isset( $settings['thsp_display_message_field'] ) && $settings['thsp_display_message_field'] === '1' ? 'checked' : '';
        echo '<input type="checkbox" name="thsp_form_settings[thsp_display_message_field]" value="1" ' . $checked . ' />';
    }

    /**
     * HTML for displaying "METODA CONTACT PREFERATA" field checkbox
     */
    public function display_contact_method_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $checked = isset( $settings['thsp_display_contact_method'] ) && $settings['thsp_display_contact_method'] === '1' ? 'checked' : '';
        echo '<input type="checkbox" name="thsp_form_settings[thsp_display_contact_method]" value="1" ' . $checked . ' />';
    }

    /**
     * HTML for Checkbox1 label textarea
     */
    public function checkbox1_label_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $value = isset( $settings['thsp_checkbox1_label'] ) ? $settings['thsp_checkbox1_label'] : '';
        echo '<textarea name="thsp_form_settings[thsp_checkbox1_label]" rows="5" cols="50">' . esc_textarea( $value ) . '</textarea>';
        echo '<p class="description">' . __( 'Enter the HTML content for the Terms and Conditions checkbox label.', 'thsalespartner' ) . '</p>';
    }

    /**
     * HTML for Checkbox2 label textarea
     */
    public function checkbox2_label_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $value = isset( $settings['thsp_checkbox2_label'] ) ? $settings['thsp_checkbox2_label'] : '';
        echo '<textarea name="thsp_form_settings[thsp_checkbox2_label]" rows="5" cols="50">' . esc_textarea( $value ) . '</textarea>';
        echo '<p class="description">' . __( 'Enter the HTML content for the Other Conditions checkbox label.', 'thsalespartner' ) . '</p>';
    }

    /**
     * HTML for managing locations
     */
    public function manage_locations_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $locations = isset( $settings['thsp_locations'] ) && is_array( $settings['thsp_locations'] ) ? $settings['thsp_locations'] : array();
        ?>
        <table class="form-table">
            <tr valign="top">
                <th scope="row"><?php _e( 'Add New Location', 'thsalespartner' ); ?></th>
                <td>
                    <input type="text" id="new_location" name="new_location" />
                    <button type="button" class="button" id="thsp-add-location"><?php _e( 'Add Location', 'thsalespartner' ); ?></button>
                </td>
            </tr>
            <tr valign="top">
                <th scope="row"><?php _e( 'Existing Locations', 'thsalespartner' ); ?></th>
                <td>
                    <ul id="thsp-locations-list">
                        <?php
                        if ( ! empty( $locations ) ) :
                            foreach ( $locations as $index => $location ) :
                                // Use esc_html to prevent XSS
                                $escaped_location = esc_html( $location );
                                echo '<li>' . $escaped_location . ' <button type="button" class="button thsp-remove-location">' . __( 'Remove', 'thsalespartner' ) . '</button></li>';
                            endforeach;
                        endif;
                        ?>
                    </ul>
                </td>
            </tr>
        </table>
        <script type="text/javascript">
            jQuery(document).ready(function($){
                $('#thsp-add-location').on('click', function(){
                    var newLocation = $('#new_location').val().trim();
                    if(newLocation !== ''){
                        // Append to the list
                        var newListItem = $('<li>').text(newLocation + ' ');
                        var removeButton = $('<button>')
                            .attr('type', 'button')
                            .addClass('button thsp-remove-location')
                            .text('<?php echo esc_js( __( "Remove", "thsalespartner" ) ); ?>');
                        newListItem.append(removeButton);
                        $('#thsp-locations-list').append(newListItem);
                        
                        // Append hidden input
                        var newHiddenInput = $('<input>')
                            .attr('type', 'hidden')
                            .attr('name', 'thsp_form_settings[thsp_locations][]')
                            .val(newLocation);
                        $('form').append(newHiddenInput);
                        
                        // Clear the input field
                        $('#new_location').val('');
                    }
                });

                $(document).on('click', '.thsp-remove-location', function(){
                    // Remove the list item
                    $(this).parent('li').remove();
                    
                    // Remove the corresponding hidden input
                    $('input[type="hidden"][value="' + $(this).parent('li').text().trim() + '"]').remove();
                });
            });
        </script>
        <?php
    }

    /**
     * HTML for Required Fields Table
     */
    public function required_fields_table_html() {
        $settings = get_option( 'thsp_form_settings', array() );
        $required_fields = isset( $settings['thsp_required_fields'] ) && is_array( $settings['thsp_required_fields'] ) ? $settings['thsp_required_fields'] : array();

        // Define all form fields
        $all_fields = array(
            'model_id'                 => __( 'Model', 'thsalespartner' ),
            'firstname'                => __( 'First Name', 'thsalespartner' ),
            'lastname'                 => __( 'Last Name', 'thsalespartner' ),
            'email'                    => __( 'Email', 'thsalespartner' ),
            'phone'                    => __( 'Phone', 'thsalespartner' ),
            'location'                 => __( 'Location', 'thsalespartner' ),
            'message'                  => __( 'Informatii Aditionale', 'thsalespartner' ),
            'preferred_contact_method' => __( 'METODA CONTACT PREFERATA*', 'thsalespartner' ),
            'checkbox1'                => __( 'Checkbox1 (Terms and Conditions)', 'thsalespartner' ),
            'checkbox2'                => __( 'Checkbox2 (Other Conditions)', 'thsalespartner' ),
        );

        ?>
        <h2><?php _e( 'Required Fields', 'thsalespartner' ); ?></h2>
        <table class="form-table">
            <thead>
                <tr>
                    <th><?php _e( 'Field', 'thsalespartner' ); ?></th>
                    <th><?php _e( 'Required', 'thsalespartner' ); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ( $all_fields as $field_key => $field_label ) : ?>
                    <tr>
                        <th scope="row"><?php echo esc_html( $field_label ); ?></th>
                        <td>
                            <input type="checkbox" name="thsp_form_settings[thsp_required_fields][]" value="<?php echo esc_attr( $field_key ); ?>" <?php checked( in_array( $field_key, $required_fields, true ) ); ?> />
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <p class="description"><?php _e( 'Select the fields that should be mandatory in the lead form.', 'thsalespartner' ); ?></p>
        <?php
    }

    /**
     * Sanitize form settings
     */
    public function sanitize_form_settings( $input ) {
        $sanitized = array();

        // Sanitize display checkboxes
        $sanitized['thsp_display_location_field'] = isset( $input['thsp_display_location_field'] ) && $input['thsp_display_location_field'] === '1' ? '1' : '';
        $sanitized['thsp_display_message_field'] = isset( $input['thsp_display_message_field'] ) && $input['thsp_display_message_field'] === '1' ? '1' : '';
        $sanitized['thsp_display_contact_method'] = isset( $input['thsp_display_contact_method'] ) && $input['thsp_display_contact_method'] === '1' ? '1' : '';

        // Sanitize checkbox labels
        $sanitized['thsp_checkbox1_label'] = isset( $input['thsp_checkbox1_label'] ) ? wp_kses_post( $input['thsp_checkbox1_label'] ) : '';
        $sanitized['thsp_checkbox2_label'] = isset( $input['thsp_checkbox2_label'] ) ? wp_kses_post( $input['thsp_checkbox2_label'] ) : '';

        // Sanitize locations
        if ( isset( $input['thsp_locations'] ) && is_array( $input['thsp_locations'] ) ) {
            $sanitized['thsp_locations'] = array_map( 'sanitize_text_field', $input['thsp_locations'] );
        } else {
            $sanitized['thsp_locations'] = array();
        }

        // Sanitize required fields
        if ( isset( $input['thsp_required_fields'] ) && is_array( $input['thsp_required_fields'] ) ) {
            // Define all valid field keys
            $valid_fields = array(
                'model_id',
                'firstname',
                'lastname',
                'email',
                'phone',
                'location',
                'message',
                'preferred_contact_method',
                'checkbox1',
                'checkbox2',
            );

            $sanitized['thsp_required_fields'] = array();

            foreach ( $input['thsp_required_fields'] as $field ) {
                $field = sanitize_text_field( $field );
                if ( in_array( $field, $valid_fields, true ) ) {
                    $sanitized['thsp_required_fields'][] = $field;
                }
            }
        } else {
            $sanitized['thsp_required_fields'] = array();
        }

        return $sanitized;
    }

    /**
     * Display the Formular Oferta admin page
     */
    public function formular_oferta_page_html() {
        if ( ! current_user_can( 'manage_options' ) ) {
            return;
        }
    
        ?>
        <div class="wrap">
            <h1><?php echo __( 'Formular Oferta Settings', 'thsalespartner' ); ?></h1>
            <form action="options.php" method="post">
                <?php
                settings_fields( 'thsp_form_settings_group' );
                do_settings_sections( 'thsp-formular-oferta' );
                submit_button( __( 'Save Settings', 'thsalespartner' ) );
                ?>
            </form>
            <hr />
            <div class="thsp-form-render-instructions">
                <h2><?php _e( 'Render the Lead Form', 'thsalespartner' ); ?></h2>
                <p>
                    <?php
                    /* translators: 1: Shortcode */
                    printf(
                        /* translators: 1: Shortcode */
                        __( 'To display the lead form on a page or post, use the following shortcode: <strong>%1$s</strong>', 'thsalespartner' ),
                        '[thsp_lead_form]'
                    );
                    ?>
                </p>
                <p>
                    <?php
                    /* translators: 1: PHP function name */
                    printf(
                        /* translators: 1: PHP function name */
                        __( 'Alternatively, to embed the form directly within a PHP template, use the following PHP function:', 'thsalespartner' ),
                        'thsp_render_lead_form()'
                    );
                    ?>
                </p>
                <pre><code>&lt;?php echo do_shortcode('[thsp_lead_form]'); ?&gt;</code></pre>
            </div>
        </div>
        <?php
    }

}

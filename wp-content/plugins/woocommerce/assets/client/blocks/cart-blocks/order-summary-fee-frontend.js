"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[5057],{8046:(e,c,s)=>{s.r(c),s.d(c,{default:()=>o});var t=s(1609),r=s(4656),a=s(910),n=s(8509);const o=({className:e})=>{const{cartFees:c,cartTotals:s}=(0,n.V)(),o=(0,a.getCurrencyFromPriceResponse)(s);return(0,t.createElement)(r.TotalsWrapper,{className:e},(0,t.createElement)(r.TotalsFees,{currency:o,cartFees:c}))}}}]);
"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[146],{6581:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var o=n(1609),c=n(8639),r=n(4656),a=n(910),s=n(8509),l=n(3551),u=n(1e3);const p=()=>{const{extensions:e,receiveCart:t,...n}=(0,s.V)(),c={extensions:e,cart:n,context:"woocommerce/cart"};return(0,o.createElement)(u.ExperimentalDiscountsMeta.Slot,{...c})},m=({className:e})=>{const{cartTotals:t,cartCoupons:n}=(0,s.V)(),{removeCoupon:u,isRemovingCoupon:m}=(0,l.k)("wc/cart"),C=(0,a.getCurrencyFromPriceResponse)(t);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(r.TotalsWrapper,{className:e},(0,o.createElement)(c.n$,{cartCoupons:n,currency:C,isRemovingCoupon:m,removeCoupon:u,values:t})),(0,o.createElement)(p,null))}}}]);
"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[1631],{6036:(e,c,t)=>{t.r(c),t.d(c,{default:()=>o});var a=t(1609),r=t(4656),s=t(910),n=t(8509);const o=({className:e=""})=>{const{cartTotals:c}=(0,n.V)(),t=(0,s.getCurrencyFromPriceResponse)(c);return(0,a.createElement)(r.TotalsWrapper,{className:e},(0,a.createElement)(r.Subtotal,{currency:t,values:c}))}}}]);
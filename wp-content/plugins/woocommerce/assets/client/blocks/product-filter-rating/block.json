{"name": "woocommerce/product-filter-rating", "version": "1.0.0", "title": "Filter Options", "description": "Enable customers to filter the product collection by rating.", "category": "woocommerce", "keywords": ["WooCommerce"], "supports": {"interactivity": true, "inserter": false, "color": {"background": false, "text": true}}, "ancestor": ["woocommerce/product-filter"], "usesContext": ["query", "queryId"], "attributes": {"className": {"type": "string", "default": ""}, "showCounts": {"type": "boolean", "default": false}, "displayStyle": {"type": "string", "default": "list"}, "selectType": {"type": "string", "default": "multiple"}, "isPreview": {"type": "boolean", "default": false}}, "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}
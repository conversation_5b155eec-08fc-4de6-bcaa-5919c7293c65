(()=>{var e,t,o,r={3986:(e,t,o)=>{"use strict";o.r(t);var r=o(1609),n=o(851),s=o(5573);const c=(0,r.createElement)(s.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",fill:"none"},(0,r.createElement)("path",{stroke:"currentColor",strokeWidth:"1.5",fill:"none",d:"M5 3.75h14c.69 0 1.25.56 1.25 1.25v14c0 .69-.56 1.25-1.25 1.25H5c-.69 0-1.25-.56-1.25-1.25V5c0-.69.56-1.25 1.25-1.25z"}),(0,r.createElement)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.4 10.75c0-.47.38-.85.85-.85h9.5c.47 0 .85.38.85.85v1.5c0 .47-.38.85-.85.85h-9.5a.85.85 0 01-.85-.85v-1.5zm1.2.35v.8h8.8v-.8H7.6zM12.4 15.25c0-.47.38-.85.85-.85h3.5c.47 0 .85.38.85.85v1.5c0 .47-.38.85-.85.85h-3.5a.85.85 0 01-.85-.85v-1.5zm1.2.35v.8h2.8v-.8h-2.8zM6.5 15.9a.6.6 0 01.6-.6h2.8a.6.6 0 010 1.2H7.1a.6.6 0 01-.6-.6zM6.5 7.9a.6.6 0 01.6-.6h9.8a.6.6 0 110 1.2H7.1a.6.6 0 01-.6-.6z",clipRule:"evenodd"}));var a=o(7104);const i=window.wp.blocks;var l=o(7723);const m=window.wp.blockEditor;var d=o(6087),p=o(9491);const u=(0,d.createContext)({hasContainerWidth:!1,containerClassName:"",isMobile:!1,isSmall:!1,isMedium:!1,isLarge:!1}),h=({children:e,className:t=""})=>{const[o,s]=(()=>{const[e,{width:t}]=(0,p.useResizeObserver)();let o="";return t>700?o="is-large":t>520?o="is-medium":t>400?o="is-small":t&&(o="is-mobile"),[e,o]})(),c={hasContainerWidth:""!==s,containerClassName:s,isMobile:"is-mobile"===s,isSmall:"is-small"===s,isMedium:"is-medium"===s,isLarge:"is-large"===s};return(0,r.createElement)(u.Provider,{value:c},(0,r.createElement)("div",{className:(0,n.A)(t,s)},o,e))};o(1221);const g=({children:e,className:t})=>(0,r.createElement)(h,{className:(0,n.A)("wc-block-components-sidebar-layout",t)},e),_=window.wp.data,E=(0,d.createContext)({isEditor:!1,currentPostId:0,currentView:"",previewData:{},getPreviewData:()=>({})}),k=()=>(0,d.useContext)(E),w=({children:e,currentPostId:t=0,previewData:o={},currentView:n="",isPreview:s=!1})=>{const c=(0,_.useSelect)((e=>t||e("core/editor").getCurrentPostId()),[t]),a=(0,d.useCallback)((e=>o&&e in o?o[e]:{}),[o]),i={isEditor:!0,currentPostId:c,currentView:n,previewData:o,getPreviewData:a,isPreview:s};return(0,r.createElement)(E.Provider,{value:i},e)},b=window.wp.plugins,y=window.wc.wcSettings;var v,f,C,S,A,P,N,T,R,x,I=o(2294);const O=(0,y.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),M=O.pluginUrl+"assets/images/",B=(O.pluginUrl,null===(v=y.STORE_PAGES.shop)||void 0===v||v.permalink,null===(f=y.STORE_PAGES.checkout)||void 0===f?void 0:f.id),F=(null===(C=y.STORE_PAGES.checkout)||void 0===C||C.permalink,null===(S=y.STORE_PAGES.privacy)||void 0===S?void 0:S.permalink),D=(null===(A=y.STORE_PAGES.privacy)||void 0===A||A.title,null===(P=y.STORE_PAGES.terms)||void 0===P?void 0:P.permalink),L=(null===(N=y.STORE_PAGES.terms)||void 0===N||N.title,null===(T=y.STORE_PAGES.cart)||void 0===T?void 0:T.id),Y=null===(R=y.STORE_PAGES.cart)||void 0===R?void 0:R.permalink,V=(null!==(x=y.STORE_PAGES.myaccount)&&void 0!==x&&x.permalink?y.STORE_PAGES.myaccount.permalink:(0,y.getSetting)("wpLoginUrl","/wp-login.php"),(0,y.getSetting)("localPickupEnabled",!1)),j=(0,y.getSetting)("countries",{}),U=(0,y.getSetting)("countryData",{}),K=Object.fromEntries(Object.keys(U).filter((e=>!0===U[e].allowBilling)).map((e=>[e,j[e]||""]))),H=Object.fromEntries(Object.keys(U).filter((e=>!0===U[e].allowBilling)).map((e=>[e,U[e].states||[]]))),q=Object.fromEntries(Object.keys(U).filter((e=>!0===U[e].allowShipping)).map((e=>[e,j[e]||""]))),$=Object.fromEntries(Object.keys(U).filter((e=>!0===U[e].allowShipping)).map((e=>[e,U[e].states||[]]))),z=Object.fromEntries(Object.keys(U).map((e=>[e,U[e].locale||[]]))),W={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},G=(0,y.getSetting)("addressFieldsLocations",W).address,Z=(0,y.getSetting)("addressFieldsLocations",W).contact,X=(0,y.getSetting)("addressFieldsLocations",W).order,J=((0,y.getSetting)("additionalOrderFields",{}),(0,y.getSetting)("additionalContactFields",{}),(0,y.getSetting)("additionalAddressFields",{}),({imageUrl:e=`${M}/block-error.svg`,header:t=(0,l.__)("Oops!","woocommerce"),text:o=(0,l.__)("There was an error loading the content.","woocommerce"),errorMessage:n,errorMessagePrefix:s=(0,l.__)("Error:","woocommerce"),button:c,showErrorBlock:a=!0})=>a?(0,r.createElement)("div",{className:"wc-block-error wc-block-components-error"},e&&(0,r.createElement)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,r.createElement)("div",{className:"wc-block-error__content wc-block-components-error__content"},t&&(0,r.createElement)("p",{className:"wc-block-error__header wc-block-components-error__header"},t),o&&(0,r.createElement)("p",{className:"wc-block-error__text wc-block-components-error__text"},o),n&&(0,r.createElement)("p",{className:"wc-block-error__message wc-block-components-error__message"},s?s+" ":"",n),c&&(0,r.createElement)("p",{className:"wc-block-error__button wc-block-components-error__button"},c))):null);o(9407);class Q extends d.Component{constructor(...e){super(...e),(0,I.A)(this,"state",{errorMessage:"",hasError:!1})}static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,r.createElement)(r.Fragment,null,(0,r.createElement)("strong",null,e.status),": ",e.statusText),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:o=!0,showErrorBlock:n=!0,text:s,errorMessagePrefix:c,renderError:a,button:i}=this.props,{errorMessage:l,hasError:m}=this.state;return m?"function"==typeof a?a({errorMessage:l}):(0,r.createElement)(J,{showErrorBlock:n,errorMessage:o?l:null,header:e,imageUrl:t,text:s,errorMessagePrefix:c,button:i}):this.props.children}}const ee=Q,te=window.wc.wcBlocksData;var oe=o(4040),re=o.n(oe);let ne=function(e){return e.ADD_EVENT_CALLBACK="add_event_callback",e.REMOVE_EVENT_CALLBACK="remove_event_callback",e}({});const se={},ce=(e=se,{type:t,eventType:o,id:r,callback:n,priority:s})=>{const c=e.hasOwnProperty(o)?new Map(e[o]):new Map;switch(t){case ne.ADD_EVENT_CALLBACK:return c.set(r,{priority:s,callback:n}),{...e,[o]:c};case ne.REMOVE_EVENT_CALLBACK:return c.delete(r),{...e,[o]:c}}},ae=(e,t)=>(o,r=10)=>{const n=((e,t,o=10)=>({id:Math.floor(Math.random()*Date.now()).toString(),type:ne.ADD_EVENT_CALLBACK,eventType:e,callback:t,priority:o}))(e,o,r);return t(n),()=>{var o;t((o=e,{id:n.id,type:ne.REMOVE_EVENT_CALLBACK,eventType:o}))}},ie=(0,d.createContext)({onPaymentProcessing:()=>()=>()=>{},onPaymentSetup:()=>()=>()=>{}}),le=({children:e})=>{const{isProcessing:t,isIdle:o,isCalculating:n,hasError:s}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{isProcessing:t.isProcessing(),isIdle:t.isIdle(),hasError:t.hasError(),isCalculating:t.isCalculating()}})),{isPaymentReady:c}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{isPaymentProcessing:t.isPaymentProcessing(),isPaymentReady:t.isPaymentReady()}})),{setValidationErrors:a}=(0,_.useDispatch)(te.VALIDATION_STORE_KEY),[i,l]=(0,d.useReducer)(ce,{}),{onPaymentSetup:m}=(e=>(0,d.useMemo)((()=>({onPaymentSetup:ae("payment_setup",e)})),[e]))(l),p=(0,d.useRef)(i);(0,d.useEffect)((()=>{p.current=i}),[i]);const{__internalSetPaymentProcessing:u,__internalSetPaymentIdle:h,__internalEmitPaymentProcessingEvent:g}=(0,_.useDispatch)(te.PAYMENT_STORE_KEY);(0,d.useEffect)((()=>{!t||s||n||(u(),g(p.current,a))}),[t,s,n,u,g,a]),(0,d.useEffect)((()=>{o&&!c&&h()}),[o,c,h]),(0,d.useEffect)((()=>{s&&c&&h()}),[s,c,h]);const E={onPaymentProcessing:(0,d.useMemo)((()=>function(...e){return re()("onPaymentProcessing",{alternative:"onPaymentSetup",plugin:"WooCommerce Blocks"}),m(...e)}),[m]),onPaymentSetup:m};return(0,r.createElement)(ie.Provider,{value:E},e)},me={NONE:"none",INVALID_ADDRESS:"invalid_address",UNKNOWN:"unknown_error"},de={INVALID_COUNTRY:"woocommerce_rest_cart_shipping_rates_invalid_country",MISSING_COUNTRY:"woocommerce_rest_cart_shipping_rates_missing_country",INVALID_STATE:"woocommerce_rest_cart_shipping_rates_invalid_state"},pe={shippingErrorStatus:{isPristine:!0,isValid:!1,hasInvalidAddress:!1,hasError:!1},dispatchErrorStatus:e=>e,shippingErrorTypes:me,onShippingRateSuccess:()=>()=>{},onShippingRateFail:()=>()=>{},onShippingRateSelectSuccess:()=>()=>{},onShippingRateSelectFail:()=>()=>{}},ue=(e,{type:t})=>Object.values(me).includes(t)?t:e,he="shipping_rates_success",ge="shipping_rates_fail",_e="shipping_rate_select_success",Ee="shipping_rate_select_fail",ke=e=>({onSuccess:ae(he,e),onFail:ae(ge,e),onSelectSuccess:ae(_e,e),onSelectFail:ae(Ee,e)}),we=window.wc.wcTypes;let be=function(e){return e.SUCCESS="success",e.FAIL="failure",e.ERROR="error",e}({}),ye=function(e){return e.CART="wc/cart",e.CHECKOUT="wc/checkout",e.PAYMENTS="wc/checkout/payments",e.EXPRESS_PAYMENTS="wc/checkout/express-payments",e.CONTACT_INFORMATION="wc/checkout/contact-information",e.SHIPPING_ADDRESS="wc/checkout/shipping-address",e.BILLING_ADDRESS="wc/checkout/billing-address",e.SHIPPING_METHODS="wc/checkout/shipping-methods",e.CHECKOUT_ACTIONS="wc/checkout/checkout-actions",e.ORDER_INFORMATION="wc/checkout/additional-information",e}({});const ve=async(e,t,o)=>{const r=((e,t)=>e[t]?Array.from(e[t].values()).sort(((e,t)=>e.priority-t.priority)):[])(e,t),n=[];for(const e of r)try{const t=await Promise.resolve(e.callback(o));"object"==typeof t&&n.push(t)}catch(e){console.error(e)}return!n.length||n};var fe=o(458),Ce=o.n(fe);const Se=window.wp.htmlEntities,Ae=e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,l.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,l.__)("%s (optional)","woocommerce"),e.label)),e.priority&&((0,we.isNumber)(e.priority)&&(t.index=e.priority),(0,we.isString)(e.priority)&&(t.index=parseInt(e.priority,10))),e.hidden&&(t.required=!1),t},Pe=Object.entries(z).map((([e,t])=>[e,Object.entries(t).map((([e,t])=>[e,Ae(t)])).reduce(((e,[t,o])=>(e[t]=o,e)),{})])).reduce(((e,[t,o])=>(e[t]=o,e)),{}),Ne=(e,t,o="")=>{const r=o&&void 0!==Pe[o]?Pe[o]:{};return e.map((e=>({key:e,...y.defaultFields[e]||{},...r[e]||{},...t[e]||{}}))).sort(((e,t)=>e.index-t.index))},Te=window.wp.url,Re=(e,t)=>e in t,xe=e=>{const t=Ne(G,{},e.country),o=Object.assign({},e);return t.forEach((({key:t="",hidden:r=!1})=>{r&&Re(t,e)&&(o[t]="")})),o},Ie=e=>{if(0===Object.values(e).length)return null;const t=(0,we.isString)(q[e.country])?(0,Se.decodeEntities)(q[e.country]):"",o=(0,we.isObject)($[e.country])&&(0,we.isString)($[e.country][e.state])?(0,Se.decodeEntities)($[e.country][e.state]):e.state,r=[];r.push(e.postcode.toUpperCase()),r.push(e.city),r.push(o),r.push(t);return r.filter(Boolean).join(", ")||null},Oe=(e,t=[])=>{if(!e.country)return!1;const o=Ne(G,{},e.country);return(t.length>0?Object.values(o).filter((({key:e})=>t.includes(e))):o).every((({key:t="",hidden:o=!1,required:r=!1})=>!(!o&&r)||Re(t,e)&&""!==e[t]))},Me=window.CustomEvent||null,Be=(e,t,o=!1,r=!1)=>{if("function"!=typeof jQuery)return()=>{};const n=()=>{((e,{bubbles:t=!1,cancelable:o=!1,element:r,detail:n={}})=>{if(!Me)return;r||(r=document.body);const s=new Me(e,{bubbles:t,cancelable:o,detail:n});r.dispatchEvent(s)})(t,{bubbles:o,cancelable:r})};return jQuery(document).on(e,n),()=>jQuery(document).off(e,n)},Fe=e=>{const t=null==e?void 0:e.detail;t&&t.preserveCartData||(0,_.dispatch)(te.CART_STORE_KEY).invalidateResolutionForStore()},De=e=>{(null!=e&&e.persisted||"back_forward"===(window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""))&&(0,_.dispatch)(te.CART_STORE_KEY).invalidateResolutionForStore()},Le=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},Ye={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},Ve={...Ye,email:""},je={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:te.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},Ue=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,Se.decodeEntities)(t)]))),Ke={cartCoupons:te.EMPTY_CART_COUPONS,cartItems:te.EMPTY_CART_ITEMS,cartFees:te.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:te.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:te.EMPTY_CART_ITEM_ERRORS,cartTotals:je,cartIsLoading:!0,cartErrors:te.EMPTY_CART_ERRORS,billingAddress:Ve,shippingAddress:Ye,shippingRates:te.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:te.EMPTY_PAYMENT_METHODS,paymentRequirements:te.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:te.EMPTY_EXTENSIONS},He=(e={shouldSelect:!0})=>{const{isEditor:t,previewData:o}=k(),r=null==o?void 0:o.previewCart,{shouldSelect:n}=e,s=(0,d.useRef)();(0,d.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),(null===(e=window.wcBlocksStoreCartListeners)||void 0===e?void 0:e.count)>0)return void window.wcBlocksStoreCartListeners.count++;var e;document.body.addEventListener("wc-blocks_added_to_cart",Fe),document.body.addEventListener("wc-blocks_removed_from_cart",Fe),window.addEventListener("pageshow",De);const t=Be("added_to_cart","wc-blocks_added_to_cart"),o=Be("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",Fe),document.body.removeEventListener("wc-blocks_removed_from_cart",Fe),window.removeEventListener("pageshow",De),t(),o()}})(),Le)),[]);const c=(0,_.useSelect)(((e,{dispatch:o})=>{if(!n)return Ke;if(t)return{cartCoupons:r.coupons,cartItems:r.items,crossSellsProducts:r.cross_sells,cartFees:r.fees,cartItemsCount:r.items_count,cartItemsWeight:r.items_weight,cartNeedsPayment:r.needs_payment,cartNeedsShipping:r.needs_shipping,cartItemErrors:te.EMPTY_CART_ITEM_ERRORS,cartTotals:r.totals,cartIsLoading:!1,cartErrors:te.EMPTY_CART_ERRORS,billingData:Ve,billingAddress:Ve,shippingAddress:Ye,extensions:te.EMPTY_EXTENSIONS,shippingRates:r.shipping_rates,isLoadingRates:!1,cartHasCalculatedShipping:r.has_calculated_shipping,paymentRequirements:r.paymentRequirements,receiveCart:"function"==typeof(null==r?void 0:r.receiveCart)?r.receiveCart:()=>{},receiveCartContents:"function"==typeof(null==r?void 0:r.receiveCartContents)?r.receiveCartContents:()=>{}};const s=e(te.CART_STORE_KEY),c=s.getCartData(),a=s.getCartErrors(),i=s.getCartTotals(),l=!s.hasFinishedResolution("getCartData"),m=s.isCustomerDataUpdating(),{receiveCart:d,receiveCartContents:p}=o(te.CART_STORE_KEY),u=Ue(c.billingAddress),h=c.needsShipping?Ue(c.shippingAddress):u,g=c.fees.length>0?c.fees.map((e=>Ue(e))):te.EMPTY_CART_FEES,_=c.coupons.length>0?c.coupons.map((e=>({...e,label:e.code}))):te.EMPTY_CART_COUPONS;return{cartCoupons:_,cartItems:c.items,crossSellsProducts:c.crossSells,cartFees:g,cartItemsCount:c.itemsCount,cartItemsWeight:c.itemsWeight,cartNeedsPayment:c.needsPayment,cartNeedsShipping:c.needsShipping,cartItemErrors:c.errors,cartTotals:i,cartIsLoading:l,cartErrors:a,billingData:xe(u),billingAddress:xe(u),shippingAddress:xe(h),extensions:c.extensions,shippingRates:c.shippingRates,isLoadingRates:m,cartHasCalculatedShipping:c.hasCalculatedShipping,paymentRequirements:c.paymentRequirements,receiveCart:d,receiveCartContents:p}}),[n]);return s.current&&Ce()(s.current,c)||(s.current=c),s.current},qe=e=>e.length,$e=(0,y.getSetting)("collectableMethodIds",[]),ze=e=>$e.includes(e.method_id),We=e=>!!V&&(Array.isArray(e)?!!e.find((e=>$e.includes(e))):$e.includes(e)),Ge=e=>Object.fromEntries(e.map((({package_id:e,shipping_rates:t})=>{var o;return[e,(null===(o=t.find((e=>e.selected)))||void 0===o?void 0:o.rate_id)||""]})));var Ze=o(923),Xe=o.n(Ze);const Je={currency_code:y.SITE_CURRENCY.code,currency_symbol:y.SITE_CURRENCY.symbol,currency_minor_unit:y.SITE_CURRENCY.minorUnit,currency_decimal_separator:y.SITE_CURRENCY.decimalSeparator,currency_thousand_separator:y.SITE_CURRENCY.thousandSeparator,currency_prefix:y.SITE_CURRENCY.prefix,currency_suffix:y.SITE_CURRENCY.suffix},Qe=e=>{const t=y.SITE_CURRENCY.minorUnit;if(2===t)return e;const o=Math.pow(10,t);return(Math.round(parseInt(e,10)/Math.pow(10,2))*o).toString()},et=[{destination:{address_1:"",address_2:"",city:"",state:"",postcode:"",country:""},package_id:0,name:(0,l.__)("Shipping","woocommerce"),items:[{key:"33e75ff09dd601bbe69f351039152189",name:(0,l._x)("Beanie with Logo","example product in Cart Block","woocommerce"),quantity:2},{key:"6512bd43d9caa6e02c990b0a82652dca",name:(0,l._x)("Beanie","example product in Cart Block","woocommerce"),quantity:1}],shipping_rates:[{...Je,name:(0,l.__)("Flat rate shipping","woocommerce"),description:"",delivery_time:"",price:Qe("500"),taxes:"0",rate_id:"flat_rate:0",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!1},{...Je,name:(0,l.__)("Free shipping","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"free_shipping:1",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!0},{...Je,name:(0,l.__)("Local pickup","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"pickup_location:1",instance_id:1,meta_data:[{key:"pickup_location",value:"New York"},{key:"pickup_address",value:"123 Easy Street, New York, 12345"}],method_id:"pickup_location",selected:!1},{...Je,name:(0,l.__)("Local pickup","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"pickup_location:2",instance_id:1,meta_data:[{key:"pickup_location",value:"Los Angeles"},{key:"pickup_address",value:"123 Easy Street, Los Angeles, California, 90210"}],method_id:"pickup_location",selected:!1}]}],tt=(0,y.getSetting)("displayCartPricesIncludingTax",!1),ot={coupons:[],shipping_rates:(0,y.getSetting)("shippingMethodsExist",!1)||(0,y.getSetting)("localPickupEnabled",!1)?et:[],items:[{key:"1",id:1,type:"simple",quantity:2,catalog_visibility:"visible",name:(0,l.__)("Beanie","woocommerce"),summary:(0,l.__)("Beanie","woocommerce"),short_description:(0,l.__)("Warm hat for winter","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-beanie",permalink:"https://example.org",low_stock_remaining:2,backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:10,src:M+"previews/beanie.jpg",thumbnail:M+"previews/beanie.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,l.__)("Color","woocommerce"),value:(0,l.__)("Yellow","woocommerce")},{attribute:(0,l.__)("Size","woocommerce"),value:(0,l.__)("Small","woocommerce")}],prices:{...Je,price:Qe(tt?"12000":"10000"),regular_price:Qe(tt?"120":"100"),sale_price:Qe(tt?"12000":"10000"),price_range:null,raw_prices:{precision:6,price:tt?"12000000":"10000000",regular_price:tt?"12000000":"10000000",sale_price:tt?"12000000":"10000000"}},totals:{...Je,line_subtotal:Qe("2000"),line_subtotal_tax:Qe("400"),line_total:Qe("2000"),line_total_tax:Qe("400")},extensions:{},item_data:[]},{key:"2",id:2,type:"simple",quantity:1,catalog_visibility:"visible",name:(0,l.__)("Cap","woocommerce"),summary:(0,l.__)("Cap","woocommerce"),short_description:(0,l.__)("Lightweight baseball cap","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-cap",low_stock_remaining:null,permalink:"https://example.org",backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:11,src:M+"previews/cap.jpg",thumbnail:M+"previews/cap.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,l.__)("Color","woocommerce"),value:(0,l.__)("Orange","woocommerce")}],prices:{...Je,price:Qe(tt?"2400":"2000"),regular_price:Qe(tt?"2400":"2000"),sale_price:Qe(tt?"2400":"2000"),price_range:null,raw_prices:{precision:6,price:tt?"24000000":"20000000",regular_price:tt?"24000000":"20000000",sale_price:tt?"24000000":"20000000"}},totals:{...Je,line_subtotal:Qe("2000"),line_subtotal_tax:Qe("400"),line_total:Qe("2000"),line_total_tax:Qe("400")},extensions:{},item_data:[]}],cross_sells:[{id:1,name:(0,l.__)("Polo","woocommerce"),slug:"polo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-polo",short_description:(0,l.__)("Polo","woocommerce"),description:(0,l.__)("Polo","woocommerce"),on_sale:!1,prices:{...Je,price:Qe(tt?"24000":"20000"),regular_price:Qe(tt?"24000":"20000"),sale_price:Qe(tt?"12000":"10000"),price_range:null},price_html:"",average_rating:"4.5",review_count:2,images:[{id:17,src:M+"previews/polo.jpg",thumbnail:M+"previews/polo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:2,name:(0,l.__)("Long Sleeve Tee","woocommerce"),slug:"long-sleeve-tee",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-long-sleeve-tee",short_description:(0,l.__)("Long Sleeve Tee","woocommerce"),description:(0,l.__)("Long Sleeve Tee","woocommerce"),on_sale:!1,prices:{...Je,price:Qe(tt?"30000":"25000"),regular_price:Qe(tt?"30000":"25000"),sale_price:Qe(tt?"30000":"25000"),price_range:null},price_html:"",average_rating:"4",review_count:2,images:[{id:17,src:M+"previews/long-sleeve-tee.jpg",thumbnail:M+"previews/long-sleeve-tee.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:3,name:(0,l.__)("Hoodie with Zipper","woocommerce"),slug:"hoodie-with-zipper",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-zipper",short_description:(0,l.__)("Hoodie with Zipper","woocommerce"),description:(0,l.__)("Hoodie with Zipper","woocommerce"),on_sale:!0,prices:{...Je,price:Qe(tt?"15000":"12500"),regular_price:Qe(tt?"30000":"25000"),sale_price:Qe(tt?"15000":"12500"),price_range:null},price_html:"",average_rating:"1",review_count:2,images:[{id:17,src:M+"previews/hoodie-with-zipper.jpg",thumbnail:M+"previews/hoodie-with-zipper.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:4,name:(0,l.__)("Hoodie with Logo","woocommerce"),slug:"hoodie-with-logo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-logo",short_description:(0,l.__)("Polo","woocommerce"),description:(0,l.__)("Polo","woocommerce"),on_sale:!1,prices:{...Je,price:Qe(tt?"4500":"4250"),regular_price:Qe(tt?"4500":"4250"),sale_price:Qe(tt?"4500":"4250"),price_range:null},price_html:"",average_rating:"5",review_count:2,images:[{id:17,src:M+"previews/hoodie-with-logo.jpg",thumbnail:M+"previews/hoodie-with-logo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:5,name:(0,l.__)("Hoodie with Pocket","woocommerce"),slug:"hoodie-with-pocket",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-pocket",short_description:(0,l.__)("Hoodie with Pocket","woocommerce"),description:(0,l.__)("Hoodie with Pocket","woocommerce"),on_sale:!0,prices:{...Je,price:Qe(tt?"3500":"3250"),regular_price:Qe(tt?"4500":"4250"),sale_price:Qe(tt?"3500":"3250"),price_range:null},price_html:"",average_rating:"3.75",review_count:4,images:[{id:17,src:M+"previews/hoodie-with-pocket.jpg",thumbnail:M+"previews/hoodie-with-pocket.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:6,name:(0,l.__)("T-Shirt","woocommerce"),slug:"t-shirt",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-t-shirt",short_description:(0,l.__)("T-Shirt","woocommerce"),description:(0,l.__)("T-Shirt","woocommerce"),on_sale:!1,prices:{...Je,price:Qe(tt?"1800":"1500"),regular_price:Qe(tt?"1800":"1500"),sale_price:Qe(tt?"1800":"1500"),price_range:null},price_html:"",average_rating:"3",review_count:2,images:[{id:17,src:M+"previews/tshirt.jpg",thumbnail:M+"previews/tshirt.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}}],fees:[{id:"fee",name:(0,l.__)("Fee","woocommerce"),totals:{...Je,total:Qe("100"),total_tax:Qe("20")}}],items_count:3,items_weight:0,needs_payment:!0,needs_shipping:(0,y.getSetting)("shippingEnabled",!0),has_calculated_shipping:!0,shipping_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},billing_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",email:"",phone:""},totals:{...Je,total_items:Qe("4000"),total_items_tax:Qe("800"),total_fees:Qe("100"),total_fees_tax:Qe("20"),total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_tax:Qe("820"),total_price:Qe("4920"),tax_lines:[{name:(0,l.__)("Sales tax","woocommerce"),rate:"20%",price:Qe("820")}]},errors:[],payment_methods:["cod","bacs","cheque"],payment_requirements:["products"],extensions:{}},rt=window.wp.hooks,nt=()=>({dispatchStoreEvent:(0,d.useCallback)(((e,t={})=>{try{(0,rt.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,d.useCallback)(((e,t={})=>{try{(0,rt.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,_.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])}),st=()=>{const{shippingRates:e,needsShipping:t,hasCalculatedShipping:o,isLoadingRates:r,isCollectable:n,isSelectingRate:s}=(0,_.useSelect)((e=>{const t=!!e("core/editor"),o=e(te.CART_STORE_KEY),r=t?ot.shipping_rates:o.getShippingRates();return{shippingRates:r,needsShipping:t?ot.needs_shipping:o.getNeedsShipping(),hasCalculatedShipping:t?ot.has_calculated_shipping:o.getHasCalculatedShipping(),isLoadingRates:!t&&o.isCustomerDataUpdating(),isCollectable:r.every((({shipping_rates:e})=>e.find((({method_id:e})=>We(e))))),isSelectingRate:!t&&o.isShippingRateBeingSelected()}})),c=(0,d.useRef)({});(0,d.useEffect)((()=>{const t=Ge(e);(0,we.isObject)(t)&&!Xe()(c.current,t)&&(c.current=t)}),[e]);const{selectShippingRate:a}=(0,_.useDispatch)(te.CART_STORE_KEY),i=We(Object.values(c.current).map((e=>e.split(":")[0]))),{dispatchCheckoutEvent:l}=nt(),m=(0,d.useCallback)(((e,t)=>{let o;void 0!==e&&(o=We(e.split(":")[0])?a(e,null):a(e,t),o.then((()=>{l("set-selected-shipping-rate",{shippingRateId:e})})).catch((e=>{(0,te.processErrorResponse)(e)})))}),[a,l]);return{isSelectingRate:s,selectedRates:c.current,selectShippingRate:m,shippingRates:e,needsShipping:t,hasCalculatedShipping:o,isLoadingRates:r,isCollectable:n,hasSelectedLocalPickup:i}},{NONE:ct,INVALID_ADDRESS:at,UNKNOWN:it}=me,lt=(0,d.createContext)(pe),mt=()=>(0,d.useContext)(lt),dt=({children:e})=>{const{__internalIncrementCalculating:t,__internalDecrementCalculating:o}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),{shippingRates:n,isLoadingRates:s,cartErrors:c}=He(),{selectedRates:a,isSelectingRate:i}=st(),[l,m]=(0,d.useReducer)(ue,ct),[p,u]=(0,d.useReducer)(ce,{}),h=(0,d.useRef)(p),g=(0,d.useMemo)((()=>({onShippingRateSuccess:ke(u).onSuccess,onShippingRateFail:ke(u).onFail,onShippingRateSelectSuccess:ke(u).onSelectSuccess,onShippingRateSelectFail:ke(u).onSelectFail})),[u]);(0,d.useEffect)((()=>{h.current=p}),[p]),(0,d.useEffect)((()=>{s?t():o()}),[s,t,o]),(0,d.useEffect)((()=>{i?t():o()}),[t,o,i]),(0,d.useEffect)((()=>{c.length>0&&c.some((e=>!(!e.code||!Object.values(de).includes(e.code))))?m({type:at}):m({type:ct})}),[c]);const E=(0,d.useMemo)((()=>({isPristine:l===ct,isValid:l===ct,hasInvalidAddress:l===at,hasError:l===it||l===at})),[l]);(0,d.useEffect)((()=>{s||0!==n.length&&!E.hasError||ve(h.current,ge,{hasInvalidAddress:E.hasInvalidAddress,hasError:E.hasError})}),[n,s,E.hasError,E.hasInvalidAddress]),(0,d.useEffect)((()=>{!s&&n.length>0&&!E.hasError&&ve(h.current,he,n)}),[n,s,E.hasError]),(0,d.useEffect)((()=>{i||(E.hasError?ve(h.current,Ee,{hasError:E.hasError,hasInvalidAddress:E.hasInvalidAddress}):ve(h.current,_e,a.current))}),[a,i,E.hasError,E.hasInvalidAddress]);const k={shippingErrorStatus:E,dispatchErrorStatus:m,shippingErrorTypes:me,...g};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(lt.Provider,{value:k},e))};function pt(e,t){const o=(0,d.useRef)();return(0,d.useEffect)((()=>{o.current===e||t&&!t(e,o.current)||(o.current=e)}),[e,t]),o.current}const ut={},ht={},gt=()=>ut,_t=()=>ht,Et=(0,d.createContext)({onSubmit:()=>{},onCheckoutAfterProcessingWithSuccess:()=>()=>{},onCheckoutAfterProcessingWithError:()=>()=>{},onCheckoutBeforeProcessing:()=>()=>{},onCheckoutValidationBeforeProcessing:()=>()=>{},onCheckoutSuccess:()=>()=>{},onCheckoutFail:()=>()=>{},onCheckoutValidation:()=>()=>{}}),kt=()=>(0,d.useContext)(Et),wt=({children:e,redirectUrl:t})=>{const o=gt(),n=_t(),{isEditor:s}=k(),{__internalUpdateAvailablePaymentMethods:c}=(0,_.useDispatch)(te.PAYMENT_STORE_KEY);(0,d.useEffect)((()=>{(s||0!==Object.keys(o).length||0!==Object.keys(n).length)&&c()}),[s,o,n,c]);const{__internalSetRedirectUrl:a,__internalEmitValidateEvent:i,__internalEmitAfterProcessingEvents:l,__internalSetBeforeProcessing:m}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),{checkoutRedirectUrl:p,checkoutStatus:u,isCheckoutBeforeProcessing:h,isCheckoutAfterProcessing:g,checkoutHasError:E,checkoutOrderId:w,checkoutOrderNotes:b,checkoutCustomerId:y}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{checkoutRedirectUrl:t.getRedirectUrl(),checkoutStatus:t.getCheckoutStatus(),isCheckoutBeforeProcessing:t.isBeforeProcessing(),isCheckoutAfterProcessing:t.isAfterProcessing(),checkoutHasError:t.hasError(),checkoutOrderId:t.getOrderId(),checkoutOrderNotes:t.getOrderNotes(),checkoutCustomerId:t.getCustomerId()}}));t&&t!==p&&a(t);const{setValidationErrors:v}=(0,_.useDispatch)(te.VALIDATION_STORE_KEY),{dispatchCheckoutEvent:f}=nt(),{checkoutNotices:C,paymentNotices:S,expressPaymentNotices:A}=(0,_.useSelect)((e=>{const{getNotices:t}=e("core/notices");return{checkoutNotices:Object.values(ye).filter((e=>e!==ye.PAYMENTS&&e!==ye.EXPRESS_PAYMENTS)).reduce(((e,o)=>[...e,...t(o)]),[]),paymentNotices:t(ye.PAYMENTS),expressPaymentNotices:t(ye.EXPRESS_PAYMENTS)}}),[]),[P,N]=(0,d.useReducer)(ce,{}),T=(0,d.useRef)(P),{onCheckoutValidation:R,onCheckoutSuccess:x,onCheckoutFail:I}=(e=>(0,d.useMemo)((()=>({onCheckoutSuccess:ae("checkout_success",e),onCheckoutFail:ae("checkout_fail",e),onCheckoutValidation:ae("checkout_validation",e)})),[e]))(N);(0,d.useEffect)((()=>{T.current=P}),[P]);const O=(0,d.useMemo)((()=>function(...e){return re()("onCheckoutBeforeProcessing",{alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks"}),R(...e)}),[R]),M=(0,d.useMemo)((()=>function(...e){return re()("onCheckoutValidationBeforeProcessing",{since:"9.7.0",alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),R(...e)}),[R]),B=(0,d.useMemo)((()=>function(...e){return re()("onCheckoutAfterProcessingWithSuccess",{since:"9.7.0",alternative:"onCheckoutSuccess",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),x(...e)}),[x]),F=(0,d.useMemo)((()=>function(...e){return re()("onCheckoutAfterProcessingWithError",{since:"9.7.0",alternative:"onCheckoutFail",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),I(...e)}),[I]);(0,d.useEffect)((()=>{h&&i({observers:T.current,setValidationErrors:v})}),[h,v,i]);const D=pt(u),L=pt(E);(0,d.useEffect)((()=>{u===D&&E===L||g&&l({observers:T.current,notices:{checkoutNotices:C,paymentNotices:S,expressPaymentNotices:A}})}),[u,E,p,w,y,b,g,h,D,L,C,A,S,i,l]);const Y={onSubmit:(0,d.useCallback)((()=>{f("submit"),m()}),[f,m]),onCheckoutBeforeProcessing:O,onCheckoutValidationBeforeProcessing:M,onCheckoutAfterProcessingWithSuccess:B,onCheckoutAfterProcessingWithError:F,onCheckoutSuccess:x,onCheckoutFail:I,onCheckoutValidation:R};return(0,r.createElement)(Et.Provider,{value:Y},e)},bt=window.wp.apiFetch;var yt=o.n(bt);(0,l.__)("Something went wrong. Please contact us to get assistance.","woocommerce");const vt=window.wc.wcBlocksRegistry,ft=(e,t,o)=>{const r=Object.keys(e).map((t=>({key:t,value:e[t]})),[]),n=`wc-${o}-new-payment-method`;return r.push({key:n,value:t}),r},Ct=e=>{if(!e)return;const{__internalSetCustomerId:t}=(0,_.dispatch)(te.CHECKOUT_STORE_KEY);yt().setNonce&&"function"==typeof yt().setNonce&&yt().setNonce(e),null!=e&&e.get("User-ID")&&t(parseInt(e.get("User-ID")||"0",10))},St=()=>{const{onCheckoutValidation:e}=kt(),{additionalFields:t,customerId:o,customerPassword:r,extensionData:n,hasError:s,isBeforeProcessing:c,isComplete:a,isProcessing:i,orderNotes:m,redirectUrl:p,shouldCreateAccount:u}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId(),customerPassword:t.getCustomerPassword(),extensionData:t.getExtensionData(),hasError:t.hasError(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes(),redirectUrl:t.getRedirectUrl(),shouldCreateAccount:t.getShouldCreateAccount()}})),{__internalSetHasError:h,__internalProcessCheckoutResponse:g}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),E=(0,_.useSelect)((e=>e(te.VALIDATION_STORE_KEY).hasValidationErrors)),{shippingErrorStatus:k}=mt(),{billingAddress:w,shippingAddress:b}=(0,_.useSelect)((e=>e(te.CART_STORE_KEY).getCustomerData())),{cartNeedsPayment:y,cartNeedsShipping:v,receiveCartContents:f}=He(),{activePaymentMethod:C,paymentMethodData:S,isExpressPaymentMethodActive:A,hasPaymentError:P,isPaymentReady:N,shouldSavePayment:T}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),hasPaymentError:t.hasPaymentError(),isPaymentReady:t.isPaymentReady(),shouldSavePayment:t.getShouldSavePaymentMethod()}}),[]),R=(0,vt.getPaymentMethods)(),x=(0,vt.getExpressPaymentMethods)(),I=(0,d.useRef)(w),O=(0,d.useRef)(b),M=(0,d.useRef)(p),[B,F]=(0,d.useState)(!1),D=(0,d.useMemo)((()=>{var e;const t={...x,...R};return null==t||null===(e=t[C])||void 0===e?void 0:e.paymentMethodId}),[C,x,R]),L=E()&&!A||P||k.hasError,Y=!s&&!L&&(N||!y)&&i;(0,d.useEffect)((()=>{L===s||!i&&!c||A||h(L)}),[L,s,i,c,A,h]),(0,d.useEffect)((()=>{I.current=w,O.current=b,M.current=p}),[w,b,p]);const V=(0,d.useCallback)((()=>E()?void 0!==(0,_.select)(te.VALIDATION_STORE_KEY).getValidationError("shipping-rates-error")&&{errorMessage:(0,l.__)("Sorry, this order requires a shipping option.","woocommerce")}:P?{errorMessage:(0,l.__)("There was a problem with your payment option.","woocommerce"),context:"wc/checkout/payments"}:!k.hasError||{errorMessage:(0,l.__)("There was a problem with your shipping option.","woocommerce"),context:"wc/checkout/shipping-methods"}),[E,P,k.hasError]);(0,d.useEffect)((()=>{let t;return A||(t=e(V,0)),()=>{A||"function"!=typeof t||t()}}),[e,V,A]),(0,d.useEffect)((()=>{M.current&&(window.location.href=M.current)}),[a]);const j=(0,d.useCallback)((async()=>{if(B)return;F(!0),(()=>{const e=(0,_.select)("wc/store/store-notices").getRegisteredContainers(),{removeNotice:t}=(0,_.dispatch)("core/notices"),{getNotices:o}=(0,_.select)("core/notices");e.forEach((e=>{o(e).forEach((o=>{t(o.id,e)}))}))})();const e=y?{payment_method:D,payment_data:ft(S,T,C)}:{},s={additional_fields:t,billing_address:xe(I.current),create_account:u,customer_note:m,customer_password:r,extensions:{...n},shipping_address:v?xe(O.current):void 0,...e};yt()({path:"/wc/store/v1/checkout",method:"POST",data:s,cache:"no-store",parse:!1}).then((e=>{if((0,we.assertResponseIsValid)(e),Ct(e.headers),!e.ok)throw e;return e.json()})).then((e=>{g(e),F(!1)})).catch((e=>{Ct(null==e?void 0:e.headers);try{e.json().then((e=>e)).then((e=>{var t;null!==(t=e.data)&&void 0!==t&&t.cart&&f(e.data.cart),(0,te.processErrorResponse)(e),g(e)}))}catch{let e=(0,l.__)("Something went wrong when placing the order. Check your email for order updates before retrying.","woocommerce");0!==o&&(e=(0,l.__)("Something went wrong when placing the order. Check your account's order history or your email for order updates before retrying.","woocommerce")),(0,te.processErrorResponse)({code:"unknown_error",message:e,data:null})}h(!0),F(!1)}))}),[B,y,D,S,T,C,m,u,o,r,n,t,v,f,h,g]);return(0,d.useEffect)((()=>{Y&&!B&&j()}),[j,Y,B]),null},At=({children:e,redirectUrl:t})=>(0,r.createElement)(wt,{redirectUrl:t},(0,r.createElement)(dt,null,(0,r.createElement)(le,null,e,(0,r.createElement)(ee,{renderError:y.CURRENT_USER_IS_ADMIN?null:()=>null},(0,r.createElement)(b.PluginArea,{scope:"woocommerce-checkout"})),(0,r.createElement)(St,null)))),Pt={cc:[{method:{gateway:"credit-card",last4:"5678",brand:"Visa"},expires:"12/20",is_default:!1,tokenId:"1"}]},Nt=window.wp.components,Tt=window.wc.blocksCheckout;var Rt=o(2172);const xt=(0,d.forwardRef)((({children:e,className:t=""},o)=>(0,r.createElement)("div",{ref:o,className:(0,n.A)("wc-block-components-main",t)},e))),It=(0,d.createContext)({showCompanyField:!1,requireCompanyField:!1,showApartmentField:!1,requireApartmentField:!1,showPhoneField:!1,requirePhoneField:!1,showOrderNotes:!0,showPolicyLinks:!0,showReturnToCart:!0,cartPageId:0,showRateAfterTaxName:!1,showFormStepNumbers:!1}),Ot=(0,d.createContext)({addressFieldControls:()=>null}),Mt=()=>(0,d.useContext)(It),Bt=()=>(0,d.useContext)(Ot),Ft=["core/paragraph","core/image","core/separator"],Dt=e=>{const t=(0,Tt.applyCheckoutFilter)({filterName:"additionalCartCheckoutInnerBlockTypes",defaultValue:[],extensions:(0,_.select)(te.CART_STORE_KEY).getCartData().extensions,arg:{block:e},validation:e=>{if(Array.isArray(e)&&e.every((e=>"string"==typeof e)))return!0;throw new Error("allowedBlockTypes filters must return an array of strings.")}});return Array.from(new Set([...(0,i.getBlockTypes)().filter((t=>((null==t?void 0:t.parent)||[]).includes(e))).map((({name:e})=>e)),...Ft,...t]))},Lt=({clientId:e,registeredBlocks:t,defaultTemplate:o=[]})=>{const r=(0,d.useRef)(t),n=(0,d.useRef)(o),s=(0,_.useRegistry)(),{isPreview:c}=k();(0,d.useEffect)((()=>{let t=!1;if(c)return;const{replaceInnerBlocks:o}=(0,_.dispatch)("core/block-editor");return s.subscribe((()=>{if(!s.select("core/block-editor").getBlock(e))return;const c=s.select("core/block-editor").getBlocks(e);if(0===c.length&&n.current.length>0&&!t){const r=(0,i.createBlocksFromInnerBlocksTemplate)(n.current);if(0!==r.length)return t=!0,void o(e,r)}const a=r.current.map((e=>(0,i.getBlockType)(e))),l=((e,t)=>{const o=t.filter((e=>e&&(({attributes:e})=>{var t,o,r;return Boolean((null===(t=e.lock)||void 0===t?void 0:t.remove)||(null===(o=e.lock)||void 0===o||null===(r=o.default)||void 0===r?void 0:r.remove))})(e))),r=[];return o.forEach((t=>{if(void 0===t)return;const o=e.find((e=>e.name===t.name));o||r.push(t)})),r})(c,a);if(0===l.length)return;let m=-1;const d=l.map((e=>{const t=n.current.findIndex((([t])=>t===e.name)),o=(0,i.createBlock)(e.name);return-1===m&&(m=(({defaultTemplatePosition:e,innerBlocks:t,currentDefaultTemplate:o})=>{switch(e){case-1:return t.length;case 0:return 0;default:const r=o.current[e-1],n=t.findIndex((({name:e})=>e===r[0]));return-1===n?e:n+1}})({defaultTemplatePosition:t,innerBlocks:c,currentDefaultTemplate:n})),o}));s.batch((()=>{s.dispatch("core/block-editor").insertBlocks(d,m,e)}))}),"core/block-editor")}),[e,c,s])};o(6619),(0,i.registerBlockType)("woocommerce/checkout-fields-block",{icon:{src:(0,r.createElement)(a.A,{icon:Rt.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e,attributes:t})=>{const o=(0,m.useBlockProps)({className:(0,n.A)("wc-block-checkout__main",null==t?void 0:t.className)}),s=Dt(Tt.innerBlockAreas.CHECKOUT_FIELDS),{showFormStepNumbers:c}=Mt(),{addressFieldControls:a}=Bt(),i=[["woocommerce/checkout-express-payment-block",{},[]],["woocommerce/checkout-contact-information-block",{},[]],["woocommerce/checkout-shipping-method-block",{},[]],["woocommerce/checkout-pickup-options-block",{},[]],["woocommerce/checkout-shipping-address-block",{},[]],["woocommerce/checkout-billing-address-block",{},[]],["woocommerce/checkout-shipping-methods-block",{},[]],["woocommerce/checkout-payment-block",{},[]],["woocommerce/checkout-additional-information-block",{},[]],["woocommerce/checkout-order-note-block",{},[]],["woocommerce/checkout-terms-block",{},[]],["woocommerce/checkout-actions-block",{},[]]].filter(Boolean);return Lt({clientId:e,registeredBlocks:s,defaultTemplate:i}),(0,r.createElement)(xt,{...o},(0,r.createElement)(a,null),(0,r.createElement)("form",{className:(0,n.A)("wc-block-components-form wc-block-checkout__form",{"wc-block-checkout__form--with-step-numbers":c})},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:s,templateLock:!1,template:i,renderAppender:m.InnerBlocks.ButtonBlockAppender})))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(m.InnerBlocks.Content,null))});const Yt=(0,d.forwardRef)((({children:e,className:t=""},o)=>(0,r.createElement)("div",{ref:o,className:(0,n.A)("wc-block-components-sidebar",t)},e)));o(7765),(0,i.registerBlockType)("woocommerce/checkout-totals-block",{icon:{src:(0,r.createElement)(a.A,{icon:Rt.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e,attributes:t})=>{const o=(0,m.useBlockProps)({className:(0,n.A)("wc-block-checkout__sidebar",null==t?void 0:t.className)}),s=Dt(Tt.innerBlockAreas.CHECKOUT_TOTALS),c=[["woocommerce/checkout-order-summary-block",{},[]]];return Lt({clientId:e,registeredBlocks:s,defaultTemplate:c}),(0,r.createElement)(Yt,{...o},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:s,templateLock:!1,template:c,renderAppender:m.InnerBlocks.ButtonBlockAppender}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(m.InnerBlocks.Content,null))});var Vt=o(601);const jt=()=>{const{customerData:e,isInitialized:t}=(0,_.useSelect)((e=>{const t=e(te.CART_STORE_KEY);return{customerData:t.getCustomerData(),isInitialized:t.hasFinishedResolution("getCartData")}})),{setShippingAddress:o,setBillingAddress:r}=(0,_.useDispatch)(te.CART_STORE_KEY);return{isInitialized:t,billingAddress:e.billingAddress,shippingAddress:e.shippingAddress,setBillingAddress:r,setShippingAddress:o}},Ut=()=>{const{needsShipping:e}=st(),{useShippingAsBilling:t,prefersCollection:o,editingBillingAddress:r,editingShippingAddress:n}=(0,_.useSelect)((e=>({useShippingAsBilling:e(te.CHECKOUT_STORE_KEY).getUseShippingAsBilling(),prefersCollection:e(te.CHECKOUT_STORE_KEY).prefersCollection(),editingBillingAddress:e(te.CHECKOUT_STORE_KEY).getEditingBillingAddress(),editingShippingAddress:e(te.CHECKOUT_STORE_KEY).getEditingShippingAddress()}))),{__internalSetUseShippingAsBilling:s,setEditingBillingAddress:c,setEditingShippingAddress:a}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),{billingAddress:i,setBillingAddress:l,shippingAddress:m,setShippingAddress:p}=jt(),u=(0,d.useCallback)((e=>{l({email:e})}),[l]),h=(0,y.getSetting)("forcedBillingAddress",!1);return{shippingAddress:m,billingAddress:i,setShippingAddress:p,setBillingAddress:l,setEmail:u,defaultFields:y.defaultFields,useShippingAsBilling:t,setUseShippingAsBilling:s,editingBillingAddress:r,editingShippingAddress:n,setEditingBillingAddress:c,setEditingShippingAddress:a,needsShipping:e,showShippingFields:!h&&e&&!o,showShippingMethods:e&&!o,showBillingFields:!e||!t||!!o,forcedBillingAddress:h,useBillingAsShipping:h||!!o}},Kt=window.wc.blocksComponents,Ht=({children:e,stepHeadingContent:t})=>(0,r.createElement)("div",{className:"wc-block-components-checkout-step__heading"},(0,r.createElement)(Kt.Title,{"aria-hidden":"true",className:"wc-block-components-checkout-step__title",headingLevel:"2"},e),!!t&&(0,r.createElement)("span",{className:"wc-block-components-checkout-step__heading-content"},t)),qt=({attributes:e,setAttributes:t,className:o="",children:s})=>{const{showFormStepNumbers:c}=Mt(),{title:a="",description:i=""}=e,d=(0,m.useBlockProps)({className:(0,n.A)("wc-block-components-checkout-step",o,{"wc-block-components-checkout-step--with-step-number":c})});return(0,r.createElement)("div",{...d},(0,r.createElement)(Ht,null,(0,r.createElement)(m.PlainText,{className:"",value:a,onChange:e=>t({title:e}),style:{backgroundColor:"transparent"}})),(0,r.createElement)("div",{className:"wc-block-components-checkout-step__container"},(0,r.createElement)("p",{className:"wc-block-components-checkout-step__description"},(0,r.createElement)(m.PlainText,{className:i?"":"wc-block-components-checkout-step__description-placeholder",value:i,placeholder:(0,l.__)("Optional text for this form step.","woocommerce"),onChange:e=>t({description:e}),style:{backgroundColor:"transparent"}})),(0,r.createElement)("div",{className:"wc-block-components-checkout-step__content"},s)))};o(6664);const $t=({block:e})=>{const{"data-block":t}=(0,m.useBlockProps)(),o=Dt(e);return Lt({clientId:t,registeredBlocks:o}),(0,r.createElement)("div",{className:"wc-block-checkout__additional_fields"},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:o}))},zt=()=>(0,r.createElement)(m.InnerBlocks.Content,null);var Wt=o(4375),Gt=o(8107),Zt=o(4717);const Xt=["BUTTON","FIELDSET","INPUT","OPTGROUP","OPTION","SELECT","TEXTAREA","A"],Jt=({children:e,style:t={},...o})=>{const n=(0,d.useRef)(null),s=()=>{n.current&&Gt.focus.focusable.find(n.current).forEach((e=>{Xt.includes(e.nodeName)&&e.setAttribute("tabindex","-1"),e.hasAttribute("contenteditable")&&e.setAttribute("contenteditable","false")}))},c=(0,Zt.YQ)(s,0,{leading:!0});return(0,d.useLayoutEffect)((()=>{let e;return s(),n.current&&(e=new window.MutationObserver(c),e.observe(n.current,{childList:!0,attributes:!0,subtree:!0})),()=>{e&&e.disconnect(),c.cancel()}}),[c]),(0,r.createElement)("div",{ref:n,"aria-disabled":"true",style:{userSelect:"none",pointerEvents:"none",cursor:"normal",...t},...o},e)};o(2931);var Qt=o(224);o(5452);const eo=e=>{const{onChange:t,options:o,label:s,value:c="",className:i,size:m,errorId:p,required:u,errorMessage:h=(0,l.__)("Please select a valid option","woocommerce"),placeholder:g,...E}=e,k=(0,d.useCallback)((e=>{t(e.target.value)}),[t]),w=(0,d.useMemo)((()=>({value:"",label:null!=g?g:(0,l.sprintf)(
// translators: %s will be label of the field. For example "country/region".
// translators: %s will be label of the field. For example "country/region".
(0,l.__)("Select a %s","woocommerce"),null==s?void 0:s.toLowerCase()),disabled:!!u})),[s,g,u]),b=(0,d.useId)(),y=E.id||`wc-blocks-components-select-${b}`,v=p||y,f=(0,d.useMemo)((()=>u&&c?o:[w].concat(o)),[u,c,w,o]),{setValidationErrors:C,clearValidationError:S}=(0,_.useDispatch)(te.VALIDATION_STORE_KEY),{error:A,validationErrorId:P}=(0,_.useSelect)((e=>{const t=e(te.VALIDATION_STORE_KEY);return{error:t.getValidationError(v),validationErrorId:t.getValidationErrorId(v)}}));(0,d.useEffect)((()=>(!u||c?S(v):C({[v]:{message:h,hidden:!0}}),()=>{S(v)})),[S,c,v,h,u,C]);const N=(0,_.useSelect)((e=>e(te.VALIDATION_STORE_KEY).getValidationError(v||"")||{hidden:!0}));return(0,r.createElement)("div",{className:(0,n.A)(i,{"has-error":!N.hidden})},(0,r.createElement)("div",{className:"wc-blocks-components-select"},(0,r.createElement)("div",{className:"wc-blocks-components-select__container"},(0,r.createElement)("label",{htmlFor:y,className:"wc-blocks-components-select__label"},s),(0,r.createElement)("select",{className:"wc-blocks-components-select__select",id:y,size:void 0!==m?m:1,onChange:k,value:c,"aria-invalid":!(null==A||!A.message||null!=A&&A.hidden),"aria-errormessage":P,...E},f.map((e=>(0,r.createElement)("option",{key:e.value,value:e.value,"data-alternate-values":`[${e.label}]`,disabled:void 0!==e.disabled&&e.disabled},e.label)))),(0,r.createElement)(a.A,{className:"wc-blocks-components-select__expand",icon:Qt.A}))),(0,r.createElement)(Kt.ValidationInputError,{propertyName:v}))},to=({className:e,countries:t,id:o,label:s,onChange:c,value:a="",autoComplete:i="off",required:l=!1})=>{const m=(0,d.useMemo)((()=>Object.entries(t).map((([e,t])=>({value:e,label:(0,Se.decodeEntities)(t)})))),[t]);return(0,r.createElement)(eo,{className:(0,n.A)(e,"wc-block-components-country-input"),id:o,label:s||"",onChange:c,options:m,value:a,required:l,autoComplete:i})},oo=e=>{const{...t}=e;return(0,r.createElement)(to,{countries:K,...t})},ro=e=>(0,r.createElement)(to,{countries:q,...e});o(8824);const no=(e,t)=>{const o=t.find((t=>t.label.toLocaleUpperCase()===e.toLocaleUpperCase()||t.value.toLocaleUpperCase()===e.toLocaleUpperCase()));return o?o.value:""},so=({className:e,id:t,states:o,country:s,label:c,onChange:a,autoComplete:i="off",value:l="",required:m=!1})=>{const p=o[s],u=(0,d.useMemo)((()=>p&&Object.keys(p).length>0?Object.keys(p).map((e=>({value:e,label:(0,Se.decodeEntities)(p[e])}))):[]),[p]),h=(0,d.useCallback)((e=>{const t=u.length>0?no(e,u):e;t!==l&&a(t)}),[a,u,l]),g=(0,d.useRef)(l);return(0,d.useEffect)((()=>{g.current!==l&&(g.current=l)}),[l]),(0,d.useEffect)((()=>{if(u.length>0&&g.current){const e=no(g.current,u);e!==g.current&&h(e)}}),[u,h]),u.length>0?(0,r.createElement)(eo,{className:(0,n.$)(e,"wc-block-components-state-input"),options:u,label:c||"",id:t,onChange:h,value:l,autoComplete:i,required:m}):(0,r.createElement)(Kt.ValidatedTextInput,{className:e,id:t,label:c,onChange:h,autoComplete:i,value:l,required:m})},co=e=>{const{...t}=e;return(0,r.createElement)(so,{states:H,...t})},ao=e=>(0,r.createElement)(so,{states:$,...e});function io(e){const t=(0,d.useRef)(e);return Xe()(e,t.current)||(t.current=e),t.current}var lo=o(1777);o(5684);const mo=({field:e,props:t,onChange:o,value:n})=>{var s;const c=null!==(s=null==e?void 0:e.required)&&void 0!==s&&s,[a,i]=(0,d.useState)((()=>Boolean(n)||c)),m=(0,d.useCallback)((t=>{o(e.key,t),i(!0)}),[e.key,o]);return(0,r.createElement)(d.Fragment,null,a?(0,r.createElement)(Kt.ValidatedTextInput,{...t,type:e.type,label:c?e.label:e.optionalLabel,className:`wc-block-components-address-form__${e.key}`,value:n,onChange:t=>o(e.key,t)}):(0,r.createElement)(d.Fragment,null,(0,r.createElement)(lo.$,{render:(0,r.createElement)("span",null),className:"wc-block-components-address-form__address_2-toggle",onClick:()=>i(!0)},(0,l.sprintf)(
// translators: %s: address 2 field label.
// translators: %s: address 2 field label.
(0,l.__)("+ Add %s","woocommerce"),e.label.toLowerCase())),(0,r.createElement)("input",{type:"text",tabIndex:-1,className:"wc-block-components-address-form__address_2-hidden-input","aria-hidden":"true","aria-label":e.label,autoComplete:e.autocomplete,id:null==t?void 0:t.id,value:n,onChange:e=>m(e.target.value)})))},po=(e,t,o)=>({id:`${t}-${null==e?void 0:e.key}`.replaceAll("/","-"),errorId:`${o}_${null==e?void 0:e.key}`,label:null!=e&&e.required?null==e?void 0:e.label:null==e?void 0:e.optionalLabel,autoCapitalize:null==e?void 0:e.autocapitalize,autoComplete:null==e?void 0:e.autocomplete,errorMessage:null==e?void 0:e.errorMessage,required:null==e?void 0:e.required,placeholder:null==e?void 0:e.placeholder,className:`wc-block-components-address-form__${null==e?void 0:e.key}`.replaceAll("/","-"),...null==e?void 0:e.attributes}),uo=(e,t,o)=>({field:t.find((t=>t.key===e)),value:(0,we.objectHasProp)(o,e)?o[e]:void 0}),ho=({formId:e,address1:t,address2:o,addressType:n,onChange:s})=>{var c,a,i,l;const m=t?po(t.field,e,n):void 0,d=o?po(o.field,e,n):void 0;return(0,r.createElement)(r.Fragment,null,t&&(0,r.createElement)(Kt.ValidatedTextInput,{...m,type:null===(c=t.field)||void 0===c?void 0:c.type,label:null===(a=t.field)||void 0===a?void 0:a.label,className:`wc-block-components-address-form__${null===(i=t.field)||void 0===i?void 0:i.key}`,value:t.value,onChange:e=>{var o;return s(null===(o=t.field)||void 0===o?void 0:o.key,e)}}),(null==o?void 0:o.field)&&!(null!=o&&null!==(l=o.field)&&void 0!==l&&l.hidden)&&(0,r.createElement)(mo,{field:o.field,props:d,onChange:s,value:null==o?void 0:o.value}))};function go(e){let t=e;return function(e){const o=t;return t=e,o}}const _o=go(),Eo=go(),ko=({id:e="",fields:t,fieldConfig:o={},onChange:s,addressType:c="shipping",values:a,children:i,isEditing:m})=>{const u=(0,p.useInstanceId)(ko),h=(0,d.useRef)(!0),g=io(t),E=io(o),k=io((0,we.objectHasProp)(a,"country")?a.country:""),w=(0,d.useMemo)((()=>{const e=Ne(g,E,k);return{fields:e,addressType:c,required:e.filter((e=>e.required)),hidden:e.filter((e=>e.hidden))}}),[g,E,k,c]),b=(0,d.useRef)({});return(0,d.useEffect)((()=>{const e={...a,...Object.fromEntries(w.hidden.map((e=>[e.key,""])))};Xe()(a,e)||s(e)}),[s,w,a]),(0,d.useEffect)((()=>{if((0,we.objectHasProp)(a,"country")&&((e,t)=>{const o=`${e}_country`,r=(0,_.select)(te.VALIDATION_STORE_KEY).getValidationError(o);!t.country&&(t.city||t.state||t.postcode)&&(r?(0,_.dispatch)(te.VALIDATION_STORE_KEY).showValidationError(o):(0,_.dispatch)(te.VALIDATION_STORE_KEY).setValidationErrors({[o]:{message:(0,l.__)("Please select your country","woocommerce"),hidden:!1}})),r&&t.country&&(0,_.dispatch)(te.VALIDATION_STORE_KEY).clearValidationError(o)})(c,a),(0,we.objectHasProp)(a,"state")){const e=w.fields.find((e=>"state"===e.key));e&&((e,t,o)=>{const r=`${e}_state`,n=(0,_.select)(te.VALIDATION_STORE_KEY).getValidationError(r),s=o.required,c="shipping"===e?_o(t):Eo(t),a=!!c&&!Xe()(c,t);n?!s||t.state?(0,_.dispatch)(te.VALIDATION_STORE_KEY).clearValidationError(r):a||(0,_.dispatch)(te.VALIDATION_STORE_KEY).showValidationError(r):!n&&s&&!t.state&&t.country&&(0,_.dispatch)(te.VALIDATION_STORE_KEY).setValidationErrors({[r]:{message:(0,l.sprintf)(/* translators: %s will be the state field label in lowercase e.g. "state" */ /* translators: %s will be the state field label in lowercase e.g. "state" */
(0,l.__)("Please select a %s","woocommerce"),o.label.toLowerCase()),hidden:!0}})})(c,a,e)}}),[a,c,w]),(0,d.useEffect)((()=>{var e,t;null===(e=b.current)||void 0===e||null===(t=e.postcode)||void 0===t||t.revalidate()}),[k]),(0,d.useEffect)((()=>{let t;if(!h.current&&m&&b.current){const o=w.fields.find((e=>!1===e.hidden));if(!o)return;const{id:r}=po(o,e||`${u}`,c),n=document.getElementById(r);n&&(t=setTimeout((()=>{n.focus()}),300))}return h.current=!1,()=>{clearTimeout(t)}}),[m,w,e,u,c]),e=e||`${u}`,(0,r.createElement)("div",{id:e,className:"wc-block-components-address-form"},w.fields.map((t=>{if(t.hidden)return null;const o=po(t,e,c),i=(e=>{const{errorId:t,errorMessage:o,autoCapitalize:r,autoComplete:n,placeholder:s,...c}=e;return c})(o);if("email"===t.key&&(o.id="email",o.errorId="billing_email"),"checkbox"===t.type)return(0,r.createElement)(Kt.CheckboxControl,{key:t.key,checked:Boolean(a[t.key]),onChange:e=>{s({...a,[t.key]:e})},...i});if("address_1"===t.key){const o=uo("address_1",w.fields,a),n=uo("address_2",w.fields,a);return(0,r.createElement)(ho,{address1:o,address2:n,addressType:c,formId:e,key:t.key,onChange:(e,t)=>{s({...a,[e]:t})}})}if("address_2"===t.key)return null;if("country"===t.key&&(0,we.objectHasProp)(a,"country")){const e="shipping"===c?ro:oo;return(0,r.createElement)(e,{key:t.key,...o,value:a.country,onChange:e=>{s({...a,country:e,state:"",postcode:""})}})}if("state"===t.key&&(0,we.objectHasProp)(a,"state")){const e="shipping"===c?ao:co;return(0,r.createElement)(e,{key:t.key,...o,country:a.country,value:a.state,onChange:e=>s({...a,state:e})})}return"select"===t.type?void 0===t.options?null:(0,r.createElement)(eo,{key:t.key,...o,label:o.label||"",className:(0,n.A)("wc-block-components-select-input",`wc-block-components-select-input-${t.key}`.replaceAll("/","-")),value:a[t.key],onChange:e=>{s({...a,[t.key]:e})},options:t.options,required:t.required,errorMessage:o.errorMessage||void 0}):(0,r.createElement)(Kt.ValidatedTextInput,{key:t.key,ref:e=>b.current[t.key]=e,...o,type:t.type,value:a[t.key],onChange:e=>s({...a,[t.key]:e}),customFormatter:e=>"postcode"===t.key?e.trimStart().toUpperCase():e,customValidation:e=>((e,t,o)=>!((e.required||e.value)&&("postcode"===t&&o&&!(0,Tt.isPostcode)({postcode:e.value,country:o})?(e.setCustomValidity((0,l.__)("Please enter a valid postcode","woocommerce")),1):"email"===t&&!(0,Te.isEmail)(e.value)&&(e.setCustomValidity((0,l.__)("Please enter a valid email address","woocommerce")),1))))(e,t.key,(0,we.objectHasProp)(a,"country")?a.country:"")})})),i)},wo=ko;o(7259);const bo=({isEditing:e=!1,addressCard:t,addressForm:o})=>{const s=(0,n.A)("wc-block-components-address-address-wrapper",{"is-editing":e});return(0,r.createElement)("div",{className:s},(0,r.createElement)("div",{className:"wc-block-components-address-card-wrapper"},t()),(0,r.createElement)("div",{className:"wc-block-components-address-form-wrapper"},o()))},yo=e=>(0,we.isObject)(H[e.country])&&(0,we.isString)(H[e.country][e.state])?(0,Se.decodeEntities)(H[e.country][e.state]):e.state,vo=e=>(0,we.isString)(K[e.country])?(0,Se.decodeEntities)(K[e.country]):e.country;o(1094);const fo=({address:e,onEdit:t,target:o,fieldConfig:n,isExpanded:s})=>{const c=(0,y.getSetting)("countryData",{});let a=(0,y.getSetting)("defaultAddressFormat","{name}\n{company}\n{address_1}\n{address_2}\n{city}\n{state}\n{postcode}\n{country}");(0,we.objectHasProp)(c,null==e?void 0:e.country)&&(0,we.objectHasProp)(c[e.country],"format")&&(0,we.isString)(c[e.country].format)&&(a=c[e.country].format);const{name:i,address:m}=((e,t)=>{const o=(e=>["{name}","{name_upper}","{first_name} {last_name}","{last_name} {first_name}","{first_name_upper} {last_name_upper}","{last_name_upper} {first_name_upper}","{first_name} {last_name_upper}","{first_name_upper} {last_name}","{last_name} {first_name_upper}","{last_name_upper} {first_name}"].find((t=>e.indexOf(t)>=0))||"")(t),r=t.replace(`${o}\n`,""),n=[["{company}",(null==e?void 0:e.company)||""],["{address_1}",(null==e?void 0:e.address_1)||""],["{address_2}",(null==e?void 0:e.address_2)||""],["{city}",(null==e?void 0:e.city)||""],["{state}",yo(e)],["{postcode}",(null==e?void 0:e.postcode)||""],["{country}",vo(e)],["{company_upper}",((null==e?void 0:e.company)||"").toUpperCase()],["{address_1_upper}",((null==e?void 0:e.address_1)||"").toUpperCase()],["{address_2_upper}",((null==e?void 0:e.address_2)||"").toUpperCase()],["{city_upper}",((null==e?void 0:e.city)||"").toUpperCase()],["{state_upper}",yo(e).toUpperCase()],["{state_code}",(null==e?void 0:e.state)||""],["{postcode_upper}",((null==e?void 0:e.postcode)||"").toUpperCase()],["{country_upper}",vo(e).toUpperCase()]],s=[["{name}",(null==e?void 0:e.first_name)+(null!=e&&e.first_name&&null!=e&&e.last_name?" ":"")+(null==e?void 0:e.last_name)],["{name_upper}",((null==e?void 0:e.first_name)+(null!=e&&e.first_name&&null!=e&&e.last_name?" ":"")+(null==e?void 0:e.last_name)).toUpperCase()],["{first_name}",(null==e?void 0:e.first_name)||""],["{last_name}",(null==e?void 0:e.last_name)||""],["{first_name_upper}",((null==e?void 0:e.first_name)||"").toUpperCase()],["{last_name_upper}",((null==e?void 0:e.last_name)||"").toUpperCase()]];let c=o;s.forEach((([e,t])=>{c=c.replace(e,t)}));let a=r;n.forEach((([e,t])=>{a=a.replace(e,t)}));const i=a.replace(/^,\s|,\s$/g,"").replace(/\n{2,}/,"\n").split("\n").filter(Boolean);return{name:c,address:i}})(e,a),d="shipping"===o?(0,l.__)("Edit shipping address","woocommerce"):(0,l.__)("Edit billing address","woocommerce");return(0,r.createElement)("div",{className:"wc-block-components-address-card"},(0,r.createElement)("address",null,(0,r.createElement)("span",{className:"wc-block-components-address-card__address-section"},i),(0,r.createElement)("div",{className:"wc-block-components-address-card__address-section"},m.filter((e=>!!e)).map(((e,t)=>(0,r.createElement)("span",{key:"address-"+t},e)))),e.phone&&!n.phone.hidden?(0,r.createElement)("div",{key:"address-phone",className:"wc-block-components-address-card__address-section"},e.phone):""),t&&(0,r.createElement)(lo.$,{render:(0,r.createElement)("span",null),className:"wc-block-components-address-card__edit","aria-controls":o,"aria-expanded":s,"aria-label":d,onClick:e=>{e.preventDefault(),t()},type:"button"},(0,l.__)("Edit","woocommerce")))},Co=({addressFieldsConfig:e})=>{const{shippingAddress:t,setShippingAddress:o,setBillingAddress:n,useShippingAsBilling:s,editingShippingAddress:c,setEditingShippingAddress:a}=Ut(),{dispatchCheckoutEvent:i}=nt(),{hasValidationErrors:l,invalidProps:m}=(0,_.useSelect)((e=>{const o=e(te.VALIDATION_STORE_KEY);return{hasValidationErrors:o.hasValidationErrors(),invalidProps:Object.keys(t).filter((e=>void 0!==o.getValidationError("shipping_"+e))).filter(Boolean)}}));(0,d.useEffect)((()=>{m.length>0&&!1===c&&a(!0)}),[c,l,m.length,a]);const p=(0,d.useCallback)((e=>{o(e),s&&(n(e),i("set-billing-address")),i("set-shipping-address")}),[i,n,o,s]),u=(0,d.useCallback)((()=>(0,r.createElement)(fo,{address:t,target:"shipping",onEdit:()=>{a(!0)},fieldConfig:e,isExpanded:c})),[t,e,c,a]),h=(0,d.useCallback)((()=>(0,r.createElement)(wo,{id:"shipping",addressType:"shipping",onChange:p,values:t,fields:G,fieldConfig:e,isEditing:c})),[e,p,t,c]);return(0,r.createElement)(bo,{isEditing:c,addressCard:u,addressForm:h})},So=({showCompanyField:e=!1,requireCompanyField:t=!1,showApartmentField:o=!1,requireApartmentField:n=!1,showPhoneField:s=!1,requirePhoneField:c=!1})=>{const{setBillingAddress:a,shippingAddress:i,billingAddress:m,useShippingAsBilling:p,setUseShippingAsBilling:u,setEditingBillingAddress:h}=Ut(),{isEditor:g}=k(),E=0===(0,y.getSetting)("currentUserId"),w=()=>{const t={...i};s||delete t.phone,e&&delete t.company,a(t)};(0,Wt.Su)((()=>{p&&w()}));const b=(0,d.useMemo)((()=>({company:{hidden:!e,required:t},address_2:{hidden:!o,required:n},phone:{hidden:!s,required:c}})),[e,t,o,n,s,c]),v=g?Jt:d.Fragment,f=p?[ye.SHIPPING_ADDRESS,ye.BILLING_ADDRESS]:[ye.SHIPPING_ADDRESS],{cartDataLoaded:C}=(0,_.useSelect)((e=>({cartDataLoaded:e(te.CART_STORE_KEY).hasFinishedResolution("getCartData")})));return(0,r.createElement)(d.Fragment,null,(0,r.createElement)(Kt.StoreNoticesContainer,{context:f}),(0,r.createElement)(v,null,C?(0,r.createElement)(Co,{addressFieldsConfig:b}):null),(0,r.createElement)(Kt.CheckboxControl,{className:"wc-block-checkout__use-address-for-billing",label:(0,l.__)("Use same address for billing","woocommerce"),checked:p,onChange:e=>{u(e),e?w():(h(!0),(e=>{if(!e||!E)return;const t=(e=>{const t=Ne(G,{},e.country),o=Object.assign({},e);return t.forEach((({key:t=""})=>{"country"!==t&&"state"!==t&&Re(t,e)&&(o[t]="")})),o})(e);a(t)})(m))}}))},Ao=({defaultTitle:e=(0,l.__)("Step","woocommerce"),defaultDescription:t=(0,l.__)("Step description text.","woocommerce"),defaultShowStepNumber:o=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:o}}),Po={...Ao({defaultTitle:(0,l.__)("Shipping address","woocommerce"),defaultDescription:(0,l.__)("Enter the address where you want your order delivered.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,i.registerBlockType)("woocommerce/checkout-shipping-address-block",{icon:{src:(0,r.createElement)(a.A,{icon:Vt.A,className:"wc-block-editor-components-block-icon"})},attributes:Po,edit:({attributes:e,setAttributes:t})=>{const{showCompanyField:o,requireCompanyField:s,showApartmentField:c,requireApartmentField:a,showPhoneField:i,requirePhoneField:l}=Mt(),{addressFieldControls:m}=Bt(),{showShippingFields:d}=Ut();if(!d)return null;const p=`shipping-address-${a?"visible":"hidden"}-address-2`;return(0,r.createElement)(qt,{setAttributes:t,attributes:e,className:(0,n.A)("wc-block-checkout__shipping-fields",null==e?void 0:e.className)},(0,r.createElement)(m,null),(0,r.createElement)(So,{key:p,showCompanyField:o,requireCompanyField:s,showApartmentField:c,requireApartmentField:a,showPhoneField:i,requirePhoneField:l}),(0,r.createElement)($t,{block:Tt.innerBlockAreas.SHIPPING_ADDRESS}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});var No=o(3813);o(8845);const To=D?`<a href="${D}" target="_blank">${(0,l.__)("Terms and Conditions","woocommerce")}</a>`:(0,l.__)("Terms and Conditions","woocommerce"),Ro=F?`<a href="${F}" target="_blank">${(0,l.__)("Privacy Policy","woocommerce")}</a>`:(0,l.__)("Privacy Policy","woocommerce"),xo=(0,l.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,l.__)("By proceeding with your purchase you agree to our %1$s and %2$s","woocommerce"),To,Ro),Io=(0,l.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,l.__)("You must accept our %1$s and %2$s to continue with your purchase.","woocommerce"),To,Ro);o(5609),(0,i.registerBlockType)("woocommerce/checkout-terms-block",{icon:{src:(0,r.createElement)(a.A,{icon:No.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:{checkbox:e,text:t,showSeparator:o},setAttributes:s})=>{const c=(0,m.useBlockProps)(),a=t||(e?Io:xo);return(0,r.createElement)("div",{...c},(0,r.createElement)(m.InspectorControls,null,(!D||!F)&&(0,r.createElement)(Nt.Notice,{className:"wc-block-checkout__terms_notice",status:"warning",isDismissible:!1},(0,l.__)("Link to your store's Terms and Conditions and Privacy Policy pages by creating pages for them.","woocommerce"),(0,r.createElement)("br",null),!D&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("br",null),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=advanced`},(0,l.__)("Setup a Terms and Conditions page","woocommerce"))),!F&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("br",null),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}options-privacy.php`},(0,l.__)("Setup a Privacy Policy page","woocommerce")))),D&&F&&!(a.includes(D)&&a.includes(F))&&(0,r.createElement)(Nt.Notice,{className:"wc-block-checkout__terms_notice",status:"warning",isDismissible:!1,actions:xo!==t?[{label:(0,l.__)("Restore default text","woocommerce"),onClick:()=>s({text:""})}]:[]},(0,r.createElement)("p",null,(0,l.__)("Ensure you add links to your policy pages in this section.","woocommerce"))),(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Display options","woocommerce")},(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Require checkbox","woocommerce"),checked:e,onChange:()=>s({checkbox:!e})}),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Show separator","woocommerce"),checked:o,onChange:()=>s({showSeparator:!o})}))),(0,r.createElement)("div",{className:(0,n.A)("wc-block-checkout__terms",{"wc-block-checkout__terms--with-separator":o})},e?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.CheckboxControl,{id:"terms-condition",checked:!1}),(0,r.createElement)(m.RichText,{value:a,onChange:e=>s({text:e})})):(0,r.createElement)(m.RichText,{tagName:"span",value:a,onChange:e=>s({text:e})})))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});var Oo=o(2596),Mo=o(3876);o(3091);const Bo=[(0,l.__)("Too weak","woocommerce"),(0,l.__)("Weak","woocommerce"),(0,l.__)("Medium","woocommerce"),(0,l.__)("Strong","woocommerce"),(0,l.__)("Very strong","woocommerce")],Fo=({password:e="",onChange:t})=>{var o;const s=(0,p.useInstanceId)(Fo,"woocommerce-password-strength-meter");let c=-1;if(e.length>0)if(void 0===window.zxcvbn){const t=(0,Mo.Bi)(e);c=t.id}else{const t=window.zxcvbn(e);c=t.score}const a=pt(c);return(0,d.useEffect)((()=>{c!==a&&t&&t(c)}),[c,a,t]),(0,r.createElement)("div",{id:s,className:(0,n.A)("wc-block-components-password-strength",{hidden:-1===c})},(0,r.createElement)("label",{htmlFor:s+"-meter",className:"screen-reader-text"},(0,l.__)("Password strength","woocommerce")),(0,r.createElement)("meter",{id:s+"-meter",className:"wc-block-components-password-strength__meter",min:0,max:4,value:c>-1?c:0},null!==(o=Bo[c])&&void 0!==o?o:""),(0,r.createElement)("div",{id:s+"-result",className:"wc-block-components-password-strength__result"},!!Bo[c]&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"screen-reader-text","aria-live":"polite"},(0,l.sprintf)(/* translators: %s: Password strength */ /* translators: %s: Password strength */
(0,l.__)("Password strength: %1$s (%2$d characters long)","woocommerce"),Bo[c],e.length))," ",(0,r.createElement)("span",{"aria-hidden":!0},Bo[c]))))},Do=Fo,Lo=()=>{const[e,t]=(0,d.useState)(0),{customerPassword:o}=(0,_.useSelect)((e=>({customerPassword:e(te.CHECKOUT_STORE_KEY).getCustomerPassword()}))),{__internalSetCustomerPassword:n}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY);return(0,r.createElement)(Kt.ValidatedTextInput,{type:"password",label:(0,l.__)("Create a password","woocommerce"),className:"wc-block-components-address-form__password",value:o,required:!0,errorId:"account-password",customValidityMessage:e=>{if(e.valueMissing||e.badInput||e.typeMismatch)return(0,l.__)("Please enter a valid password","woocommerce")},customValidation:t=>!(e<2&&(t.setCustomValidity((0,l.__)("Please create a stronger password","woocommerce")),1)),onChange:e=>n(e),feedback:(0,r.createElement)(Do,{password:o,onChange:e=>t(e)})})},Yo=()=>{const{shouldCreateAccount:e}=(0,_.useSelect)((e=>({shouldCreateAccount:e(te.CHECKOUT_STORE_KEY).getShouldCreateAccount()}))),{__internalSetShouldCreateAccount:t,__internalSetCustomerPassword:o}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),n=(0,y.getSetting)("checkoutAllowsGuest",!1),s=(0,y.getSetting)("checkoutAllowsSignup",!1),c=n&&s,a=!(0,y.getSetting)("generatePassword",!1)&&(c&&e||!n);return n||c||a?(0,r.createElement)(r.Fragment,null,n&&(0,r.createElement)("p",{className:"wc-block-checkout__guest-checkout-notice"},(0,l.__)("You are currently checking out as a guest.","woocommerce")),c&&(0,r.createElement)(Kt.CheckboxControl,{className:"wc-block-checkout__create-account",label:(0,l.sprintf)(/* translators: Store name */ /* translators: Store name */
(0,l.__)("Create an account with %s","woocommerce"),(0,y.getSetting)("siteTitle","")),checked:e,onChange:e=>{t(e),o("")}}),a&&(0,r.createElement)(Lo,null)):null},Vo=()=>{const{additionalFields:e,customerId:t}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId()}})),{setAdditionalFields:o}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),{billingAddress:n,setEmail:s}=Ut(),{dispatchCheckoutEvent:c}=nt(),a={email:n.email,...e};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.StoreNoticesContainer,{context:ye.CONTACT_INFORMATION}),(0,r.createElement)(wo,{id:"contact",addressType:"contact",onChange:e=>{const{email:t,...r}=e;s(t),c("set-email-address"),o(r)},values:a,fields:Z},!t&&(0,r.createElement)(Yo,null)))},jo={...Ao({defaultTitle:(0,l.__)("Contact information","woocommerce"),defaultDescription:(0,l.__)("We'll use this email to send you details and updates about your order.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{remove:!0,move:!0}}};(0,i.registerBlockType)("woocommerce/checkout-contact-information-block",{icon:{src:(0,r.createElement)(a.A,{icon:Oo.A,className:"wc-block-editor-components-block-icon"})},attributes:jo,edit:({attributes:e,setAttributes:t})=>(0,r.createElement)(qt,{attributes:e,setAttributes:t,className:(0,n.A)("wc-block-checkout__contact-fields",null==e?void 0:e.className)},(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Account creation and guest checkout","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Account creation and guest checkout settings can be managed in your store settings.","woocommerce")),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=account`},(0,l.__)("Manage account settings","woocommerce")))),(0,r.createElement)(Jt,null,(0,r.createElement)(Vo,null)),(0,r.createElement)($t,{block:Tt.innerBlockAreas.CONTACT_INFORMATION})),save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});const Uo=({addressFieldsConfig:e})=>{const{billingAddress:t,setShippingAddress:o,setBillingAddress:n,useBillingAsShipping:s,editingBillingAddress:c,setEditingBillingAddress:a}=Ut(),{dispatchCheckoutEvent:i}=nt(),{hasValidationErrors:l,invalidProps:m}=(0,_.useSelect)((e=>{const o=e(te.VALIDATION_STORE_KEY);return{hasValidationErrors:o.hasValidationErrors(),invalidProps:Object.keys(t).filter((e=>"email"!==e&&void 0!==o.getValidationError("billing_"+e))).filter(Boolean)}}));(0,d.useEffect)((()=>{m.length>0&&!1===c&&a(!0)}),[c,l,m.length,a]);const p=(0,d.useCallback)((e=>{n(e),s&&(o(e),i("set-shipping-address")),i("set-billing-address")}),[i,n,o,s]),u=(0,d.useCallback)((()=>(0,r.createElement)(fo,{address:t,target:"billing",onEdit:()=>{a(!0)},fieldConfig:e,isExpanded:c})),[t,e,c,a]),h=(0,d.useCallback)((()=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)(wo,{id:"billing",addressType:"billing",onChange:p,values:t,fields:G,fieldConfig:e,isEditing:c}))),[e,t,p,c]);return(0,r.createElement)(bo,{isEditing:c,addressCard:u,addressForm:h})},Ko=({showCompanyField:e=!1,requireCompanyField:t=!1,showApartmentField:o=!1,requireApartmentField:n=!1,showPhoneField:s=!1,requirePhoneField:c=!1})=>{const{billingAddress:a,setShippingAddress:i,useBillingAsShipping:l}=Ut(),{isEditor:m}=k();(0,Wt.Su)((()=>{if(l){const{email:t,...o}=a,r={...o};s||delete r.phone,e&&delete r.company,i(r)}}));const p=(0,d.useMemo)((()=>({company:{hidden:!e,required:t},address_2:{hidden:!o,required:n},phone:{hidden:!s,required:c}})),[e,t,o,n,s,c]),u=m?Jt:d.Fragment,h=l?[ye.BILLING_ADDRESS,ye.SHIPPING_ADDRESS]:[ye.BILLING_ADDRESS],{cartDataLoaded:g}=(0,_.useSelect)((e=>({cartDataLoaded:e(te.CART_STORE_KEY).hasFinishedResolution("getCartData")})));return(0,r.createElement)(d.Fragment,null,(0,r.createElement)(Kt.StoreNoticesContainer,{context:h}),(0,r.createElement)(u,null,g?(0,r.createElement)(Uo,{addressFieldsConfig:p}):null))},Ho=(0,l.__)("Billing address","woocommerce"),qo=(0,l.__)("Enter the billing address that matches your payment method.","woocommerce"),$o=(0,l.__)("Billing and shipping address","woocommerce"),zo=(0,l.__)("Enter the billing and shipping address that matches your payment method.","woocommerce"),Wo={...Ao({defaultTitle:Ho,defaultDescription:qo}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,i.registerBlockType)("woocommerce/checkout-billing-address-block",{icon:{src:(0,r.createElement)(a.A,{icon:Vt.A,className:"wc-block-editor-components-block-icon"})},attributes:Wo,edit:({attributes:e,setAttributes:t})=>{const{showCompanyField:o,requireCompanyField:s,showApartmentField:c,requireApartmentField:a,showPhoneField:i,requirePhoneField:l}=Mt(),{addressFieldControls:m}=Bt(),{showBillingFields:d,forcedBillingAddress:p,useBillingAsShipping:u}=Ut();if(!d&&!u)return null;e.title=((e,t)=>t?e===Ho?$o:e:e===$o?Ho:e)(e.title,p),e.description=((e,t)=>t?e===qo?zo:e:e===zo?qo:e)(e.description,p);const h=`billing-address-${a?"visible":"hidden"}-address-2`;return(0,r.createElement)(qt,{setAttributes:t,attributes:e,className:(0,n.A)("wc-block-checkout__billing-fields",null==e?void 0:e.className)},(0,r.createElement)(m,null),(0,r.createElement)(Ko,{key:h,showCompanyField:o,requireCompanyField:s,showApartmentField:c,requireApartmentField:a,showPhoneField:i,requirePhoneField:l}),(0,r.createElement)($t,{block:Tt.innerBlockAreas.BILLING_ADDRESS}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});var Go=o(498);const Zo=(0,l.__)("Place Order","woocommerce"),Xo={cartPageId:{type:"number",default:0},showReturnToCart:{type:"boolean",default:!1},className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}},placeOrderButtonLabel:{type:"string",default:Zo}},Jo=(e,t)=>{if(!e.title.raw)return e.slug;const o=1===t.filter((t=>t.title.raw===e.title.raw)).length;return e.title.raw+(o?"":` - ${e.slug}`)},Qo=({setPageId:e,pageId:t,labels:o})=>{const n=(0,_.useSelect)((e=>e("core").getEntityRecords("postType","page",{status:"publish",orderby:"title",order:"asc",per_page:100})),[])||null;return n?(0,r.createElement)(Nt.PanelBody,{title:o.title},(0,r.createElement)(Nt.SelectControl,{label:(0,l.__)("Link to","woocommerce"),value:t,options:[{label:o.default,value:0},...n.map((e=>({label:Jo(e,n),value:parseInt(e.id,10)})))],onChange:t=>e(parseInt(t,10))})):null};var er=o(1387);o(8337);const tr=({link:e})=>{const t=e||Y;return t?(0,r.createElement)("a",{href:t,className:"wc-block-components-checkout-return-to-cart-button"},(0,r.createElement)(a.A,{icon:er.A}),(0,l.__)("Return to Cart","woocommerce")):null};o(2080),o(7791);const or=()=>(0,r.createElement)("span",{className:"wc-block-components-spinner","aria-hidden":"true"}),rr=(0,d.forwardRef)(((e,t)=>{"showSpinner"in e&&re()("showSpinner prop",{version:"8.9.0",alternative:"Render a spinner in the button children instead.",plugin:"WooCommerce"});const{className:o,showSpinner:s=!1,children:c,variant:a="contained",removeTextWrap:i=!1,...l}=e,m=(0,n.A)("wc-block-components-button","wp-element-button",o,a,{"wc-block-components-button--loading":s});if("href"in e)return(0,r.createElement)(lo.$,{render:(0,r.createElement)("a",{ref:t,href:e.href},s&&(0,r.createElement)(or,null),(0,r.createElement)("span",{className:"wc-block-components-button__text"},c)),className:m,...l});const d=i?e.children:(0,r.createElement)("span",{className:"wc-block-components-button__text"},e.children);return(0,r.createElement)(lo.$,{ref:t,className:m,...l},s&&(0,r.createElement)(or,null),d)})),nr=({onChange:e,placeholder:t,value:o,...n})=>(0,r.createElement)(rr,{...n},(0,r.createElement)(m.RichText,{multiline:!1,allowedFormats:[],value:o,placeholder:t,onChange:e}));o(5089);const sr={icon:{src:(0,r.createElement)(a.A,{icon:Go.A,className:"wc-block-editor-components-block-icon"})},attributes:Xo,save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()}),edit:({attributes:e,setAttributes:t})=>{const o=(0,m.useBlockProps)(),{cartPageId:s=0,showReturnToCart:c=!1,placeOrderButtonLabel:a}=e,{current:i}=(0,d.useRef)(s),p=(0,_.useSelect)((e=>i||e("core/editor").getCurrentPostId()),[i]);return(0,r.createElement)("div",{...o},(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Navigation options","woocommerce")},(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)('Show a "Return to Cart" link',"woocommerce"),help:(0,l.__)("Recommended to enable only if there is no Cart link in the header.","woocommerce"),checked:c,onChange:()=>t({showReturnToCart:!c})})),c&&!(p===B&&0===i)&&(0,r.createElement)(Qo,{pageId:s,setPageId:e=>t({cartPageId:e}),labels:{title:(0,l.__)("Return to Cart button","woocommerce"),default:(0,l.__)("WooCommerce Cart Page","woocommerce")}})),(0,r.createElement)("div",{className:"wc-block-checkout__actions"},(0,r.createElement)("div",{className:"wc-block-checkout__actions_row"},(0,r.createElement)(Jt,null,c&&(0,r.createElement)(tr,{link:(0,y.getSetting)("page-"+s,!1)})),(0,r.createElement)(nr,{className:(0,n.A)("wc-block-cart__submit-button","wc-block-components-checkout-place-order-button",{"wc-block-components-checkout-place-order-button--full-width":!c}),value:a,placeholder:Zo,onChange:e=>{t({placeOrderButtonLabel:e})}}))))}};(0,i.registerBlockType)("woocommerce/checkout-actions-block",sr);const cr=()=>{const{additionalFields:e}=(0,_.useSelect)((e=>({additionalFields:e(te.CHECKOUT_STORE_KEY).getAdditionalFields()}))),{setAdditionalFields:t}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),o={...e};return 0===X.length?null:(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.StoreNoticesContainer,{context:ye.ORDER_INFORMATION}),(0,r.createElement)(wo,{id:"additional-information",addressType:"additional-information",onChange:e=>{t(e)},values:o,fields:X}))},ar={...Ao({defaultTitle:(0,l.__)("Additional order information","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!1,remove:!0}}};(0,i.registerBlockType)("woocommerce/checkout-additional-information-block",{attributes:ar,icon:{src:(0,r.createElement)(a.A,{icon:No.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>0===X.length?null:(0,r.createElement)(qt,{setAttributes:t,attributes:e,className:(0,n.A)("wc-block-checkout__additional-information-fields",null==e?void 0:e.className)},(0,r.createElement)(cr,null)),save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});var ir=o(6465);const lr=({disabled:e,onChange:t,placeholder:o,value:n})=>{const[s,c]=(0,d.useState)(!1),[a,i]=(0,d.useState)("");return(0,r.createElement)("div",{className:"wc-block-checkout__add-note"},(0,r.createElement)(Kt.CheckboxControl,{disabled:e,label:(0,l.__)("Add a note to your order","woocommerce"),checked:s,onChange:e=>{c(e),e?n!==a&&t(a):(t(""),i(n))}}),s&&(0,r.createElement)(Kt.Textarea,{disabled:e,onTextChange:t,placeholder:o,value:n}))},mr=({className:e})=>{const{needsShipping:t}=st(),{isProcessing:o,orderNotes:s}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes()}})),{__internalSetOrderNotes:c}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY);return(0,r.createElement)(Kt.FormStep,{id:"order-notes",showStepNumber:!1,className:(0,n.A)("wc-block-checkout__order-notes",e),disabled:o},(0,r.createElement)(lr,{disabled:o,onChange:c,placeholder:t?(0,l.__)("Notes about your order, e.g. special notes for delivery.","woocommerce"):(0,l.__)("Notes about your order.","woocommerce"),value:s}))};o(377),o(7797),(0,i.registerBlockType)("woocommerce/checkout-order-note-block",{icon:{src:(0,r.createElement)(a.A,{icon:ir.A,className:"wc-block-editor-components-block-icon"})},edit:()=>{const e=(0,m.useBlockProps)();return(0,r.createElement)("div",{...e},(0,r.createElement)(Jt,null,(0,r.createElement)(mr,null)))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const dr=(0,r.createElement)(s.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",fill:"none"},(0,r.createElement)("path",{stroke:"currentColor",strokeWidth:"1.5",fill:"none",d:"M6 3.75h12c.69 0 1.25.56 1.25 1.25v14c0 .69-.56 1.25-1.25 1.25H6c-.69 0-1.25-.56-1.25-1.25V5c0-.69.56-1.25 1.25-1.25z"}),(0,r.createElement)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.9 7.5A1.1 1.1 0 018 6.4h8a1.1 1.1 0 011.1 1.1v2a1.1 1.1 0 01-1.1 1.1H8a1.1 1.1 0 01-1.1-1.1v-2zm1.2.1v1.8h7.8V7.6H8.1z",clipRule:"evenodd"}),(0,r.createElement)("path",{fill:"currentColor",d:"M8.5 12h1v1h-1v-1zM8.5 14h1v1h-1v-1zM8.5 16h1v1h-1v-1zM11.5 12h1v1h-1v-1zM11.5 14h1v1h-1v-1zM11.5 16h1v1h-1v-1zM14.5 12h1v1h-1v-1zM14.5 14h1v1h-1v-1zM14.5 16h1v1h-1v-1z"}));o(8375);const pr=({children:e,className:t,screenReaderLabel:o,showSpinner:s=!1,isLoading:c=!0})=>(0,r.createElement)("div",{className:(0,n.A)(t,{"wc-block-components-loading-mask":c})},c&&s&&(0,r.createElement)(Kt.Spinner,null),(0,r.createElement)("div",{className:(0,n.A)({"wc-block-components-loading-mask__children":c}),"aria-hidden":c},e),c&&(0,r.createElement)("span",{className:"screen-reader-text"},o||(0,l.__)("Loading…","woocommerce")));o(3048);const ur=(0,p.withInstanceId)((({instanceId:e,isLoading:t=!1,onSubmit:o,displayCouponForm:n=!1})=>{const[s,c]=(0,d.useState)(""),[a,i]=(0,d.useState)(n),m=`wc-block-components-totals-coupon__input-${e}`,{validationErrorId:p}=(0,_.useSelect)((e=>({validationErrorId:e(te.VALIDATION_STORE_KEY).getValidationErrorId(m)}))),u=(0,d.useRef)(null);return(0,r.createElement)(Kt.Panel,{className:"wc-block-components-totals-coupon",initialOpen:a,hasBorder:!1,title:(0,l.__)("Add a coupon","woocommerce"),state:[a,i]},(0,r.createElement)(pr,{screenReaderLabel:(0,l.__)("Applying coupon…","woocommerce"),isLoading:t,showSpinner:!1},(0,r.createElement)("div",{className:"wc-block-components-totals-coupon__content"},(0,r.createElement)("form",{className:"wc-block-components-totals-coupon__form",id:"wc-block-components-totals-coupon__form"},(0,r.createElement)(Kt.ValidatedTextInput,{id:m,errorId:"coupon",className:"wc-block-components-totals-coupon__input",label:(0,l.__)("Enter code","woocommerce"),value:s,ariaDescribedBy:p,onChange:e=>{c(e)},focusOnMount:!0,validateOnMount:!1,showError:!1,ref:u}),(0,r.createElement)(rr,{className:"wc-block-components-totals-coupon__button",disabled:t||!s,showSpinner:t,onClick:e=>{var t;e.preventDefault(),void 0!==o?null===(t=o(s))||void 0===t||t.then((e=>{var t;e?(c(""),i(!1)):null!==(t=u.current)&&void 0!==t&&t.focus&&u.current.focus()})):(c(""),i(!0))},type:"submit"},(0,l.__)("Apply","woocommerce"))),(0,r.createElement)(Kt.ValidationInputError,{propertyName:"coupon",elementId:m}))))}));o(265);const hr={context:"summary"},gr=({cartCoupons:e=[],currency:t,isRemovingCoupon:o,removeCoupon:n,values:s})=>{const{total_discount:c,total_discount_tax:a}=s,i=parseInt(c,10),m=(0,Tt.applyCheckoutFilter)({arg:hr,filterName:"coupons",defaultValue:e});if(!i&&0===m.length)return null;const d=parseInt(a,10),p=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?i+d:i;return(0,r.createElement)(Kt.TotalsItem,{className:"wc-block-components-totals-discount",currency:t,description:0!==m.length&&(0,r.createElement)(pr,{screenReaderLabel:(0,l.__)("Removing coupon…","woocommerce"),isLoading:o,showSpinner:!1},(0,r.createElement)("ul",{className:"wc-block-components-totals-discount__coupon-list"},m.map((e=>(0,r.createElement)(Kt.RemovableChip,{key:"coupon-"+e.code,className:"wc-block-components-totals-discount__coupon-list-item",text:e.label,screenReaderText:(0,l.sprintf)(/* translators: %s Coupon code. */ /* translators: %s Coupon code. */
(0,l.__)("Coupon: %s","woocommerce"),e.label),disabled:o,onRemove:()=>{n(e.code)},radius:"large",ariaLabel:(0,l.sprintf)(/* translators: %s is a coupon code. */ /* translators: %s is a coupon code. */
(0,l.__)('Remove coupon "%s"',"woocommerce"),e.label)}))))),label:p?(0,l.__)("Discount","woocommerce"):(0,l.__)("Coupons","woocommerce"),value:p?-1*p:"-"})},_r=window.wc.priceFormat;o(7919);const Er=({currency:e,values:t,className:o})=>{const s=(0,y.getSetting)("taxesEnabled",!0)&&(0,y.getSetting)("displayCartPricesIncludingTax",!1),{total_price:c,total_tax:a,tax_lines:i}=t,{receiveCart:m,...p}=He(),u=(0,Tt.applyCheckoutFilter)({filterName:"totalLabel",defaultValue:(0,l.__)("Total","woocommerce"),extensions:p.extensions,arg:{cart:p}}),h=(0,Tt.applyCheckoutFilter)({filterName:"totalValue",defaultValue:"<price/>",extensions:p.extensions,arg:{cart:p},validation:Tt.productPriceValidation}),g=(0,r.createElement)(Kt.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:parseInt(c,10)}),_=(0,d.createInterpolateElement)(h,{price:g}),E=parseInt(a,10),k=i&&i.length>0?(0,l.sprintf)(/* translators: %s is a list of tax rates */ /* translators: %s is a list of tax rates */
(0,l.__)("Including %s","woocommerce"),i.map((({name:t,price:o})=>`${(0,_r.formatPrice)(o,e)} ${t}`)).join(", ")):(0,l.__)("Including <TaxAmount/> in taxes","woocommerce");return(0,r.createElement)(Kt.TotalsItem,{className:(0,n.A)("wc-block-components-totals-footer-item",o),currency:e,label:u,value:_,description:s&&0!==E&&(0,r.createElement)("p",{className:"wc-block-components-totals-footer-item-tax"},(0,d.createInterpolateElement)(k,{TaxAmount:(0,r.createElement)(Kt.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:E})}))})},kr=({selectedShippingRates:e})=>(0,r.createElement)("div",{className:"wc-block-components-totals-item__description wc-block-components-totals-shipping__via"},(0,Se.decodeEntities)(e.filter(((t,o)=>e.indexOf(t)===o)).join(", ")));let wr=null;o(7051);const br=({address:e,onUpdate:t,onCancel:o,addressFields:n})=>{const[s,c]=(0,d.useState)(e),{showAllValidationErrors:a}=(0,_.useDispatch)(te.VALIDATION_STORE_KEY),i=function(e){const t=(0,d.useRef)(null),o=(0,d.useRef)(null),r=(0,d.useRef)(e);return(0,d.useEffect)((()=>{r.current=e}),[e]),(0,d.useCallback)((e=>{if(e)t.current=e,o.current=e.ownerDocument.activeElement;else if(o.current){var n,s,c;const e=null===(n=t.current)||void 0===n?void 0:n.contains(null===(s=t.current)||void 0===s?void 0:s.ownerDocument.activeElement);var a;if(null!==(c=t.current)&&void 0!==c&&c.isConnected&&!e&&(null!==(a=wr)&&void 0!==a||(wr=o.current)),r.current)r.current();else{var i;const e=o.current;null===(i=null!=e&&e.isConnected?e:wr)||void 0===i||i.focus()}wr=null}}),[])}(),{hasValidationErrors:m,isCustomerDataUpdating:p}=(0,_.useSelect)((e=>({hasValidationErrors:e(te.VALIDATION_STORE_KEY).hasValidationErrors,isCustomerDataUpdating:e(te.CART_STORE_KEY).isCustomerDataUpdating()})));return(0,r.createElement)("form",{className:"wc-block-components-shipping-calculator-address",ref:i},(0,r.createElement)(wo,{fields:n,onChange:c,values:s}),(0,r.createElement)(rr,{className:"wc-block-components-shipping-calculator-address__button",disabled:p,onClick:r=>{if(r.preventDefault(),Xe()(s,e))return o();if(a(),!m()){const e={};return n.forEach((t=>{void 0!==s[t]&&(e[t]=s[t])})),t(e)}},type:"submit"},(0,l.__)("Update","woocommerce")))},yr=({onUpdate:e=(()=>{}),onCancel:t=(()=>{}),addressFields:o=["country","state","city","postcode"]})=>{const{shippingAddress:n}=jt(),s="wc/cart/shipping-calculator";return(0,r.createElement)("div",{className:"wc-block-components-shipping-calculator"},(0,r.createElement)(Kt.StoreNoticesContainer,{context:s}),(0,r.createElement)(br,{address:n,addressFields:o,onCancel:t,onUpdate:t=>{(0,_.dispatch)(te.CART_STORE_KEY).updateCustomerData({shipping_address:t},!1).then((()=>{(e=>{const{removeNotice:t}=(0,_.dispatch)("core/notices"),{getNotices:o}=(0,_.select)("core/notices");o(e).forEach((o=>{t(o.id,e)}))})(s),e(t)})).catch((e=>{(0,te.processErrorResponse)(e,s)}))}}))},vr=({label:e=(0,l.__)("Calculate","woocommerce"),isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o})=>(0,r.createElement)("button",{className:"wc-block-components-totals-shipping__change-address__link",id:"wc-block-components-totals-shipping__change-address__link",onClick:e=>{e.preventDefault(),o(!t)},"aria-label":e,"aria-expanded":t},e),fr=({showCalculator:e,isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o,isCheckout:n=!1})=>e?(0,r.createElement)(vr,{label:(0,l.__)("Enter address to check delivery options","woocommerce"),isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o}):(0,r.createElement)("em",null,n?(0,l.__)("No shipping options available","woocommerce"):(0,l.__)("Calculated during checkout","woocommerce")),Cr=()=>{const{pickupAddress:e}=(0,_.useSelect)((e=>{const t=e("wc/store/cart").getShippingRates().flatMap((e=>e.shipping_rates)).find((e=>e.selected&&ze(e)));if((0,we.isObject)(t)&&(0,we.objectHasProp)(t,"meta_data")){const e=t.meta_data.find((e=>"pickup_address"===e.key));if((0,we.isObject)(e)&&(0,we.objectHasProp)(e,"value")&&e.value)return{pickupAddress:e.value}}return(0,we.isObject)(t),{pickupAddress:void 0}}));return void 0===e?null:(0,r.createElement)("span",{className:"wc-block-components-shipping-address"},(0,l.sprintf)(/* translators: %s: shipping method name, e.g. "Amazon Locker" */ /* translators: %s: shipping method name, e.g. "Amazon Locker" */
(0,l.__)("Collection from %s","woocommerce"),e)+" ")},Sr=({formattedLocation:e})=>e?(0,r.createElement)("span",{className:"wc-block-components-shipping-address"},(0,l.sprintf)(/* translators: %s location. */ /* translators: %s location. */
(0,l.__)("Delivers to %s","woocommerce"),e)+" "):null,Ar=({showCalculator:e,isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o,shippingAddress:n})=>{const s=(0,_.useSelect)((e=>e(te.CHECKOUT_STORE_KEY).prefersCollection())),c=Ie(n)?(0,l.__)("Change address","woocommerce"):(0,l.__)("Enter address to check delivery options","woocommerce"),a=Ie(n);return(0,r.createElement)(r.Fragment,null,s?(0,r.createElement)(Cr,null):(0,r.createElement)(Sr,{formattedLocation:a}),e&&(0,r.createElement)(vr,{label:c,isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o}))};var Pr=o(1208),Nr=(o(9345),o(2900)),Tr=o(2478),Rr=o(8306);const xr=e=>{switch(e){case"success":case"warning":case"info":case"default":return"polite";default:return"assertive"}},Ir=e=>{switch(e){case"success":return Nr.A;case"warning":case"info":case"error":return Tr.A;default:return Rr.A}};var Or=o(195);const Mr=({className:e,status:t="default",children:o,spokenMessage:s=o,onRemove:c=(()=>{}),isDismissible:i=!0,politeness:m=xr(t),summary:p})=>(((e,t)=>{const o="string"==typeof e?e:(0,d.renderToString)(e);(0,d.useEffect)((()=>{o&&(0,Or.speak)(o,t)}),[o,t])})(s,m),(0,r.createElement)("div",{className:(0,n.A)(e,"wc-block-components-notice-banner","is-"+t,{"is-dismissible":i})},(0,r.createElement)(a.A,{icon:Ir(t)}),(0,r.createElement)("div",{className:"wc-block-components-notice-banner__content"},p&&(0,r.createElement)("p",{className:"wc-block-components-notice-banner__summary"},p),o),!!i&&(0,r.createElement)(rr,{className:"wc-block-components-notice-banner__dismiss","aria-label":(0,l.__)("Dismiss this notice","woocommerce"),onClick:e=>{"function"==typeof(null==e?void 0:e.preventDefault)&&e.preventDefault&&e.preventDefault(),c()},removeTextWrap:!0},(0,r.createElement)(a.A,{icon:Pr.A}))));var Br=o(1359),Fr=o.n(Br);const Dr=["a","b","em","i","strong","p","br"],Lr=["target","href","rel","name","download"],Yr=(e,t)=>{const o=(null==t?void 0:t.tags)||Dr,r=(null==t?void 0:t.attr)||Lr;return Fr().sanitize(e,{ALLOWED_TAGS:o,ALLOWED_ATTR:r})},Vr=e=>{const t=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10);let o=(0,r.createElement)(r.Fragment,null,Number.isFinite(t)&&(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:(0,_r.getCurrencyFromPriceResponse)(e),value:t}),Number.isFinite(t)&&e.delivery_time?" — ":null,(0,Se.decodeEntities)(e.delivery_time));return 0===t&&(o=(0,r.createElement)("span",{className:"wc-block-components-shipping-rates-control__package__description--free"},(0,l.__)("Free","woocommerce"))),{label:(0,Se.decodeEntities)(e.name),value:e.rate_id,description:o}},jr=({className:e="",noResultsMessage:t,onSelectRate:o,rates:n,renderOption:s=Vr,selectedRate:c,disabled:a=!1,highlightChecked:i=!1})=>{const l=(null==c?void 0:c.rate_id)||"",m=pt(l),[p,u]=(0,d.useState)(null!=l?l:"");return(0,d.useEffect)((()=>{l&&l!==m&&l!==p&&u(l)}),[l,p,m]),(0,d.useEffect)((()=>{!p&&n.length>0&&(u(n[0].rate_id),o(n[0].rate_id))}),[o,n,p]),0===n.length?t:(0,r.createElement)(Kt.RadioControl,{className:e,onChange:e=>{u(e),o(e)},highlightChecked:i,disabled:a,selected:p,options:n.map(s)})};o(2867);const Ur=({packageId:e,className:t="",noResultsMessage:o,renderOption:s,packageData:c,collapsible:a,showItems:i,highlightChecked:m=!1})=>{var p;const{selectShippingRate:u,isSelectingRate:h}=st(),g=(0,_.useSelect)((e=>{var t,o,r;return null===(t=e(te.CART_STORE_KEY))||void 0===t||null===(o=t.getCartData())||void 0===o||null===(r=o.shippingRates)||void 0===r?void 0:r.length}))>1||document.querySelectorAll(".wc-block-components-shipping-rates-control__package").length>1,E=null!=i?i:g,k=null!=a?a:g,w=(0,r.createElement)(r.Fragment,null,(k||E)&&(0,r.createElement)("div",{className:"wc-block-components-shipping-rates-control__package-title",dangerouslySetInnerHTML:{__html:Yr(c.name)}}),E&&(0,r.createElement)("ul",{className:"wc-block-components-shipping-rates-control__package-items"},Object.values(c.items).map((e=>{const t=(0,Se.decodeEntities)(e.name),o=e.quantity;return(0,r.createElement)("li",{key:e.key,className:"wc-block-components-shipping-rates-control__package-item"},(0,r.createElement)(Kt.Label,{label:o>1?`${t} × ${o}`:`${t}`,screenReaderLabel:(0,l.sprintf)(/* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */ /* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */
(0,l._n)("%1$s (%2$d unit)","%1$s (%2$d units)",o,"woocommerce"),t,o)}))})))),b=(0,d.useCallback)((t=>{u(t,e)}),[e,u]),y={className:t,noResultsMessage:o,rates:c.shipping_rates,onSelectRate:b,selectedRate:c.shipping_rates.find((e=>e.selected)),renderOption:s,disabled:h,highlightChecked:m},v=(0,d.useMemo)((()=>{var e;return null==c||null===(e=c.shipping_rates)||void 0===e?void 0:e.findIndex((e=>null==e?void 0:e.selected))}),[null==c?void 0:c.shipping_rates]);return k?(0,r.createElement)(Kt.Panel,{className:(0,n.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":h}),initialOpen:!1,title:w},(0,r.createElement)(jr,{...y})):(0,r.createElement)("div",{className:(0,n.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":h,"wc-block-components-shipping-rates-control__package--first-selected":!h&&0===v,"wc-block-components-shipping-rates-control__package--last-selected":!h&&v===(null==c||null===(p=c.shipping_rates)||void 0===p?void 0:p.length)-1})},w,(0,r.createElement)(jr,{...y}))},Kr=({packages:e,showItems:t,collapsible:o,noResultsMessage:n,renderOption:s,context:c=""})=>e.length?(0,r.createElement)(r.Fragment,null,e.map((({package_id:e,...a})=>(0,r.createElement)(Ur,{highlightChecked:"woocommerce/cart"!==c,key:e,packageId:e,packageData:a,collapsible:o,showItems:t,noResultsMessage:n,renderOption:s})))):null,Hr=({shippingRates:e,isLoadingRates:t,className:o,collapsible:n,showItems:s,noResultsMessage:c,renderOption:a,context:i})=>{(0,d.useEffect)((()=>{var o,r;t||(o=qe(e),r=(e=>e.reduce((function(e,t){return e+t.shipping_rates.length}),0))(e),1===o?(0,Or.speak)((0,l.sprintf)(/* translators: %d number of shipping options found. */ /* translators: %d number of shipping options found. */
(0,l._n)("%d shipping option was found.","%d shipping options were found.",r,"woocommerce"),r)):(0,Or.speak)((0,l.sprintf)(/* translators: %d number of shipping packages packages. */ /* translators: %d number of shipping packages packages. */
(0,l._n)("Shipping option searched for %d package.","Shipping options searched for %d packages.",o,"woocommerce"),o)+" "+(0,l.sprintf)(/* translators: %d number of shipping options available. */ /* translators: %d number of shipping options available. */
(0,l._n)("%d shipping option was found","%d shipping options were found",r,"woocommerce"),r)))}),[t,e]);const{extensions:m,receiveCart:p,...u}=He(),h={className:o,collapsible:n,showItems:s,noResultsMessage:c,renderOption:a,extensions:m,cart:u,components:{ShippingRatesControlPackage:Ur},context:i},{isEditor:g}=k(),{hasSelectedLocalPickup:_,selectedRates:E}=st(),w=(0,we.isObject)(E)?Object.values(E):[],b=w.every((e=>e===w[0]));return(0,r.createElement)(pr,{isLoading:t,screenReaderLabel:(0,l.__)("Loading shipping rates…","woocommerce"),showSpinner:!0},_&&"woocommerce/cart"===i&&e.length>1&&!b&&!g&&(0,r.createElement)(Mr,{className:"wc-block-components-notice",isDismissible:!1,status:"warning"},(0,l.__)("Multiple shipments must have the same pickup location","woocommerce")),(0,r.createElement)(Tt.ExperimentalOrderShippingPackages.Slot,{...h}),(0,r.createElement)(Tt.ExperimentalOrderShippingPackages,null,(0,r.createElement)(Kr,{packages:e,noResultsMessage:c,renderOption:a})))},qr=({hasRates:e,shippingRates:t,isLoadingRates:o,isAddressComplete:n})=>{const s=e?(0,l.__)("Shipping options","woocommerce"):(0,l.__)("Choose a shipping option","woocommerce");return(0,r.createElement)("fieldset",{className:"wc-block-components-totals-shipping__fieldset"},(0,r.createElement)("legend",{className:"screen-reader-text"},s),(0,r.createElement)(Hr,{className:"wc-block-components-totals-shipping__options",noResultsMessage:(0,r.createElement)(r.Fragment,null,n&&(0,r.createElement)(Mr,{isDismissible:!1,className:"wc-block-components-shipping-rates-control__no-results-notice",status:"warning"},(0,l.__)("There are no shipping options available. Please check your shipping address.","woocommerce"))),shippingRates:t,isLoadingRates:o,context:"woocommerce/cart"}))};o(780);const $r=({currency:e,values:t,showCalculator:o=!0,showRateSelector:s=!0,isCheckout:c=!1,className:a})=>{const[i,m]=(0,d.useState)(!1),{shippingAddress:p,cartHasCalculatedShipping:u,shippingRates:h,isLoadingRates:g}=He(),E=(e=>(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.total_shipping,10)+parseInt(e.total_shipping_tax,10):parseInt(e.total_shipping,10))(t),k=h.some((e=>e.shipping_rates.length))||E>0,w=o&&i,b=(0,_.useSelect)((e=>e(te.CHECKOUT_STORE_KEY).prefersCollection())),v=h.flatMap((e=>e.shipping_rates.filter((e=>b&&ze(e)&&e.selected||!b&&e.selected)).flatMap((e=>e.name)))),f=Oe(p,["state","country","postcode","city"]),C=((e,t,o)=>!e||!t&&o.some((e=>!e.shipping_rates.some((e=>!We(e.method_id))))))(k,b,h),S=0===E?(0,r.createElement)("strong",null,(0,l.__)("Free","woocommerce")):E;return(0,r.createElement)("div",{className:(0,n.A)("wc-block-components-totals-shipping",a)},(0,r.createElement)(Kt.TotalsItem,{label:(0,l.__)("Delivery","woocommerce"),value:!C&&u?S:(!f||c)&&(0,r.createElement)(fr,{showCalculator:o,isCheckout:c,isShippingCalculatorOpen:i,setIsShippingCalculatorOpen:m}),description:!C&&u||f&&!c?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(kr,{selectedShippingRates:v}),(0,r.createElement)(Ar,{shippingAddress:p,showCalculator:o,isShippingCalculatorOpen:i,setIsShippingCalculatorOpen:m})):null,currency:e}),w&&(0,r.createElement)(yr,{onUpdate:()=>{m(!1)},onCancel:()=>{m(!1)}}),s&&u&&!w&&(0,r.createElement)(qr,{hasRates:k,shippingRates:h,isLoadingRates:g,isAddressComplete:f}))},zr=()=>{const{extensions:e,receiveCart:t,...o}=He(),n={extensions:e,cart:o,context:"woocommerce/checkout"};return(0,r.createElement)(Tt.ExperimentalOrderMeta.Slot,{...n})},Wr=JSON.parse('{"xY":{"align":false,"html":false,"multiple":false,"reusable":false,"inserter":false,"lock":false},"uK":{"lock":{"type":"object","default":{"remove":true}}}}'),Gr=[{attributes:Wr.uK,save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(m.InnerBlocks.Content,null)),supports:Wr.xY,migrate:({attributes:e})=>[e,[(0,i.createBlock)("woocommerce/checkout-order-summary-cart-items-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-coupon-form-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-totals-block",{},[(0,i.createBlock)("woocommerce/checkout-order-summary-subtotal-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-fee-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-discount-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-shipping-block",{},[]),(0,i.createBlock)("woocommerce/checkout-order-summary-taxes-block",{},[])])]],isEligible:(e,t)=>!t.some((e=>"woocommerce/checkout-order-summary-totals-block"===e.name))}],Zr=Gr;(0,i.registerBlockType)("woocommerce/checkout-order-summary-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},attributes:{className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}},edit:({clientId:e})=>{const t=(0,m.useBlockProps)(),{cartTotals:o}=He(),n=(0,_r.getCurrencyFromPriceResponse)(o),s=Dt(Tt.innerBlockAreas.CHECKOUT_ORDER_SUMMARY),c=[["woocommerce/checkout-order-summary-cart-items-block",{},[]],["woocommerce/checkout-order-summary-coupon-form-block",{},[]],["woocommerce/checkout-order-summary-totals-block",{},[]]];return Lt({clientId:e,registeredBlocks:s,defaultTemplate:c}),(0,r.createElement)("div",{...t},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:s,template:c}),(0,r.createElement)("div",{className:"wc-block-components-totals-wrapper"},(0,r.createElement)(Er,{currency:n,values:o})),(0,r.createElement)(zr,null))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(m.InnerBlocks.Content,null)),deprecated:Zr});var Xr=o(8994),Jr=o(4133);const Qr={warning:"#F0B849",error:"#CC1818",success:"#46B450",info:"#0073AA"},en=({status:e="warning",...t})=>(0,r.createElement)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",...t},(0,r.createElement)("path",{d:"M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z",stroke:Qr[e],strokeWidth:"1.5"}),(0,r.createElement)("path",{d:"M13 7H11V13H13V7Z",fill:Qr[e]}),(0,r.createElement)("path",{d:"M13 15H11V17H13V15Z",fill:Qr[e]}));o(9705);const tn=({href:e,title:t,description:o,warning:n})=>(0,r.createElement)("a",{href:e,className:"wc-block-editor-components-external-link-card",target:"_blank",rel:"noreferrer"},(0,r.createElement)("span",{className:"wc-block-editor-components-external-link-card__content"},(0,r.createElement)("strong",{className:"wc-block-editor-components-external-link-card__title"},t),o&&(0,r.createElement)("span",{className:"wc-block-editor-components-external-link-card__description",dangerouslySetInnerHTML:{__html:Yr(o)}}),n?(0,r.createElement)("span",{className:"wc-block-editor-components-external-link-card__warning"},(0,r.createElement)(a.A,{icon:(0,r.createElement)(en,{status:"error"})}),(0,r.createElement)("span",null,n)):null),(0,r.createElement)(Nt.VisuallyHidden,{as:"span"},/* translators: accessibility text */ /* translators: accessibility text */
(0,l.__)("(opens in a new tab)","woocommerce")),(0,r.createElement)(a.A,{icon:Jr.A,className:"wc-block-editor-components-external-link-card__icon"})),on=window.wp.autop,rn=e=>e.replace(/<\/?[a-z][^>]*?>/gi,""),nn=(e,t)=>e.replace(/[\s|\.\,]+$/i,"")+t,sn=(e,t,o="&hellip;",r=!0)=>{const n=rn(e),s=n.split(" ").splice(0,t).join(" ");return s===n?r?(0,on.autop)(n):n:r?(0,on.autop)(nn(s,o)):nn(s,o)},cn=(e,t,o=!0,r="&hellip;",n=!0)=>{const s=rn(e),c=s.slice(0,t);if(c===s)return n?(0,on.autop)(s):s;if(o)return(0,on.autop)(nn(c,r));const a=c.match(/([\s]+)/g),i=a?a.length:0,l=s.slice(0,t+i);return n?(0,on.autop)(nn(l,r)):nn(l,r)};o(1637);const an=()=>(0,r.createElement)(Mr,{isDismissible:!1,className:"wc-block-checkout__no-payment-methods-notice",status:"error"},(0,l.__)("There are no payment methods available. This may be an error on our side. Please contact us if you need any help placing your order.","woocommerce")),ln=(0,r.createElement)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)("g",{fill:"none",fillRule:"evenodd"},(0,r.createElement)("path",{d:"M0 0h24v24H0z"}),(0,r.createElement)("path",{fill:"#000",fillRule:"nonzero",d:"M17.3 8v1c1 .2 1.4.9 1.4 1.7h-1c0-.6-.3-1-1-1-.8 0-1.3.4-1.3.9 0 .4.3.6 1.4 1 1 .2 2 .6 2 1.9 0 .9-.6 1.4-1.5 1.5v1H16v-1c-.9-.1-1.6-.7-1.7-1.7h1c0 .6.4 1 1.3 1 1 0 1.2-.5 1.2-.8 0-.4-.2-.8-1.3-1.1-1.3-.3-2.1-.8-2.1-1.8 0-.9.7-1.5 1.6-1.6V8h1.3zM12 10v1H6v-1h6zm2-2v1H6V8h8zM2 4v16h20V4H2zm2 14V6h16v12H4z"}),(0,r.createElement)("path",{stroke:"#000",strokeLinecap:"round",d:"M6 16c2.6 0 3.9-3 1.7-3-2 0-1 3 1.5 3 1 0 1-.8 2.8-.8"})));var mn=o(4166),dn=o(3576);o(777);const pn={bank:mn.A,bill:dn.A,card:Xr.A,checkPayment:ln},un=({icon:e="",text:t=""})=>{const o=!!e,s=(0,d.useCallback)((e=>o&&(0,we.isString)(e)&&(0,we.objectHasProp)(pn,e)),[o]),c=(0,n.A)("wc-block-components-payment-method-label",{"wc-block-components-payment-method-label--with-icon":o});return(0,r.createElement)("span",{className:c},s(e)?(0,r.createElement)(a.A,{icon:pn[e]}):e,t)},hn=e=>`wc-block-components-payment-method-icon wc-block-components-payment-method-icon--${e}`,gn=({id:e,src:t=null,alt:o=""})=>t?(0,r.createElement)("img",{className:hn(e),src:t,alt:o}):null,En=[{id:"alipay",alt:"Alipay",src:M+"payment-methods/alipay.svg"},{id:"amex",alt:"American Express",src:M+"payment-methods/amex.svg"},{id:"bancontact",alt:"Bancontact",src:M+"payment-methods/bancontact.svg"},{id:"diners",alt:"Diners Club",src:M+"payment-methods/diners.svg"},{id:"discover",alt:"Discover",src:M+"payment-methods/discover.svg"},{id:"eps",alt:"EPS",src:M+"payment-methods/eps.svg"},{id:"giropay",alt:"Giropay",src:M+"payment-methods/giropay.svg"},{id:"ideal",alt:"iDeal",src:M+"payment-methods/ideal.svg"},{id:"jcb",alt:"JCB",src:M+"payment-methods/jcb.svg"},{id:"laser",alt:"Laser",src:M+"payment-methods/laser.svg"},{id:"maestro",alt:"Maestro",src:M+"payment-methods/maestro.svg"},{id:"mastercard",alt:"Mastercard",src:M+"payment-methods/mastercard.svg"},{id:"multibanco",alt:"Multibanco",src:M+"payment-methods/multibanco.svg"},{id:"p24",alt:"Przelewy24",src:M+"payment-methods/p24.svg"},{id:"sepa",alt:"Sepa",src:M+"payment-methods/sepa.svg"},{id:"sofort",alt:"Sofort",src:M+"payment-methods/sofort.svg"},{id:"unionpay",alt:"Union Pay",src:M+"payment-methods/unionpay.svg"},{id:"visa",alt:"Visa",src:M+"payment-methods/visa.svg"},{id:"wechat",alt:"WeChat",src:M+"payment-methods/wechat.svg"}];o(4957);const kn=({icons:e=[],align:t="center",className:o})=>{const s=(e=>{const t={};return e.forEach((e=>{let o={};"string"==typeof e&&(o={id:e,alt:e,src:null}),"object"==typeof e&&(o={id:e.id||"",alt:e.alt||"",src:e.src||null}),o.id&&(0,we.isString)(o.id)&&!t[o.id]&&(t[o.id]=o)})),Object.values(t)})(e);if(0===s.length)return null;const c=(0,n.A)("wc-block-components-payment-method-icons",{"wc-block-components-payment-method-icons--align-left":"left"===t,"wc-block-components-payment-method-icons--align-right":"right"===t},o);return(0,r.createElement)("div",{className:c},s.map((e=>{const t={...e,...(o=e.id,En.find((e=>e.id===o))||{})};var o;return(0,r.createElement)(gn,{key:"payment-method-icon-"+e.id,...t})})))},wn=(e="")=>{const{cartCoupons:t,cartIsLoading:o}=He(),{createErrorNotice:r}=(0,_.useDispatch)("core/notices"),{createNotice:n}=(0,_.useDispatch)("core/notices"),{setValidationErrors:s}=(0,_.useDispatch)(te.VALIDATION_STORE_KEY),{isApplyingCoupon:c,isRemovingCoupon:a}=(0,_.useSelect)((e=>{const t=e(te.CART_STORE_KEY);return{isApplyingCoupon:t.isApplyingCoupon(),isRemovingCoupon:t.isRemovingCoupon()}}),[r,n]),{applyCoupon:i,removeCoupon:m}=(0,_.useDispatch)(te.CART_STORE_KEY),d=(0,_.useSelect)((e=>e(te.CHECKOUT_STORE_KEY).getOrderId()));return{appliedCoupons:t,isLoading:o,applyCoupon:t=>i(t).then((()=>((0,Tt.applyCheckoutFilter)({filterName:"showApplyCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&n("info",(0,l.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,l.__)('Coupon code "%s" has been applied to your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((e=>{const t=(e=>{var t,o,r,n;return d&&d>0&&null!=e&&null!==(t=e.data)&&void 0!==t&&null!==(o=t.details)&&void 0!==o&&o.checkout?e.data.details.checkout:null!=e&&null!==(r=e.data)&&void 0!==r&&null!==(n=r.details)&&void 0!==n&&n.cart?e.data.details.cart:e.message})(e);return s({coupon:{message:(0,Se.decodeEntities)(t),hidden:!1}}),Promise.resolve(!1)})),removeCoupon:t=>m(t).then((()=>((0,Tt.applyCheckoutFilter)({filterName:"showRemoveCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&n("info",(0,l.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,l.__)('Coupon code "%s" has been removed from your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((t=>(r(t.message,{id:"coupon-form",context:e}),Promise.resolve(!1)))),isApplyingCoupon:c,isRemovingCoupon:a}},bn=(e,t)=>{const o=[],r=(t,o)=>{const r=o+"_tax",n=(0,we.objectHasProp)(e,o)&&(0,we.isString)(e[o])?parseInt(e[o],10):0;return{key:o,label:t,value:n,valueWithTax:n+((0,we.objectHasProp)(e,r)&&(0,we.isString)(e[r])?parseInt(e[r],10):0)}};return o.push(r((0,l.__)("Subtotal:","woocommerce"),"total_items")),o.push(r((0,l.__)("Fees:","woocommerce"),"total_fees")),o.push(r((0,l.__)("Discount:","woocommerce"),"total_discount")),o.push({key:"total_tax",label:(0,l.__)("Taxes:","woocommerce"),value:parseInt(e.total_tax,10),valueWithTax:parseInt(e.total_tax,10)}),t&&o.push(r((0,l.__)("Shipping:","woocommerce"),"total_shipping")),o},yn=()=>{const{onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutAfterProcessingWithSuccess:o,onCheckoutAfterProcessingWithError:r,onSubmit:n,onCheckoutSuccess:s,onCheckoutFail:c,onCheckoutValidation:a}=kt(),{isCalculating:i,isComplete:m,isIdle:p,isProcessing:u,customerId:h}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{isComplete:t.isComplete(),isIdle:t.isIdle(),isProcessing:t.isProcessing(),customerId:t.getCustomerId(),isCalculating:t.isCalculating()}})),{paymentStatus:g,activePaymentMethod:E,shouldSavePayment:k}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{paymentStatus:{get isPristine(){return re()("isPristine",{since:"9.6.0",alternative:"isIdle",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentIdle()},isIdle:t.isPaymentIdle(),isStarted:t.isExpressPaymentStarted(),isProcessing:t.isPaymentProcessing(),get isFinished(){return re()("isFinished",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()||t.isPaymentReady()},hasError:t.hasPaymentError(),get hasFailed(){return re()("hasFailed",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()},get isSuccessful(){return re()("isSuccessful",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentReady()},isReady:t.isPaymentReady(),isDoingExpressPayment:t.isExpressPaymentMethodActive()},activePaymentMethod:t.getActivePaymentMethod(),shouldSavePayment:t.getShouldSavePaymentMethod()}})),{__internalSetExpressPaymentError:w}=(0,_.useDispatch)(te.PAYMENT_STORE_KEY),{onPaymentProcessing:b,onPaymentSetup:v}=(0,d.useContext)(ie),{shippingErrorStatus:f,shippingErrorTypes:C,onShippingRateSuccess:S,onShippingRateFail:A,onShippingRateSelectSuccess:P,onShippingRateSelectFail:N}=mt(),{shippingRates:T,isLoadingRates:R,selectedRates:x,isSelectingRate:I,selectShippingRate:O,needsShipping:M}=st(),{billingAddress:B,shippingAddress:F}=(0,_.useSelect)((e=>e(te.CART_STORE_KEY).getCustomerData())),{setShippingAddress:D}=(0,_.useDispatch)(te.CART_STORE_KEY),{cartItems:L,cartFees:Y,cartTotals:V,extensions:j}=He(),{appliedCoupons:U}=wn(),K=(0,d.useRef)(bn(V,M)),H=(0,d.useRef)({label:(0,l.__)("Total","woocommerce"),value:parseInt(V.total_price,10)});(0,d.useEffect)((()=>{K.current=bn(V,M),H.current={label:(0,l.__)("Total","woocommerce"),value:parseInt(V.total_price,10)}}),[V,M]);const q=(0,d.useCallback)(((e="")=>{re()("setExpressPaymentError should only be used by Express Payment Methods (using the provided onError handler).",{alternative:"",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),w(e)}),[w]);return{activePaymentMethod:E,billing:{appliedCoupons:U,billingAddress:B,billingData:B,cartTotal:H.current,cartTotalItems:K.current,currency:(0,_r.getCurrencyFromPriceResponse)(V),customerId:h,displayPricesIncludingTax:(0,y.getSetting)("displayCartPricesIncludingTax",!1)},cartData:{cartItems:L,cartFees:Y,extensions:j},checkoutStatus:{isCalculating:i,isComplete:m,isIdle:p,isProcessing:u},components:{LoadingMask:pr,PaymentMethodIcons:kn,PaymentMethodLabel:un,ValidationInputError:Kt.ValidationInputError},emitResponse:{noticeContexts:ye,responseTypes:be},eventRegistration:{onCheckoutAfterProcessingWithError:r,onCheckoutAfterProcessingWithSuccess:o,onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutSuccess:s,onCheckoutFail:c,onCheckoutValidation:a,onPaymentProcessing:b,onPaymentSetup:v,onShippingRateFail:A,onShippingRateSelectFail:N,onShippingRateSelectSuccess:P,onShippingRateSuccess:S},onSubmit:n,paymentStatus:g,setExpressPaymentError:q,shippingData:{isSelectingRate:I,needsShipping:M,selectedRates:x,setSelectedRates:O,setShippingAddress:D,shippingAddress:F,shippingRates:T,shippingRatesLoading:R},shippingStatus:{shippingErrorStatus:f,shippingErrorTypes:C},shouldSavePayment:k}};class vn extends d.Component{constructor(...e){super(...e),(0,I.A)(this,"state",{errorMessage:"",hasError:!1})}static getDerivedStateFromError(e){return{errorMessage:e.message,hasError:!0}}render(){const{hasError:e,errorMessage:t}=this.state,{isEditor:o}=this.props;if(e){let e=(0,l.__)("We are experiencing difficulties with this payment method. Please contact us for assistance.","woocommerce");(o||y.CURRENT_USER_IS_ADMIN)&&(e=t||(0,l.__)("There was an error with this payment method. Please verify it's configured correctly.","woocommerce"));const n=[{id:"0",content:e,isDismissible:!1,status:"error"}];return(0,r.createElement)(Kt.StoreNoticesContainer,{additionalNotices:n,context:ye.PAYMENTS})}return this.props.children}}const fn=vn,Cn=({children:e,showSaveOption:t})=>{const{isEditor:o}=k(),{shouldSavePaymentMethod:n,customerId:s}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY),o=e(te.CHECKOUT_STORE_KEY);return{shouldSavePaymentMethod:t.getShouldSavePaymentMethod(),customerId:o.getCustomerId()}})),{__internalSetShouldSavePaymentMethod:c}=(0,_.useDispatch)(te.PAYMENT_STORE_KEY);return(0,r.createElement)(fn,{isEditor:o},e,s>0&&t&&(0,r.createElement)(Kt.CheckboxControl,{className:"wc-block-components-payment-methods__save-card-info",label:(0,l.__)("Save payment information to my account for future purchases.","woocommerce"),checked:n,onChange:()=>c(!n)}))},Sn="wc/store/payment",An=()=>{const{activeSavedToken:e,activePaymentMethod:t,isExpressPaymentMethodActive:o,savedPaymentMethods:s,availablePaymentMethods:c}=(0,_.useSelect)((e=>{const t=e(Sn);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),savedPaymentMethods:t.getSavedPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),{__internalSetActivePaymentMethod:a}=(0,_.useDispatch)(Sn),i=(0,vt.getPaymentMethods)(),{...l}=yn(),{removeNotice:m}=(0,_.useDispatch)("core/notices"),{dispatchCheckoutEvent:p}=nt(),{isEditor:u}=k(),h=Object.keys(c).map((e=>{const{edit:t,content:o,label:n,supports:s}=i[e],c=u?t:o;return{value:e,label:"string"==typeof n?n:(0,d.cloneElement)(n,{components:l.components}),name:`wc-saved-payment-method-token-${e}`,content:(0,r.createElement)(Cn,{showSaveOption:s.showSaveOption},(0,d.cloneElement)(c,{__internalSetActivePaymentMethod:a,...l}))}})),g=(0,d.useCallback)((e=>{a(e),m("wc-payment-error",ye.PAYMENTS),p("set-active-payment-method",{value:e})}),[p,m,a]),E=0===Object.keys(s).length&&1===Object.keys(i).length,w=(0,n.A)({"disable-radio-control":E});return o?null:(0,r.createElement)(Kt.RadioControlAccordion,{highlightChecked:!0,id:"wc-payment-method-options",className:w,selected:e?null:t,onChange:g,options:h})},Pn="wc/store/cart",Nn=((0,l.__)("Unable to get cart data from the API.","woocommerce"),[]),Tn=[],Rn={},xn={};Object.keys(y.defaultFields).forEach((e=>{xn[e]=""})),delete xn.email;const In={};Object.keys(y.defaultFields).forEach((e=>{In[e]=""}));const On={cartItemsPendingQuantity:[],cartItemsPendingDelete:[],cartData:{coupons:[],shippingRates:[],shippingAddress:xn,billingAddress:In,items:[],itemsCount:0,itemsWeight:0,crossSells:[],needsShipping:!0,needsPayment:!1,hasCalculatedShipping:!0,fees:[],totals:{currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:".",currency_thousand_separator:",",currency_prefix:"",currency_suffix:"",total_items:"0",total_items_tax:"0",total_fees:"0",total_fees_tax:"0",total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_price:"0",total_tax:"0",tax_lines:[]},errors:Nn,paymentMethods:[],paymentRequirements:[],extensions:Rn},metaData:{updatingCustomerData:!1,updatingSelectedRate:!1,applyingCoupon:"",removingCoupon:"",isCartDataStale:!1},errors:Tn},Mn=({method:e,expires:t})=>{var o,r,n;return(0,l.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */
(0,l.__)("%1$s ending in %2$s (expires %3$s)","woocommerce"),null!==(o=null!==(r=null==e?void 0:e.display_brand)&&void 0!==r?r:null==e||null===(n=e.networks)||void 0===n?void 0:n.preferred)&&void 0!==o?o:e.brand,e.last4,t)},Bn=({method:e})=>e.brand&&e.last4?(0,l.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */
(0,l.__)("%1$s ending in %2$s","woocommerce"),e.brand,e.last4):(0,l.sprintf)(/* translators: %s is the name of the payment method gateway. */ /* translators: %s is the name of the payment method gateway. */
(0,l.__)("Saved token for %s","woocommerce"),e.gateway),Fn=()=>{var e;const{activeSavedToken:t,activePaymentMethod:o,savedPaymentMethods:n}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),savedPaymentMethods:t.getSavedPaymentMethods()}})),{__internalSetActivePaymentMethod:s}=(0,_.useDispatch)(te.PAYMENT_STORE_KEY),c=(()=>{let e;if((0,_.select)("core/editor")){const t={cartCoupons:ot.coupons,cartItems:ot.items,crossSellsProducts:ot.cross_sells,cartFees:ot.fees,cartItemsCount:ot.items_count,cartItemsWeight:ot.items_weight,cartNeedsPayment:ot.needs_payment,cartNeedsShipping:ot.needs_shipping,cartItemErrors:Nn,cartTotals:ot.totals,cartIsLoading:!1,cartErrors:Tn,billingData:On.cartData.billingAddress,billingAddress:On.cartData.billingAddress,shippingAddress:On.cartData.shippingAddress,extensions:Rn,shippingRates:ot.shipping_rates,isLoadingRates:!1,cartHasCalculatedShipping:ot.has_calculated_shipping,paymentRequirements:ot.payment_requirements,receiveCart:()=>{}};e={cart:t,cartTotals:t.cartTotals,cartNeedsShipping:t.cartNeedsShipping,billingData:t.billingAddress,billingAddress:t.billingAddress,shippingAddress:t.shippingAddress,selectedShippingMethods:Ge(t.shippingRates),paymentMethods:ot.payment_methods,paymentRequirements:t.paymentRequirements}}else{const t=(0,_.select)(Pn),o=t.getCartData(),r=t.getCartErrors(),n=t.getCartTotals(),s=!t.hasFinishedResolution("getCartData"),c=t.isCustomerDataUpdating(),a=Ge(o.shippingRates);e={cart:{cartCoupons:o.coupons,cartItems:o.items,crossSellsProducts:o.crossSells,cartFees:o.fees,cartItemsCount:o.itemsCount,cartItemsWeight:o.itemsWeight,cartNeedsPayment:o.needsPayment,cartNeedsShipping:o.needsShipping,cartItemErrors:o.errors,cartTotals:n,cartIsLoading:s,cartErrors:r,billingData:xe(o.billingAddress),billingAddress:xe(o.billingAddress),shippingAddress:xe(o.shippingAddress),extensions:o.extensions,shippingRates:o.shippingRates,isLoadingRates:c,cartHasCalculatedShipping:o.hasCalculatedShipping,paymentRequirements:o.paymentRequirements,receiveCart:(0,_.dispatch)(Pn).receiveCart},cartTotals:o.totals,cartNeedsShipping:o.needsShipping,billingData:o.billingAddress,billingAddress:o.billingAddress,shippingAddress:o.shippingAddress,selectedShippingMethods:a,paymentMethods:o.paymentMethods,paymentRequirements:o.paymentRequirements}}return e})(),a=(0,vt.getPaymentMethods)(),i=yn(),{removeNotice:l}=(0,_.useDispatch)("core/notices"),{dispatchCheckoutEvent:m}=nt(),p=(0,d.useMemo)((()=>{const e=Object.keys(n),t=new Set(e.flatMap((e=>n[e].map((e=>e.method.gateway))))),o=Array.from(t).filter((e=>{var t;return null===(t=a[e])||void 0===t?void 0:t.canMakePayment(c)}));return e.flatMap((e=>n[e].map((t=>{if(!o.includes(t.method.gateway))return;const r="cc"===e||"echeck"===e,n=t.method.gateway;return{name:`wc-saved-payment-method-token-${n}`,label:r?Mn(t):Bn(t),value:t.tokenId.toString(),onChange:e=>{s(n,{token:e,payment_method:n,[`wc-${n}-payment-token`]:e.toString(),isSavedToken:!0}),l("wc-payment-error",ye.PAYMENTS),m("set-active-payment-method",{paymentMethodSlug:n})}}})))).filter((e=>void 0!==e))}),[n,a,s,l,m,c]),u=t&&a[o]&&void 0!==(null===(e=a[o])||void 0===e?void 0:e.savedTokenComponent)&&!(0,we.isNull)(a[o].savedTokenComponent)?(0,d.cloneElement)(a[o].savedTokenComponent,{token:t,...i}):null;return p.length>0?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.RadioControl,{highlightChecked:!0,id:"wc-payment-method-saved-tokens",selected:t,options:p,onChange:()=>{}}),u):null};o(181);const Dn=()=>{const{paymentMethodsInitialized:e,availablePaymentMethods:t,savedPaymentMethods:o}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),availablePaymentMethods:t.getAvailablePaymentMethods(),savedPaymentMethods:t.getSavedPaymentMethods()}}));return e&&0===Object.keys(t).length?(0,r.createElement)(an,null):(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Fn,null),Object.keys(o).length>0&&(0,r.createElement)(Kt.Label,{label:(0,l.__)("Use another payment method.","woocommerce"),screenReaderLabel:(0,l.__)("Other available payment methods","woocommerce"),wrapperElement:"p",wrapperProps:{className:["wc-block-components-checkout-step__description wc-block-components-checkout-step__description-payments-aligned"]}}),(0,r.createElement)(An,null))},Ln=()=>(0,r.createElement)(Dn,null),Yn={...Ao({defaultTitle:(0,l.__)("Payment options","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,i.registerBlockType)("woocommerce/checkout-payment-block",{icon:{src:(0,r.createElement)(a.A,{icon:Xr.A,className:"wc-block-editor-components-block-icon"})},attributes:Yn,edit:({attributes:e,setAttributes:t})=>{const o=(0,y.getSetting)("globalPaymentMethods"),{incompatiblePaymentMethods:s}=(0,_.useSelect)((e=>{const{getIncompatiblePaymentMethods:t}=e(te.PAYMENT_STORE_KEY);return{incompatiblePaymentMethods:t()}}),[]),c=(0,l.__)("Incompatible with block-based checkout","woocommerce"),a=O.wordCountType;return(0,r.createElement)(qt,{attributes:e,setAttributes:t,className:(0,n.A)("wc-block-checkout__payment-method",null==e?void 0:e.className)},(0,r.createElement)(m.InspectorControls,null,o.length>0&&(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Methods","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("You currently have the following payment integrations active.","woocommerce")),o.map((e=>{const t=!!s[e.id];let o;return o="words"===a?sn(e.description,30,void 0,!1):cn(e.description,30,"characters_including_spaces"===a,void 0,!1),(0,r.createElement)(tn,{key:e.id,href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=checkout&section=${e.id}`,title:e.title,description:o,...t?{warning:c}:{}})})),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=checkout`},(0,l.__)("Manage payment methods","woocommerce")))),(0,r.createElement)(Jt,null,(0,r.createElement)(Ln,null)),(0,r.createElement)($t,{block:Tt.innerBlockAreas.PAYMENT_METHODS}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});const Vn=(0,r.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor",viewBox:"0 0 24 24"},(0,r.createElement)("path",{stroke:"#1E1E1E",strokeLinejoin:"round",strokeWidth:"1.5",d:"M18.25 12a6.25 6.25 0 1 1-12.5 0 6.25 6.25 0 0 1 12.5 0Z"}),(0,r.createElement)("path",{fill:"#1E1E1E",d:"M10 3h4v3h-4z"}),(0,r.createElement)("rect",{width:"1.5",height:"5",x:"11.25",y:"8",fill:"#1E1E1E",rx:".75"}),(0,r.createElement)("path",{fill:"#1E1E1E",d:"m15.7 4.816 1.66 1.078-1.114 1.718-1.661-1.078z"})),jn=()=>((e=!1)=>{const{paymentMethodsInitialized:t,expressPaymentMethodsInitialized:o,availablePaymentMethods:r,availableExpressPaymentMethods:n}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),s=Object.values(r).map((({name:e})=>e)),c=Object.values(n).map((({name:e})=>e)),a=(0,vt.getPaymentMethods)(),i=(0,vt.getExpressPaymentMethods)(),l=Object.keys(a).reduce(((e,t)=>(s.includes(t)&&(e[t]=a[t]),e)),{}),m=Object.keys(i).reduce(((e,t)=>(c.includes(t)&&(e[t]=i[t]),e)),{}),d=io(l),p=io(m);return{paymentMethods:e?p:d,isInitialized:e?o:t}})(!0),Un=["height","borderRadius"],Kn=e=>{const t=(0,_.select)(te.PAYMENT_STORE_KEY).getAvailableExpressPaymentMethods();return Object.values(t).reduce(((t,o)=>t||(null==o?void 0:o.supportsStyle.some((t=>e.includes(t))))),!1)},Hn=({attributes:e,setAttributes:t})=>{const{buttonHeight:o,buttonBorderRadius:n}=e;return(0,r.createElement)(r.Fragment,null,Kn(["height"])&&(0,r.createElement)(Nt.RadioControl,{label:(0,l.__)("Button height","woocommerce"),selected:o,options:[{label:(0,l.__)("Small (40px)","woocommerce"),value:"40"},{label:(0,l.__)("Medium (48px)","woocommerce"),value:"48"},{label:(0,l.__)("Large (55px)","woocommerce"),value:"55"}],onChange:e=>t({buttonHeight:e})}),Kn(["borderRadius"])&&(0,r.createElement)("div",{className:"border-radius-control-container"},(0,r.createElement)(m.HeightControl,{label:(0,l.__)("Button border radius","woocommerce"),value:n,onChange:e=>{const o=e.replace("px","");t({buttonBorderRadius:o})}})))},qn=({attributes:e,setAttributes:t})=>e.showButtonStyles?(0,r.createElement)(Hn,{attributes:e,setAttributes:t}):null,$n=()=>{const e=(0,_.select)(te.PAYMENT_STORE_KEY).getAvailableExpressPaymentMethods();return Object.entries(e).length<1?(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("You currently have no express payment integrations active.","woocommerce")):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("You currently have the following express payment integrations active.","woocommerce")),Object.values(e).map((e=>(0,r.createElement)(tn,{key:e.name,href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=checkout&section=${encodeURIComponent(e.gatewayId)}`,title:e.title,description:e.description}))))},zn=(0,r.createElement)(r.Fragment,null,(0,l.__)("Apply uniform styles","woocommerce")," ",(0,r.createElement)("span",{className:"express-payment-styles-beta-badge"},"Beta")),Wn=({attributes:e,setAttributes:t})=>(0,r.createElement)(m.InspectorControls,null,Kn(Un)&&(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Button Settings","woocommerce"),className:"express-payment-button-settings"},(0,r.createElement)(Nt.ToggleControl,{label:zn,checked:e.showButtonStyles,onChange:()=>t({showButtonStyles:!e.showButtonStyles}),help:(0,l.__)("Sets a consistent style for express payment buttons.","woocommerce")}),(0,r.createElement)(Nt.Notice,{status:"warning",isDismissible:!1,className:"wc-block-checkout__notice express-payment-styles-notice"},(0,r.createElement)("strong",null,(0,l.__)("Note","woocommerce"),":")," ",(0,l.__)("Some payment methods might not yet support all style controls","woocommerce")),(0,r.createElement)(qn,{attributes:e,setAttributes:t})),(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Express Payment Methods","woocommerce")},(0,r.createElement)($n,null))),Gn=(0,d.createContext)({showButtonStyles:!1,buttonHeight:"48",buttonBorderRadius:"4"}),Zn=()=>{const{isEditor:e}=k(),{showButtonStyles:t,buttonHeight:o,buttonBorderRadius:n}=(0,d.useContext)(Gn),s=t?{height:o,borderRadius:n}:void 0,{activePaymentMethod:c,paymentMethodData:a}=(0,_.useSelect)((e=>{const t=e(Sn);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData()}})),{__internalSetActivePaymentMethod:i,__internalSetExpressPaymentStarted:m,__internalSetPaymentIdle:p,__internalSetPaymentError:u,__internalSetPaymentMethodData:h,__internalSetExpressPaymentError:g}=(0,_.useDispatch)(Sn),{paymentMethods:E}=jn(),w=yn(),b=(0,d.useRef)(c),y=(0,d.useRef)(a),v=(0,d.useCallback)((e=>()=>{b.current=c,y.current=a,m(),i(e)}),[c,a,i,m]),f=(0,d.useCallback)((()=>{p(),i(b.current,y.current)}),[i,p]),C=(0,d.useCallback)((e=>{u(),h(e),g(e),i(b.current,y.current)}),[i,u,h,g]),S=(0,d.useCallback)(((e="")=>{re()("Express Payment Methods should use the provided onError handler instead.",{alternative:"onError",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),e?C(e):g("")}),[g,C]),A=Object.entries(E),P=A.length>0?A.map((([t,o])=>{const n=e?o.edit:o.content;return(0,d.isValidElement)(n)?(0,r.createElement)("li",{key:t,id:`express-payment-method-${t}`},(0,d.cloneElement)(n,{...w,onClick:v(t),onClose:f,onError:C,setExpressPaymentError:S,buttonAttributes:s})):null})):(0,r.createElement)("li",{key:"noneRegistered"},(0,l.__)("No registered Payment Methods","woocommerce"));return(0,r.createElement)(fn,{isEditor:e},(0,r.createElement)("ul",{className:"wc-block-components-express-payment__event-buttons"},P))};o(9569);const Xn=()=>{const{isCalculating:e,isProcessing:t,isAfterProcessing:o,isBeforeProcessing:n,isComplete:s,hasError:c}=(0,_.useSelect)((e=>{const t=e(te.CHECKOUT_STORE_KEY);return{isCalculating:t.isCalculating(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),{availableExpressPaymentMethods:a,expressPaymentMethodsInitialized:i,isExpressPaymentMethodActive:m}=(0,_.useSelect)((e=>{const t=e(te.PAYMENT_STORE_KEY);return{availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive()}})),{isEditor:d}=k();if(!i||i&&0===Object.keys(a).length)return d||y.CURRENT_USER_IS_ADMIN?(0,r.createElement)(Kt.StoreNoticesContainer,{context:ye.EXPRESS_PAYMENTS}):null;const p=t||o||n||s&&!c;return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(pr,{isLoading:e||p||m},(0,r.createElement)("div",{className:"wc-block-components-express-payment wc-block-components-express-payment--checkout"},(0,r.createElement)("div",{className:"wc-block-components-express-payment__title-container"},(0,r.createElement)(Kt.Title,{className:"wc-block-components-express-payment__title",headingLevel:"2"},(0,l.__)("Express Checkout","woocommerce"))),(0,r.createElement)("div",{className:"wc-block-components-express-payment__content"},(0,r.createElement)(Kt.StoreNoticesContainer,{context:ye.EXPRESS_PAYMENTS}),(0,r.createElement)(Zn,null)))),(0,r.createElement)("div",{className:"wc-block-components-express-payment-continue-rule wc-block-components-express-payment-continue-rule--checkout"},(0,l.__)("Or continue below","woocommerce")))},Jn=({className:e})=>{const{cartNeedsPayment:t}=He();return t?(0,r.createElement)("div",{className:e},(0,r.createElement)(Xn,null)):null};o(9865),(0,i.registerBlockType)("woocommerce/checkout-express-payment-block",{icon:{src:(0,r.createElement)(a.A,{style:{fill:"none"},icon:Vn,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{paymentMethods:o,isInitialized:s}=jn(),c=Object.keys(o).length>0,a=(0,m.useBlockProps)({className:(0,n.A)({"wp-block-woocommerce-checkout-express-payment-block--has-express-payment-methods":c},null==e?void 0:e.className),attributes:e});if(!s||!c)return null;const{buttonHeight:i,buttonBorderRadius:l,showButtonStyles:d}=e;return(0,r.createElement)("div",{...a},(0,r.createElement)(Wn,{attributes:e,setAttributes:t}),(0,r.createElement)(Gn.Provider,{value:{showButtonStyles:d,buttonHeight:i,buttonBorderRadius:l}},(0,r.createElement)(Jn,null)))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});var Qn=o(1176),es=o(3705);const ts=({minRate:e,maxRate:t,multiple:o=!1})=>{if(void 0===e||void 0===t)return null;const n=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),s=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(t.price,10)+parseInt(t.taxes,10):parseInt(t.price,10),c=0===n?(0,r.createElement)("em",null,(0,l.__)("free","woocommerce")):(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:(0,_r.getCurrencyFromPriceResponse)(e),value:n});return(0,r.createElement)("span",{className:"wc-block-checkout__shipping-method-option-price"},n!==s||o?(0,d.createInterpolateElement)(0===n&&0===s?"<price />":(0,l.__)("from <price />","woocommerce"),{price:c}):c)};function os(e){return e?{min:e.reduce(((e,t)=>We(t.method_id)?e:void 0===e||parseInt(t.price,10)<parseInt(e.price,10)?t:e),void 0),max:e.reduce(((e,t)=>We(t.method_id)?e:void 0===e||parseInt(t.price,10)>parseInt(e.price,10)?t:e),void 0)}:{min:void 0,max:void 0}}function rs(e){return e?{min:e.reduce(((e,t)=>We(t.method_id)&&(void 0===e||t.price<e.price)?t:e),void 0),max:e.reduce(((e,t)=>We(t.method_id)&&(void 0===e||t.price>e.price)?t:e),void 0)}:{min:void 0,max:void 0}}o(7694);const ns=(0,l.__)("Pickup","woocommerce"),ss=(0,l.__)("Ship","woocommerce"),cs=({checked:e,rate:t,showPrice:o,showIcon:s,toggleText:c,setAttributes:i,onClick:l})=>(0,r.createElement)(lo.$,{render:(0,r.createElement)("div",null),className:(0,n.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"pickup"===e}),onClick:l},!0===s&&(0,r.createElement)(a.A,{icon:es.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,r.createElement)(m.RichText,{value:c,placeholder:ns,tagName:"span",className:"wc-block-checkout__shipping-method-option-title",onChange:e=>i({localPickupText:e}),__unstableDisableFormats:!0,preserveWhiteSpace:!0}),!0===o&&(0,r.createElement)(ts,{minRate:t.min,maxRate:t.max})),as=({checked:e,rate:t,showPrice:o,showIcon:s,toggleText:c,setAttributes:i,onClick:d})=>{const p=void 0===t.min?(0,r.createElement)("span",{className:"wc-block-checkout__shipping-method-option-price"},(0,l.__)("calculated with an address","woocommerce")):(0,r.createElement)(ts,{minRate:t.min,maxRate:t.max});return(0,r.createElement)(lo.$,{render:(0,r.createElement)("div",null),className:(0,n.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"shipping"===e}),onClick:d},!0===s&&(0,r.createElement)(a.A,{icon:Qn.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,r.createElement)(m.RichText,{value:c,placeholder:ss,tagName:"span",className:"wc-block-checkout__shipping-method-option-title",onChange:e=>i({shippingText:e}),__unstableDisableFormats:!0,preserveWhiteSpace:!0}),!0===o&&p)},is={...Ao({defaultTitle:(0,l.__)("Delivery","woocommerce"),defaultDescription:(0,l.__)("Select how you would like to receive your order.","woocommerce")}),className:{type:"string",default:""},showIcon:{type:"boolean",default:!0},showPrice:{type:"boolean",default:!1},localPickupText:{type:"string",default:ns},shippingText:{type:"string",default:ss},lock:{type:"object",default:{move:!0,remove:!0}}};(0,i.registerBlockType)("woocommerce/checkout-shipping-method-block",{icon:{src:(0,r.createElement)(a.A,{icon:Qn.A,className:"wc-block-editor-components-block-icon"})},attributes:is,edit:({attributes:e,setAttributes:t})=>{var o,s;(0,d.useEffect)((()=>{const o=(0,y.getSetting)("localPickupText",e.localPickupText);t({localPickupText:o})}),[t]);const{setPrefersCollection:c}=(0,_.useDispatch)(te.CHECKOUT_STORE_KEY),{prefersCollection:a}=(0,_.useSelect)((e=>({prefersCollection:e(te.CHECKOUT_STORE_KEY).prefersCollection()}))),{showPrice:i,showIcon:p,className:u,localPickupText:h,shippingText:g}=e,{shippingRates:E,needsShipping:k,hasCalculatedShipping:w,isCollectable:b}=st();if(!(k&&w&&E&&b&&V))return null;const v=e=>{c("pickup"===e)};return(0,r.createElement)(qt,{attributes:e,setAttributes:t,className:(0,n.A)("wc-block-checkout__shipping-method",u)},(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Appearance","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Choose how this block is displayed to your customers.","woocommerce")),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Show icon","woocommerce"),checked:p,onChange:()=>t({showIcon:!p})}),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Show costs","woocommerce"),checked:i,onChange:()=>t({showPrice:!i})})),(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Shipping Methods","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Methods can be made managed in your store settings.","woocommerce")),(0,r.createElement)(tn,{key:"shipping_methods",href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`,title:(0,l.__)("Shipping","woocommerce"),description:(0,l.__)("Manage your shipping zones, methods, and rates.","woocommerce")}),(0,r.createElement)(tn,{key:"pickup_location",href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=pickup_location`,title:(0,l.__)("Pickup","woocommerce"),description:(0,l.__)("Allow customers to choose a local pickup location during checkout.","woocommerce")}))),(0,r.createElement)("div",{id:"shipping-method",className:"wc-block-checkout__shipping-method-container",role:"radiogroup"},(0,r.createElement)(as,{checked:a?"pickup":"shipping",rate:os(null===(o=E[0])||void 0===o?void 0:o.shipping_rates),onClick:()=>{v("shipping")},showPrice:i,showIcon:p,setAttributes:t,toggleText:g}),(0,r.createElement)(cs,{checked:a?"pickup":"shipping",rate:rs(null===(s=E[0])||void 0===s?void 0:s.shipping_rates),showPrice:i,onClick:()=>{v("pickup")},showIcon:p,setAttributes:t,toggleText:h})),(0,r.createElement)($t,{block:Tt.innerBlockAreas.SHIPPING_METHOD}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))}),o(8024);const ls=()=>(0,r.createElement)(Nt.Placeholder,{icon:(0,r.createElement)(a.A,{icon:Qn.A}),label:(0,l.__)("Shipping options","woocommerce"),className:"wc-block-checkout__no-shipping-placeholder"},(0,r.createElement)("span",{className:"wc-block-checkout__no-shipping-placeholder-description"},(0,l.__)("Your store does not have any Shipping Options configured. Once you have added your Shipping Options they will appear here.","woocommerce")),(0,r.createElement)(Nt.Button,{variant:"secondary",href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`,target:"_blank",rel:"noopener noreferrer"},(0,l.__)("Configure Shipping Options","woocommerce"))),ms=e=>{const t=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),o=0===t?(0,r.createElement)("span",{className:"wc-block-checkout__shipping-option--free"},(0,l.__)("Free","woocommerce")):(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:(0,_r.getCurrencyFromPriceResponse)(e),value:t});return{label:(0,Se.decodeEntities)(e.name),value:e.rate_id,description:(0,Se.decodeEntities)(e.description),secondaryLabel:o,secondaryDescription:(0,Se.decodeEntities)(e.delivery_time)}},ds=({noShippingPlaceholder:e=null})=>{const{isEditor:t}=k(),{shippingRates:o,needsShipping:n,isLoadingRates:s,hasCalculatedShipping:c,isCollectable:a}=st(),{shippingAddress:i}=jt(),m=a?o.map((e=>({...e,shipping_rates:e.shipping_rates.filter((e=>!We(e.method_id)))}))):o;if(!n)return null;const d=qe(o);if(!c&&!d)return(0,r.createElement)("p",null,(0,l.__)("Shipping options will be displayed here after entering your full shipping address.","woocommerce"));const p=Oe(i);return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.StoreNoticesContainer,{context:ye.SHIPPING_METHODS}),t&&!d?e:(0,r.createElement)(Hr,{noResultsMessage:(0,r.createElement)(r.Fragment,null,p?(0,r.createElement)(Mr,{isDismissible:!1,className:"wc-block-components-shipping-rates-control__no-results-notice",status:"warning"},(0,l.__)("There are no shipping options available. Please check your shipping address.","woocommerce")):(0,l.__)("Add a shipping address to view shipping options.","woocommerce")),renderOption:ms,collapsible:!1,shippingRates:m,isLoadingRates:s,context:"woocommerce/checkout"}))};o(2255);const ps={...Ao({defaultTitle:(0,l.__)("Shipping options","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};o(1299),(0,i.registerBlockType)("woocommerce/checkout-shipping-methods-block",{icon:{src:(0,r.createElement)(a.A,{icon:Qn.A,className:"wc-block-editor-components-block-icon"})},attributes:ps,edit:({attributes:e,setAttributes:t})=>{const o=(0,y.getSetting)("globalShippingMethods"),s=(0,y.getSetting)("activeShippingZones"),{showShippingMethods:c}=Ut();return c?(0,r.createElement)(qt,{attributes:e,setAttributes:t,className:(0,n.A)("wc-block-checkout__shipping-option",null==e?void 0:e.className)},(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Shipping Calculations","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Options that control shipping can be managed in your store settings.","woocommerce")),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=options`},(0,l.__)("Manage shipping options","woocommerce"))," "),o.length>0&&(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Methods","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("The following shipping integrations are active on your store.","woocommerce")),o.map((e=>(0,r.createElement)(tn,{key:e.id,href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=${e.id}`,title:e.title,description:e.description}))),(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`},(0,l.__)("Manage shipping methods","woocommerce"))),s.length&&(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Shipping Zones","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Shipping Zones can be made managed in your store settings.","woocommerce")),s.map((e=>(0,r.createElement)(tn,{key:e.id,href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&zone_id=${e.id}`,title:e.title,description:e.description}))))),(0,r.createElement)(Jt,null,(0,r.createElement)(ds,{noShippingPlaceholder:(0,r.createElement)(ls,null)})),(0,r.createElement)($t,{block:Tt.innerBlockAreas.SHIPPING_METHODS})):null},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});const us=({title:e,setSelectedOption:t,selectedOption:o,pickupLocations:n,onSelectRate:s,renderPickupLocation:c,packageCount:a})=>{const i=(0,_.useSelect)((e=>{var t,o,r;return null===(t=e(te.CART_STORE_KEY))||void 0===t||null===(o=t.getCartData())||void 0===o||null===(r=o.shippingRates)||void 0===r?void 0:r.length}))>1||document.querySelectorAll(".wc-block-components-local-pickup-select .wc-block-components-radio-control").length>1;return(0,r.createElement)("div",{className:"wc-block-components-local-pickup-select"},!(!i||!e)&&(0,r.createElement)("div",null,e),(0,r.createElement)(Kt.RadioControl,{onChange:e=>{t(e),s(e)},highlightChecked:!0,selected:o,options:n.map((e=>c(e,a)))}))};function hs(e){let t,o,r,n=[];for(let s=0;s<e.length;s++)t=e.substring(s),o=t.match(/^&[a-z0-9#]+;/),o?(r=o[0],n.push(r),s+=r.length-1):n.push(e[s]);return n}const gs=(e,t,o="...")=>{const r=function(e,t){const o=(t=t||{}).limit||100,r=void 0===t.preserveTags||t.preserveTags,n=void 0!==t.wordBreak&&t.wordBreak,s=t.suffix||"...",c=t.moreLink||"",a=t.moreText||"»",i=t.preserveWhiteSpace||!1,l=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let m,d,p,u,h,g,_=0,E=[],k=!1;for(let e=0;e<l.length;e++){if(m=l[e],u=i?m:m.replace(/[ ]+/g," "),!m.length)continue;const t=hs(u);if("<"!==m[0])if(_>=o)m="";else if(_+t.length>=o){if(d=o-_," "===t[d-1])for(;d&&(d-=1," "===t[d-1]););else p=t.slice(d).indexOf(" "),n||(-1!==p?d+=p:d=m.length);m=t.slice(0,d).join("")+s,c&&(m+='<a href="'+c+'" style="display:inline">'+a+"</a>"),_=o,k=!0}else _+=t.length;else if(r){if(_>=o)if(h=m.match(/[a-zA-Z]+/),g=h?h[0]:"",g)if("</"!==m.substring(0,2))E.push(g),m="";else{for(;E[E.length-1]!==g&&E.length;)E.pop();E.length&&(m=""),E.pop()}else m=""}else m="";l[e]=m}return{html:l.join("\n").replace(/\n/g,""),more:k}}(e,{suffix:o,limit:t});return r.html},_s=(e,t,o)=>(t<=o?e.start=e.middle+1:e.end=e.middle-1,e),Es=(e,t,o,r)=>{const n=((e,t,o)=>{let r={start:0,middle:0,end:e.length};for(;r.start<=r.end;)r.middle=Math.floor((r.start+r.end)/2),t.innerHTML=gs(e,r.middle),r=_s(r,t.clientHeight,o);return r.middle})(e,t,o);return gs(e,n-r.length,r)},ks={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,l.__)("Read less","woocommerce"),maxLines:3,moreText:(0,l.__)("Read more","woocommerce")};class ws extends d.Component{constructor(e){super(e),(0,I.A)(this,"reviewSummary",void 0),(0,I.A)(this,"reviewContent",void 0),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,d.createRef)(),this.reviewSummary=(0,d.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const o=(this.reviewSummary.current.clientHeight+1)*e+1,r=this.reviewContent.current.clientHeight+1>o;this.setState({clampEnabled:r}),r&&this.setState({summary:Es(this.reviewContent.current.innerHTML,this.reviewSummary.current,o,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:o,moreText:n}=this.props,s=e?o:n;if(s)return(0,r.createElement)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button"},s)}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:o,clampEnabled:n,isExpanded:s}=this.state;return t?!1===n?(0,r.createElement)("div",{className:e},(0,r.createElement)("div",{ref:this.reviewContent},t)):(0,r.createElement)("div",{className:e},(!s||null===n)&&(0,r.createElement)("div",{ref:this.reviewSummary,"aria-hidden":s,dangerouslySetInnerHTML:{__html:o}}),(s||null===n)&&(0,r.createElement)("div",{ref:this.reviewContent,"aria-hidden":!s},t),this.getButton()):null}}(0,I.A)(ws,"defaultProps",ks);const bs=ws,ys=(e,t)=>{const o=(0,y.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):e.price,n=(e=>{if(null!=e&&e.meta_data){const t=e.meta_data.find((e=>"pickup_location"===e.key));return t?t.value:""}return""})(e),s=(e=>{if(null!=e&&e.meta_data){const t=e.meta_data.find((e=>"pickup_address"===e.key));return t?t.value:""}return""})(e),c=(e=>{if(null!=e&&e.meta_data){const t=e.meta_data.find((e=>"pickup_details"===e.key));return t?t.value:""}return""})(e);let i=(0,r.createElement)("em",null,(0,l.__)("free","woocommerce"));return parseInt(o,10)>0&&(i=1===t?(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:(0,_r.getCurrencyFromPriceResponse)(e),value:o}):(0,d.createInterpolateElement)(/* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */ /* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */
(0,l._n)("<price/> x <packageCount/> package","<price/> x <packageCount/> packages",t,"woocommerce"),{price:(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:(0,_r.getCurrencyFromPriceResponse)(e),value:o}),packageCount:(0,r.createElement)(r.Fragment,null,t)})),{value:e.rate_id,label:n?(0,Se.decodeEntities)(n):(0,Se.decodeEntities)(e.name),secondaryLabel:i,description:s?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(a.A,{icon:Vt.A,className:"wc-block-editor-components-block-icon"}),(0,Se.decodeEntities)(s)):void 0,secondaryDescription:(0,r.createElement)(bs,{maxLines:2},(0,Se.decodeEntities)(c))}},vs=()=>{var e;const{shippingRates:t,selectShippingRate:o}=st(),n=((null===(e=t[0])||void 0===e?void 0:e.shipping_rates)||[]).filter(ze),[s,c]=(0,d.useState)((()=>{var e;return(null===(e=n.find((e=>e.selected)))||void 0===e?void 0:e.rate_id)||""})),a=(0,d.useCallback)((e=>{o(e)}),[o]),{extensions:i,receiveCart:l,...m}=He(),p={extensions:i,cart:m,components:{ShippingRatesControlPackage:Ur,LocalPickupSelect:us},renderPickupLocation:ys};(0,d.useEffect)((()=>{!s&&n[0]&&(c(n[0].rate_id),a(n[0].rate_id))}),[a,n,s]);const u=qe(t);return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Tt.ExperimentalOrderLocalPickupPackages.Slot,{...p}),(0,r.createElement)(Tt.ExperimentalOrderLocalPickupPackages,null,(0,r.createElement)(us,{title:t[0].name,setSelectedOption:c,onSelectRate:a,selectedOption:s,renderPickupLocation:ys,pickupLocations:n,packageCount:u})))},fs={...Ao({defaultTitle:(0,l.__)("Pickup locations","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};o(3425),(0,i.registerBlockType)("woocommerce/checkout-pickup-options-block",{icon:{src:(0,r.createElement)(a.A,{icon:es.A,className:"wc-block-editor-components-block-icon"})},attributes:fs,edit:({attributes:e,setAttributes:t})=>{const{prefersCollection:o}=(0,_.useSelect)((e=>({prefersCollection:e(te.CHECKOUT_STORE_KEY).prefersCollection()}))),{className:s}=e;return o&&V?(0,r.createElement)(qt,{attributes:e,setAttributes:t,className:(0,n.A)("wc-block-checkout__shipping-method",s)},(0,r.createElement)(vs,null),(0,r.createElement)($t,{block:Tt.innerBlockAreas.PICKUP_LOCATION})):null},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(zt,null))});const Cs=({className:e=""})=>{const{cartTotals:t}=He(),o=(0,_r.getCurrencyFromPriceResponse)(t);return(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(Kt.Subtotal,{currency:o,values:t}))};(0,i.registerBlockType)("woocommerce/checkout-order-summary-subtotal-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Cs,{className:t}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const Ss=({className:e=""})=>{const{cartFees:t,cartTotals:o}=He(),n=(0,_r.getCurrencyFromPriceResponse)(o);return(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(Kt.TotalsFees,{currency:n,cartFees:t}))};(0,i.registerBlockType)("woocommerce/checkout-order-summary-fee-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Ss,{className:t}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const As=()=>{const{extensions:e,receiveCart:t,...o}=He(),n={extensions:e,cart:o,context:"woocommerce/checkout"};return(0,r.createElement)(Tt.ExperimentalDiscountsMeta.Slot,{...n})},Ps=({className:e=""})=>{const{cartTotals:t,cartCoupons:o}=He(),{removeCoupon:n,isRemovingCoupon:s}=wn("wc/checkout"),c=(0,_r.getCurrencyFromPriceResponse)(t);return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(gr,{cartCoupons:o,currency:c,isRemovingCoupon:s,removeCoupon:n,values:t})),(0,r.createElement)(As,null))};(0,i.registerBlockType)("woocommerce/checkout-order-summary-discount-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Ps,{className:t}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const Ns=({className:e=""})=>{const{cartTotals:t,cartNeedsShipping:o}=He();if(!o)return null;const n=(0,_r.getCurrencyFromPriceResponse)(t);return(0,r.createElement)(Tt.TotalsWrapper,{className:e},(0,r.createElement)($r,{showCalculator:!1,showRateSelector:!1,values:t,currency:n,isCheckout:!0}))};(0,i.registerBlockType)("woocommerce/checkout-order-summary-shipping-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Jt,null,(0,r.createElement)(Ns,{className:t})))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});var Ts=o(8848);const Rs=({className:e=""})=>{const t=(0,y.getSetting)("couponsEnabled",!0),{applyCoupon:o,isApplyingCoupon:n}=wn("wc/checkout");return t?(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(ur,{onSubmit:o,isLoading:n})):null};(0,i.registerBlockType)("woocommerce/checkout-order-summary-coupon-form-block",{icon:{src:(0,r.createElement)(a.A,{icon:Ts.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Jt,null,(0,r.createElement)(Rs,{className:t})))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const xs=({className:e,showRateAfterTaxName:t})=>{const{cartTotals:o}=He();if((0,y.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(o.total_tax,10)<=0)return null;const n=(0,_r.getCurrencyFromPriceResponse)(o);return(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(Kt.TotalsTaxes,{showRateAfterTaxName:t,currency:n,values:o}))},Is={showRateAfterTaxName:{type:"boolean",default:(0,y.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!0}}};(0,i.registerBlockType)("woocommerce/checkout-order-summary-taxes-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},attributes:Is,edit:({attributes:e,setAttributes:t})=>{const{className:o,showRateAfterTaxName:n}=e,s=(0,m.useBlockProps)(),c=(0,y.getSetting)("taxesEnabled"),a=(0,y.getSetting)("displayItemizedTaxes",!1),i=(0,y.getSetting)("displayCartPricesIncludingTax",!1);return(0,r.createElement)("div",{...s},(0,r.createElement)(m.InspectorControls,null,c&&a&&!i&&(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Taxes","woocommerce")},(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Show rate after tax name","woocommerce"),help:(0,l.__)("Show the percentage rate alongside each tax line in the summary.","woocommerce"),checked:n,onChange:()=>t({showRateAfterTaxName:!n})}))),(0,r.createElement)(xs,{className:o,showRateAfterTaxName:n}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})});const Os=(0,r.createElement)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,r.createElement)("path",{d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"}));o(4567);const Ms=({currency:e,maxPrice:t,minPrice:o,priceClassName:s,priceStyle:c={}})=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"screen-reader-text"},(0,l.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,l.__)("Price between %1$s and %2$s","woocommerce"),(0,_r.formatPrice)(o),(0,_r.formatPrice)(t))),(0,r.createElement)("span",{"aria-hidden":!0},(0,r.createElement)(Kt.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",s),currency:e,value:o,style:c})," — ",(0,r.createElement)(Kt.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",s),currency:e,value:t,style:c}))),Bs=({currency:e,regularPriceClassName:t,regularPriceStyle:o,regularPrice:s,priceClassName:c,priceStyle:a,price:i})=>(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Previous price:","woocommerce")),(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,r.createElement)("del",{className:(0,n.A)("wc-block-components-product-price__regular",t),style:o},e),value:s}),(0,r.createElement)("span",{className:"screen-reader-text"},(0,l.__)("Discounted price:","woocommerce")),(0,r.createElement)(Kt.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,r.createElement)("ins",{className:(0,n.A)("wc-block-components-product-price__value","is-discounted",c),style:a},e),value:i})),Fs=({align:e,className:t,currency:o,format:s="<price/>",maxPrice:c,minPrice:a,price:i,priceClassName:l,priceStyle:m,regularPrice:p,regularPriceClassName:u,regularPriceStyle:h,style:g})=>{const _=(0,n.A)(t,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});s.includes("<price/>")||(s="<price/>",console.error("Price formats need to include the `<price/>` tag."));const E=p&&i&&i<p;let k=(0,r.createElement)("span",{className:(0,n.A)("wc-block-components-product-price__value",l)});return E?k=(0,r.createElement)(Bs,{currency:o,price:i,priceClassName:l,priceStyle:m,regularPrice:p,regularPriceClassName:u,regularPriceStyle:h}):void 0!==a&&void 0!==c?k=(0,r.createElement)(Ms,{currency:o,maxPrice:c,minPrice:a,priceClassName:l,priceStyle:m}):i&&(k=(0,r.createElement)(Kt.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",l),currency:o,value:i,style:m})),(0,r.createElement)("span",{className:_,style:g},(0,d.createInterpolateElement)(s,{price:k}))};o(6625);const Ds=({className:e="",disabled:t=!1,name:o,permalink:s="",target:c,rel:a,style:i,onClick:l,...m})=>{const d=(0,n.A)("wc-block-components-product-name",e);if(t){const e=m;return(0,r.createElement)("span",{className:d,...e,dangerouslySetInnerHTML:{__html:(0,Se.decodeEntities)(o)}})}return(0,r.createElement)("a",{className:d,href:s,target:c,...m,dangerouslySetInnerHTML:{__html:(0,Se.decodeEntities)(o)},style:i})};var Ls=o(131);o(9507);const Ys=({children:e,className:t})=>(0,r.createElement)("div",{className:(0,n.A)("wc-block-components-product-badge",t)},e),Vs=()=>(0,r.createElement)(Ys,{className:"wc-block-components-product-backorder-badge"},(0,l.__)("Available on backorder","woocommerce")),js=({image:e={},fallbackAlt:t=""})=>{const o=e.thumbnail?{src:e.thumbnail,alt:(0,Se.decodeEntities)(e.alt)||t||"Product Image"}:{src:y.PLACEHOLDER_IMG_SRC,alt:""};return(0,r.createElement)("img",{...o,alt:o.alt})},Us=({lowStockRemaining:e})=>e?(0,r.createElement)(Ys,{className:"wc-block-components-product-low-stock-badge"},(0,l.sprintf)(/* translators: %d stock amount (number of items in stock for product) */ /* translators: %d stock amount (number of items in stock for product) */
(0,l.__)("%d left in stock","woocommerce"),e)):null;var Ks=o(1194);o(4982);const Hs=({details:e=[]})=>Array.isArray(e)?0===(e=e.filter((e=>!e.hidden))).length?null:(0,r.createElement)("ul",{className:"wc-block-components-product-details"},e.map((e=>{const t=(null==e?void 0:e.key)||e.name||"",o=(null==e?void 0:e.className)||(t?`wc-block-components-product-details__${(0,Ks.c)(t)}`:"");return(0,r.createElement)("li",{key:t+(e.display||e.value),className:o},t&&(0,r.createElement)(r.Fragment,null,(0,r.createElement)("span",{className:"wc-block-components-product-details__name"},(0,Se.decodeEntities)(t),":")," "),(0,r.createElement)("span",{className:"wc-block-components-product-details__value"},(0,Se.decodeEntities)(e.display||e.value)))}))):null,qs=window.wp.wordcount,$s=({source:e,maxLength:t=15,countType:o="words",className:n="",style:s={}})=>{const c=(0,d.useMemo)((()=>((e,t=15,o="words")=>{const r=(0,on.autop)(e);if((0,qs.count)(r,o)<=t)return r;const n=(e=>{const t=e.indexOf("</p>");return-1===t?e:e.substr(0,t+4)})(r);return(0,qs.count)(n,o)<=t?n:"words"===o?sn(n,t):cn(n,t,"characters_including_spaces"===o)})(e,t,o)),[e,t,o]);return(0,r.createElement)(d.RawHTML,{style:s,className:n},c)},zs=({className:e,shortDescription:t="",fullDescription:o=""})=>{const n=t||o;return n?(0,r.createElement)($s,{className:e,source:n,maxLength:15,countType:O.wordCountType||"words"}):null};o(401);const Ws=({shortDescription:e="",fullDescription:t="",itemData:o=[],variation:n=[]})=>(0,r.createElement)("div",{className:"wc-block-components-product-metadata"},(0,r.createElement)(zs,{className:"wc-block-components-product-metadata__description",shortDescription:e,fullDescription:t}),(0,r.createElement)(Hs,{details:o}),(0,r.createElement)(Hs,{details:n.map((({attribute:e="",value:t})=>({key:e,value:t})))})),Gs=({cartItem:e})=>{const{images:t,low_stock_remaining:o,show_backorder_badge:s,name:c,permalink:a,prices:i,quantity:m,short_description:p,description:u,item_data:h,variation:g,totals:_,extensions:E}=e,{receiveCart:k,...w}=He(),b=(0,d.useMemo)((()=>({context:"summary",cartItem:e,cart:w})),[e,w]),v=(0,_r.getCurrencyFromPriceResponse)(i),f=(0,Tt.applyCheckoutFilter)({filterName:"itemName",defaultValue:c,extensions:E,arg:b}),C=(0,Ls.A)({amount:parseInt(i.raw_prices.regular_price,10),precision:(0,we.isString)(i.raw_prices.precision)?parseInt(i.raw_prices.precision,10):i.raw_prices.precision}).convertPrecision(v.minorUnit).getAmount(),S=(0,Ls.A)({amount:parseInt(i.raw_prices.price,10),precision:(0,we.isString)(i.raw_prices.precision)?parseInt(i.raw_prices.precision,10):i.raw_prices.precision}).convertPrecision(v.minorUnit).getAmount(),A=(0,_r.getCurrencyFromPriceResponse)(_);let P=parseInt(_.line_subtotal,10);(0,y.getSetting)("displayCartPricesIncludingTax",!1)&&(P+=parseInt(_.line_subtotal_tax,10));const N=(0,Ls.A)({amount:P,precision:A.minorUnit}).getAmount(),T=(0,Tt.applyCheckoutFilter)({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:E,arg:b,validation:Tt.productPriceValidation}),R=(0,Tt.applyCheckoutFilter)({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:E,arg:b,validation:Tt.productPriceValidation}),x=(0,Tt.applyCheckoutFilter)({filterName:"cartItemClass",defaultValue:"",extensions:E,arg:b});return(0,r.createElement)("div",{className:(0,n.A)("wc-block-components-order-summary-item",x)},(0,r.createElement)("div",{className:"wc-block-components-order-summary-item__image"},(0,r.createElement)("div",{className:"wc-block-components-order-summary-item__quantity"},(0,r.createElement)(Kt.Label,{label:m.toString(),screenReaderLabel:(0,l.sprintf)(/* translators: %d number of products of the same type in the cart */ /* translators: %d number of products of the same type in the cart */
(0,l._n)("%d item","%d items",m,"woocommerce"),m)})),(0,r.createElement)(js,{image:t.length?t[0]:{},fallbackAlt:f})),(0,r.createElement)("div",{className:"wc-block-components-order-summary-item__description"},(0,r.createElement)(Ds,{disabled:!0,name:f,permalink:a}),(0,r.createElement)(Fs,{currency:v,price:S,regularPrice:C,className:"wc-block-components-order-summary-item__individual-prices",priceClassName:"wc-block-components-order-summary-item__individual-price",regularPriceClassName:"wc-block-components-order-summary-item__regular-individual-price",format:T}),s?(0,r.createElement)(Vs,null):!!o&&(0,r.createElement)(Us,{lowStockRemaining:o}),(0,r.createElement)(Ws,{shortDescription:p,fullDescription:u,itemData:h,variation:g})),(0,r.createElement)("span",{className:"screen-reader-text"},(0,l.sprintf)(/* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */ /* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */
(0,l._n)("Total price for %1$d %2$s item: %3$s","Total price for %1$d %2$s items: %3$s",m,"woocommerce"),m,f,(0,_r.formatPrice)(N,A))),(0,r.createElement)("div",{className:"wc-block-components-order-summary-item__total-price","aria-hidden":"true"},(0,r.createElement)(Fs,{currency:A,format:R,price:N})))};o(5415);const Zs=({cartItems:e=[]})=>{const{isLarge:t,hasContainerWidth:o}=(0,d.useContext)(u);return o?(0,r.createElement)(Kt.Panel,{className:"wc-block-components-order-summary",initialOpen:t,hasBorder:!1,title:(0,r.createElement)("span",{className:"wc-block-components-order-summary__button-text"},(0,l.__)("Order summary","woocommerce"))},(0,r.createElement)("div",{className:"wc-block-components-order-summary__content"},e.map((e=>(0,r.createElement)(Gs,{key:e.key,cartItem:e}))))):null},Xs=({className:e=""})=>{const{cartItems:t}=He();return(0,r.createElement)(Kt.TotalsWrapper,{className:e},(0,r.createElement)(Zs,{cartItems:t}))};(0,i.registerBlockType)("woocommerce/checkout-order-summary-cart-items-block",{icon:{src:(0,r.createElement)(a.A,{icon:Os,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,m.useBlockProps)();return(0,r.createElement)("div",{...o},(0,r.createElement)(Xs,{className:t}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()})}),o(5681),(0,i.registerBlockType)("woocommerce/checkout-order-summary-totals-block",{icon:{src:(0,r.createElement)(a.A,{icon:dr,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,m.useBlockProps)(),o=Dt(Tt.innerBlockAreas.CHECKOUT_ORDER_SUMMARY_TOTALS),n=[["woocommerce/checkout-order-summary-subtotal-block",{},[]],["woocommerce/checkout-order-summary-fee-block",{},[]],["woocommerce/checkout-order-summary-discount-block",{},[]],["woocommerce/checkout-order-summary-shipping-block",{},[]],["woocommerce/checkout-order-summary-taxes-block",{},[]]];return Lt({clientId:e,registeredBlocks:o,defaultTemplate:n}),(0,r.createElement)("div",{...t},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:o,template:n}))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save()},(0,r.createElement)(m.InnerBlocks.Content,null))}),o(6201);var Js=o(8558);const Qs=(e,t=!0)=>{t?window.document.body.classList.add(e):window.document.body.classList.remove(e)},ec=({attributes:e,setAttributes:t})=>{const{hasDarkControls:o}=e;return(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Style","woocommerce")},(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Dark mode inputs","woocommerce"),help:(0,l.__)("Inputs styled specifically for use on dark background colors.","woocommerce"),checked:o,onChange:()=>t({hasDarkControls:!o})})))};function tc(){const e=(0,l.__)("Your store does not have any payment methods that support the Checkout block. Once you have configured a compatible payment method it will be displayed here.","woocommerce");return(0,r.createElement)(Nt.Notice,{className:"wc-blocks-no-payment-methods-notice",status:"warning",spokenMessage:e,isDismissible:!1},(0,r.createElement)("div",{className:"wc-blocks-no-payment-methods-notice__content"},e," ",(0,r.createElement)(Nt.ExternalLink,{href:`${y.ADMIN_URL}admin.php?page=wc-settings&tab=checkout`},(0,l.__)("Configure Payment Methods","woocommerce"))))}o(7414);const oc=window.wp.editor,rc=window.wp.coreData;function nc({block:e}){const t="checkout"===e?B:L,o="checkout"===e?"woocommerce_checkout_page_id":"woocommerce_cart_page_id",{saveEntityRecord:n}=(0,_.useDispatch)(rc.store),{editPost:s,savePost:c}=(0,_.useDispatch)(oc.store),{slug:a,postPublished:i,currentPostId:m}=(0,_.useSelect)((o=>{var r;const{getEntityRecord:n}=o(rc.store),{isCurrentPostPublished:s,getCurrentPostId:c}=o(oc.store);return{slug:(null===(r=n("postType","page",t))||void 0===r?void 0:r.slug)||e,postPublished:s(),currentPostId:c()}}),[]),[p,u]=(0,d.useState)("pristine"),h=(0,d.useCallback)((()=>{u("updating"),Promise.resolve().then((()=>yt()({path:`/wc/v3/settings/advanced/${o}`,method:"GET"}))).catch((e=>{"rest_setting_setting_invalid"===e.code&&u("error")})).then((()=>{if(!i)return s({status:"publish"}),c()})).then((()=>yt()({path:`/wc/v3/settings/advanced/${o}`,method:"POST",data:{value:m.toString()}}))).then((()=>{if(0!==t)return n("postType","page",{id:t,slug:`${a}-2`})})).then((()=>s({slug:a}))).then((()=>c())).then((()=>u("updated")))}),[i,s,c,o,m,t,n,a]);let g;return g="checkout"===e?(0,d.createInterpolateElement)((0,l.__)("If you would like to use this block as your default checkout, <a>update your page settings</a>.","woocommerce"),{a:(0,r.createElement)("a",{href:"#",onClick:h},(0,l.__)("update your page settings","woocommerce"))}):(0,d.createInterpolateElement)((0,l.__)("If you would like to use this block as your default cart, <a>update your page settings</a>.","woocommerce"),{a:(0,r.createElement)("a",{href:"#",onClick:h},(0,l.__)("update your page settings","woocommerce"))}),"string"==typeof pagenow&&"site-editor"===pagenow||m===t||"dismissed"===p?null:(0,r.createElement)(Nt.Notice,{className:"wc-default-page-notice",status:"updated"===p?"success":"info",onRemove:()=>u("dismissed"),spokenMessage:"updated"===p?(0,l.__)("Page settings updated","woocommerce"):g},"updated"===p?(0,l.__)("Page settings updated","woocommerce"):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("p",null,g)))}o(4688);const sc=[],cc=e=>{const[t,o,r]=(()=>{const e={};(0,y.getSetting)("incompatibleExtensions")&&(0,y.getSetting)("incompatibleExtensions").forEach((t=>{e[t.id]=t.title}));const t=Object.keys(e),o=t.length;return[e,t,o]})(),[n,s,c]=(()=>{const{incompatiblePaymentMethods:e}=(0,_.useSelect)((e=>{const{getIncompatiblePaymentMethods:t}=e(Sn);return{incompatiblePaymentMethods:t()}}),[]),t=Object.keys(e);return[e,t,t.length]})(),a={...t,...n},i=[...o,...s],l=r+c,[m,p]=((e,t)=>{const[o,r]=(0,d.useState)((()=>{const o=window.localStorage.getItem(e);if(o)try{return JSON.parse(o)}catch{console.error(`Value for key '${e}' could not be retrieved from localStorage because it can't be parsed.`)}return t}));return(0,d.useEffect)((()=>{try{window.localStorage.setItem(e,JSON.stringify(o))}catch{console.error(`Value for key '${e}' could not be saved in localStorage because it can't be converted into a string.`)}}),[e,o]),[o,r]})("wc-blocks_dismissed_incompatible_extensions_notices",sc),[u,h]=(0,d.useState)(!1),g=m.some((t=>{return Object.keys(t).includes(e)&&(o=t[e],r=i,o.length===r.length&&new Set([...o,...r]).size===o.length);var o,r})),E=0===l||g;return(0,d.useEffect)((()=>{h(!E),E||g||p((t=>t.reduce(((t,o)=>(Object.keys(o).includes(e)||t.push(o),t)),[])))}),[E,g,p,e]),[u,()=>{const t=new Set(m);t.add({[e]:i}),p([...t])},(k=a,Object.fromEntries(Object.entries(k).sort((([,e],[,t])=>e.localeCompare(t))))),l];var k},ac=window.wp.notices;var ic=o(4922),lc=o.n(ic);const mc=!1;lc()("wc-admin:tracks:stats");const dc=lc()("wc-admin:tracks");function pc(e,t){return dc("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!(mc||!window._tkq||!window.wcTracks||!window.wcTracks.isEnabled)}),!(!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)&&(mc?(window.wcTracks.validateEvent(e,t),!1):void window.wcTracks.recordEvent(e,t))}const uc=({blocks:e,findCondition:t})=>{for(const o of e){if(t(o))return o;if(o.innerBlocks){const e=uc({blocks:o.innerBlocks,findCondition:t});if(e)return e}}},hc=({blockType:e="woocommerce/cart"})=>"woocommerce/cart"===e?(0,r.createElement)("p",null,(0,l.__)("If you continue, the cart block will be replaced with the classic experience powered by shortcodes. This means that you may lose customizations that you made to the cart block.","woocommerce")):(0,r.createElement)(r.Fragment,null,(0,r.createElement)("p",null,(0,l.__)("If you continue, the checkout block will be replaced with the classic experience powered by shortcodes. This means that you may lose:","woocommerce")),(0,r.createElement)("ul",{className:"cross-list"},(0,r.createElement)("li",null,(0,l.__)("Customizations and updates to the block","woocommerce")),(0,r.createElement)("li",null,(0,l.__)("Additional local pickup options created for the new checkout","woocommerce"))));function gc({block:e,clientId:t,type:o}){const{createInfoNotice:n}=(0,_.useDispatch)(ac.store),{replaceBlock:s,selectBlock:c}=(0,_.useDispatch)(m.store),[a,p]=(0,d.useState)(!1),u=()=>p(!1),{undo:h}=(0,_.useDispatch)(rc.store),[,,g,E]=cc(e),k="woocommerce/cart"===e,w=k?(0,l.__)("Switch to classic cart","woocommerce"):(0,l.__)("Switch to classic checkout","woocommerce"),b=k?(0,l.__)("Switched to classic cart.","woocommerce"):(0,l.__)("Switched to classic checkout.","woocommerce"),y=k?"cart":"checkout",v={shortcode:y,notice:"incompatible"===o?"incompatible_notice":"generic_notice",incompatible_extensions_count:E,incompatible_extensions_names:JSON.stringify(g)},{getBlocks:f}=(0,_.useSelect)((e=>({getBlocks:e(m.store).getBlocks})),[]),C=()=>{h(),pc("switch_to_classic_shortcode_undo",v)};return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(Nt.Button,{variant:"secondary",onClick:()=>{pc("switch_to_classic_shortcode_click",v),p(!0)}},w),a&&(0,r.createElement)(Nt.Modal,{size:"medium",title:w,onRequestClose:u,className:"wc-blocks-switch-to-classic-shortcode-modal-content"},(0,r.createElement)(hc,{blockType:e}),(0,r.createElement)(Nt.TabbableContainer,{className:"wc-blocks-switch-to-classic-shortcode-modal-actions"},(0,r.createElement)(Nt.Button,{variant:"primary",isDestructive:!0,onClick:()=>{s(t,(0,i.createBlock)("woocommerce/classic-shortcode",{shortcode:y})),pc("switch_to_classic_shortcode_confirm",v),(()=>{const e=uc({blocks:f(),findCondition:e=>"woocommerce/classic-shortcode"===e.name});e&&c(e.clientId)})(),n(b,{actions:[{label:(0,l.__)("Undo","woocommerce"),onClick:C}],type:"snackbar"}),u()}},(0,l.__)("Switch","woocommerce"))," ",(0,r.createElement)(Nt.Button,{variant:"secondary",onClick:()=>{pc("switch_to_classic_shortcode_cancel",v),u()}},(0,l.__)("Cancel","woocommerce")))))}function _c({block:e,clientId:t}){const[o,n,s,c]=cc(e);if(!o)return null;const i=(0,r.createElement)(r.Fragment,null,c>1?(0,d.createInterpolateElement)((0,l.__)("Some active extensions do not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),{a:(0,r.createElement)(Nt.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})}):(0,d.createInterpolateElement)((0,l.sprintf)(
// translators: %s is the name of the extension.
// translators: %s is the name of the extension.
(0,l.__)("<strong>%s</strong> does not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),Object.values(s)[0]),{strong:(0,r.createElement)("strong",null),a:(0,r.createElement)(Nt.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})})),m=Object.entries(s),p=m.length-2;return(0,r.createElement)(Nt.Notice,{className:"wc-blocks-incompatible-extensions-notice",status:"warning",onRemove:n,spokenMessage:i},(0,r.createElement)("div",{className:"wc-blocks-incompatible-extensions-notice__content"},(0,r.createElement)(a.A,{className:"wc-blocks-incompatible-extensions-notice__warning-icon",icon:(0,r.createElement)(en,null)}),(0,r.createElement)("div",null,(0,r.createElement)("p",null,i),c>1&&(0,r.createElement)("ul",null,m.slice(0,2).map((([e,t])=>(0,r.createElement)("li",{key:e,className:"wc-blocks-incompatible-extensions-notice__element"},t)))),m.length>2&&(0,r.createElement)("details",null,(0,r.createElement)("summary",null,(0,r.createElement)("span",null,(0,l.sprintf)(
// translators: %s is the number of incompatible extensions.
// translators: %s is the number of incompatible extensions.
(0,l._n)("%s more incompatibility","%s more incompatibilities",p,"woocommerce"),p)),(0,r.createElement)(a.A,{icon:Qt.A})),(0,r.createElement)("ul",null,m.slice(2).map((([e,t])=>(0,r.createElement)("li",{key:e,className:"wc-blocks-incompatible-extensions-notice__element"},t))))),(0,r.createElement)(gc,{block:e,clientId:t,type:"incompatible"}))))}o(7840),o(1212);var Ec=o(361);o(1598);const kc=({text:e,title:t=(0,l.__)("Feedback?","woocommerce"),url:o})=>{const[n,s]=(0,d.useState)(!1);return(0,d.useEffect)((()=>{s(!0)}),[]),(0,r.createElement)(r.Fragment,null,n&&(0,r.createElement)("div",{className:"wc-block-feedback-prompt"},(0,r.createElement)(a.A,{icon:Ec.A}),(0,r.createElement)("h2",{className:"wc-block-feedback-prompt__title"},t),(0,r.createElement)("p",{className:"wc-block-feedback-prompt__text"},e),(0,r.createElement)("a",{href:o,className:"wc-block-feedback-prompt__link",rel:"noreferrer noopener",target:"_blank"},(0,l.__)("Give us your feedback.","woocommerce"),(0,r.createElement)(a.A,{icon:Jr.A,size:16}))))},wc=()=>(0,r.createElement)(kc,{text:(0,l.__)("We are currently working on improving our cart and checkout blocks to provide merchants with the tools and customization options they need.","woocommerce"),url:"https://github.com/woocommerce/woocommerce/discussions/new?category=checkout-flow&labels=type%3A+product%20feedback"}),bc=(0,p.createHigherOrderComponent)((e=>t=>{const{clientId:o,name:n,isSelected:s}=t,{isCart:c,isCheckout:a,isPaymentMethodsBlock:i,hasPaymentMethods:l,parentId:d}=(0,_.useSelect)((e=>{const{getBlockParentsByBlockName:t,getBlockName:r}=e(m.store),n=t(o,["woocommerce/cart","woocommerce/checkout"]).reduce(((e,t)=>(e[r(t)]=t,e)),{}),s=r(o),c=Object.keys(n).includes("woocommerce/cart"),a=Object.keys(n).includes("woocommerce/checkout"),i="woocommerce/cart"===s||c,l=i?"woocommerce/cart":"woocommerce/checkout";return{isCart:i,isCheckout:"woocommerce/checkout"===s||a,parentId:s===l?o:n[l],isPaymentMethodsBlock:"woocommerce/checkout-payment-block"===s,hasPaymentMethods:e(te.PAYMENT_STORE_KEY).paymentMethodsInitialized()&&Object.keys(e(te.PAYMENT_STORE_KEY).getAvailablePaymentMethods()).length>0}}));return n.startsWith("woocommerce/")&&s&&(c||a)?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(_c,{block:c?"woocommerce/cart":"woocommerce/checkout",clientId:d}),(0,r.createElement)(nc,{block:a?"checkout":"cart"}),i&&!l&&(0,r.createElement)(tc,null),(0,r.createElement)(wc,null)),(0,r.createElement)(e,{key:"edit",...t})):(0,r.createElement)(e,{key:"edit",...t})}),"withSidebarNotices");(0,rt.hasFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice")||(0,rt.addFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice",bc,11),(0,rt.hasFilter)("blocks.registerBlockType","core/lock/addAttribute")||(0,_.subscribe)((()=>{var e,t,o,r;const n=(0,_.select)(m.store);if(!n)return;const s=n.getSelectedBlock();s&&(Qs("wc-lock-selected-block--remove",!(null==s||null===(e=s.attributes)||void 0===e||null===(t=e.lock)||void 0===t||!t.remove)),Qs("wc-lock-selected-block--move",!(null==s||null===(o=s.attributes)||void 0===o||null===(r=o.lock)||void 0===r||!r.move)))}));const yc=["woocommerce/checkout-fields-block","woocommerce/checkout-totals-block"],vc={hasDarkControls:{type:"boolean",default:(0,y.getSetting)("hasDarkEditorStyleSupport",!1)},showRateAfterTaxName:{type:"boolean",default:(0,y.getSetting)("displayCartPricesIncludingTax",!1)}},fc={showOrderNotes:{type:"boolean",default:!0},showPolicyLinks:{type:"boolean",default:!0},showReturnToCart:{type:"boolean",default:!0},cartPageId:{type:"number",default:0}},Cc=JSON.parse('{"name":"woocommerce/checkout","version":"1.0.0","title":"Checkout","description":"Display a checkout form so your customers can submit orders.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"align":["wide"],"html":false,"multiple":false},"example":{"attributes":{"isPreview":true},"viewportWidth":800},"attributes":{"isPreview":{"type":"boolean","default":false,"save":false},"showCompanyField":{"type":"boolean","default":false},"requireCompanyField":{"type":"boolean","default":false},"showApartmentField":{"type":"boolean","default":true},"requireApartmentField":{"type":"boolean","default":false},"showPhoneField":{"type":"boolean","default":true},"requirePhoneField":{"type":"boolean","default":false},"align":{"type":"string","default":"wide"},"showFormStepNumbers":{"type":"boolean","default":false}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),Sc={icon:{src:(0,r.createElement)(a.A,{icon:c,className:"wc-block-editor-components-block-icon"})},attributes:{...Cc.attributes,...vc,...fc},edit:({clientId:e,attributes:t,setAttributes:o})=>{const{showCompanyField:s,requireCompanyField:c,showApartmentField:a,requireApartmentField:p,showPhoneField:u,requirePhoneField:h,showOrderNotes:E,showPolicyLinks:k,showReturnToCart:b,showRateAfterTaxName:y,cartPageId:v,isPreview:f=!1,showFormStepNumbers:C=!1}=t,S=(0,d.useRef)((0,Te.getQueryArg)(window.location.href,"focus"));(0,d.useEffect)((()=>{"checkout"!==S.current||(0,_.select)("core/block-editor").hasSelectedBlock()||((0,_.dispatch)("core/block-editor").selectBlock(e),(0,_.dispatch)("core/interface").enableComplementaryArea("core/edit-site","edit-site/block-inspector"))}),[e]);const A=e=>{const r={};r[e]=!t[e],o(r)},P=((e={})=>{const t=(0,d.useRef)(),o=(0,m.useBlockProps)({ref:t,...e});return(({ref:e})=>{const t=(0,rt.hasFilter)("blocks.registerBlockType","core/lock/addAttribute"),o=e.current;(0,d.useEffect)((()=>{if(o&&!t)return o.addEventListener("keydown",e,{capture:!0,passive:!1}),()=>{o.removeEventListener("keydown",e,{capture:!0})};function e(e){const{keyCode:t,target:o}=e;if(!(o instanceof HTMLElement))return;if(t!==Js.BACKSPACE&&t!==Js.DELETE)return;if((0,Gt.isTextField)(o))return;const r=o;if(void 0===r.dataset.block)return;const n=(e=>{var t,o,r,n,s;if(!e)return!1;const{getBlock:c}=(0,_.select)(m.store),a=c(e);if("boolean"==typeof(null==a||null===(t=a.attributes)||void 0===t||null===(o=t.lock)||void 0===o?void 0:o.remove))return a.attributes.lock.remove;const l=(0,i.getBlockType)(a.name);var d,p,u;return"boolean"==typeof(null==l||null===(r=l.attributes)||void 0===r||null===(n=r.lock)||void 0===n||null===(s=n.default)||void 0===s?void 0:s.remove)&&(null==l||null===(d=l.attributes)||void 0===d||null===(p=d.lock)||void 0===p||null===(u=p.default)||void 0===u?void 0:u.remove)})(r.dataset.block);n&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation())}}),[o,t])})({ref:t}),o})();return(0,r.createElement)("div",{...P},(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(ec,{attributes:t,setAttributes:o})),(0,r.createElement)(w,{isPreview:f,previewData:{previewCart:ot,previewSavedPaymentMethods:Pt}},(0,r.createElement)(Tt.SlotFillProvider,null,(0,r.createElement)(At,null,(0,r.createElement)(g,{className:(0,n.A)("wc-block-checkout",{"has-dark-controls":t.hasDarkControls})},(0,r.createElement)(Ot.Provider,{value:{addressFieldControls:()=>(0,r.createElement)(m.InspectorControls,null,(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Form Step Options","woocommerce")},(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Show form step numbers","woocommerce"),checked:C,onChange:()=>o({showFormStepNumbers:!C})})),(0,r.createElement)(Nt.PanelBody,{title:(0,l.__)("Address Fields","woocommerce")},(0,r.createElement)("p",{className:"wc-block-checkout__controls-text"},(0,l.__)("Show or hide fields in the checkout address forms.","woocommerce")),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Company","woocommerce"),checked:s,onChange:()=>A("showCompanyField")}),s&&(0,r.createElement)(Nt.RadioControl,{selected:c,options:[{label:(0,l.__)("Optional","woocommerce"),value:!1},{label:(0,l.__)("Required","woocommerce"),value:!0}],onChange:()=>A("requireCompanyField"),className:"components-base-control--nested wc-block-components-require-company-field"}),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Address line 2","woocommerce"),checked:a,onChange:()=>A("showApartmentField")}),a&&(0,r.createElement)(Nt.RadioControl,{selected:p,options:[{label:(0,l.__)("Optional","woocommerce"),value:!1},{label:(0,l.__)("Required","woocommerce"),value:!0}],onChange:()=>A("requireApartmentField"),className:"components-base-control--nested wc-block-components-require-apartment-field"}),(0,r.createElement)(Nt.ToggleControl,{label:(0,l.__)("Phone","woocommerce"),checked:u,onChange:()=>A("showPhoneField")}),u&&(0,r.createElement)(Nt.RadioControl,{selected:h,options:[{label:(0,l.__)("Optional","woocommerce"),value:!1},{label:(0,l.__)("Required","woocommerce"),value:!0}],onChange:()=>A("requirePhoneField"),className:"components-base-control--nested wc-block-components-require-phone-field"})))}},(0,r.createElement)(It.Provider,{value:{showApartmentField:a,showCompanyField:s,showPhoneField:u,requireApartmentField:p,requireCompanyField:c,requirePhoneField:h,showOrderNotes:E,showPolicyLinks:k,showReturnToCart:b,cartPageId:v,showRateAfterTaxName:y,showFormStepNumbers:C}},(0,r.createElement)(m.InnerBlocks,{allowedBlocks:yc,template:[["woocommerce/checkout-totals-block",{},[]],["woocommerce/checkout-fields-block",{},[]]],templateLock:"insert"}))))))))},save:()=>(0,r.createElement)("div",{...m.useBlockProps.save({className:"wc-block-checkout is-loading"})},(0,r.createElement)(m.InnerBlocks.Content,null)),transforms:{to:[{type:"block",blocks:["woocommerce/classic-shortcode"],transform:e=>(0,i.createBlock)("woocommerce/classic-shortcode",{shortcode:"checkout",align:e.align},[])}]},deprecated:[{attributes:{...Cc.attributes,...vc,...fc},save:({attributes:e})=>(0,r.createElement)("div",{className:(0,n.A)("is-loading",e.className)}),migrate:e=>{const{showOrderNotes:t,showPolicyLinks:o,showReturnToCart:r,cartPageId:n}=e;return[e,[(0,i.createBlock)("woocommerce/checkout-fields-block",{},[(0,i.createBlock)("woocommerce/checkout-express-payment-block",{},[]),(0,i.createBlock)("woocommerce/checkout-contact-information-block",{},[]),(0,i.createBlock)("woocommerce/checkout-shipping-address-block",{},[]),(0,i.createBlock)("woocommerce/checkout-billing-address-block",{},[]),(0,i.createBlock)("woocommerce/checkout-shipping-methods-block",{},[]),(0,i.createBlock)("woocommerce/checkout-payment-block",{},[]),(0,i.createBlock)("woocommerce/checkout-additional-information-block",{},[]),!!t&&(0,i.createBlock)("woocommerce/checkout-order-note-block",{},[]),!!o&&(0,i.createBlock)("woocommerce/checkout-terms-block",{},[]),(0,i.createBlock)("woocommerce/checkout-actions-block",{showReturnToCart:r,cartPageId:n},[])].filter(Boolean)),(0,i.createBlock)("woocommerce/checkout-totals-block",{})]]},isEligible:(e,t)=>!t.some((e=>"woocommerce/checkout-fields-block"===e.name))},{save:({attributes:e})=>(0,r.createElement)("div",{className:(0,n.A)("is-loading",e.className)}),isEligible:(e,t)=>{const o=t.find((e=>"woocommerce/checkout-fields-block"===e.name));return!!o&&!o.innerBlocks.some((e=>"woocommerce/checkout-additional-information-block"===e.name))},migrate:(e,t)=>{const o=t.findIndex((e=>"woocommerce/checkout-fields-block"===e.name));if(-1===o)return!1;const r=t[o],n=r.innerBlocks.findIndex((e=>"wp-block-woocommerce-checkout-payment-block"===e.name));return-1!==n&&(t[o]=r.innerBlocks.slice(0,n).concat((0,i.createBlock)("woocommerce/checkout-additional-information-block",{},[])).concat(t.slice(n+1,t.length)),[e,t])}}]};(0,i.registerBlockType)(Cc,Sc)},9407:()=>{},2080:()=>{},5684:()=>{},5415:()=>{},3091:()=>{},4957:()=>{},777:()=>{},9507:()=>{},4982:()=>{},401:()=>{},8337:()=>{},7051:()=>{},2867:()=>{},3048:()=>{},265:()=>{},7919:()=>{},780:()=>{},2931:()=>{},8375:()=>{},9345:()=>{},6625:()=>{},4567:()=>{},5452:()=>{},1221:()=>{},8824:()=>{},9569:()=>{},1637:()=>{},181:()=>{},1094:()=>{},7259:()=>{},6664:()=>{},5089:()=>{},9865:()=>{},6619:()=>{},377:()=>{},7797:()=>{},5681:()=>{},3425:()=>{},7694:()=>{},2255:()=>{},8024:()=>{},1299:()=>{},8845:()=>{},5609:()=>{},7765:()=>{},6201:()=>{},4688:()=>{},9705:()=>{},1598:()=>{},1212:()=>{},7414:()=>{},7840:()=>{},7791:()=>{},1609:e=>{"use strict";e.exports=window.React},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},4040:e=>{"use strict";e.exports=window.wp.deprecated},8107:e=>{"use strict";e.exports=window.wp.dom},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},8558:e=>{"use strict";e.exports=window.wp.keycodes},5573:e=>{"use strict";e.exports=window.wp.primitives}},n={};function s(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e].call(o.exports,o,o.exports,s),o.exports}s.m=r,e=[],s.O=(t,o,r,n)=>{if(!o){var c=1/0;for(m=0;m<e.length;m++){for(var[o,r,n]=e[m],a=!0,i=0;i<o.length;i++)(!1&n||c>=n)&&Object.keys(s.O).every((e=>s.O[e](o[i])))?o.splice(i--,1):(a=!1,n<c&&(c=n));if(a){e.splice(m--,1);var l=r();void 0!==l&&(t=l)}}return t}n=n||0;for(var m=e.length;m>0&&e[m-1][2]>n;m--)e[m]=e[m-1];e[m]=[o,r,n]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);s.r(n);var c={};t=t||[null,o({}),o([]),o(o)];for(var a=2&r&&e;"object"==typeof a&&!~t.indexOf(a);a=o(a))Object.getOwnPropertyNames(a).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,s.d(n,c),n},s.d=(e,t)=>{for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.j=251,(()=>{var e={251:0};s.O.j=t=>0===e[t];var t=(t,o)=>{var r,n,[c,a,i]=o,l=0;if(c.some((t=>0!==e[t]))){for(r in a)s.o(a,r)&&(s.m[r]=a[r]);if(i)var m=i(s)}for(t&&t(o);l<c.length;l++)n=c[l],s.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return s.O(m)},o=self.webpackChunkwebpackWcBlocksMainJsonp=self.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var c=s.O(void 0,[94],(()=>s(3986)));c=s.O(c),((this.wc=this.wc||{}).blocks=this.wc.blocks||{}).checkout=c})();
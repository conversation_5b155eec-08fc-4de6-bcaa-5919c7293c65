(self.webpackChunkwebpackWcBlocksFrontendJsonp=self.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[105],{1812:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>g});var n=l(1616),o=l(1609),r=l(851),s=l(2796),a=l(8537);l(6625);const c=({className:e="",disabled:t=!1,name:l,permalink:n="",target:s,rel:c,style:i,onClick:d,...u})=>{const m=(0,r.A)("wc-block-components-product-name",e);if(t){const e=u;return(0,o.createElement)("span",{className:m,...e,dangerouslySetInnerHTML:{__html:(0,a.decodeEntities)(l)}})}return(0,o.createElement)("a",{className:m,href:n,target:s,...u,dangerouslySetInnerHTML:{__html:(0,a.decodeEntities)(l)},style:i})};var i=l(7103),d=l(3249);l(7128);const u=({children:e,headingLevel:t,elementType:l=`h${t}`,...n})=>(0,o.createElement)(l,{...n},e),m=(0,n.withProductDataContext)((e=>{const{className:t,headingLevel:l=2,showProductLink:n=!0,linkTarget:a,align:m}=e,p=(0,d.p)(e),{parentClassName:y}=(0,s.useInnerBlockLayoutContext)(),{product:g}=(0,s.useProductDataContext)(),{dispatchStoreEvent:v}=(0,i.y)();return g.id?(0,o.createElement)(u,{headingLevel:l,className:(0,r.A)(t,p.className,"wc-block-components-product-title",{[`${y}__product-title`]:y,[`wc-block-components-product-title--align-${m}`]:m}),style:p.style},(0,o.createElement)(c,{disabled:!n,name:g.name,permalink:g.permalink,target:a,onClick:()=>{v("product-view-link",{product:g})}})):(0,o.createElement)(u,{headingLevel:l,className:(0,r.A)(t,p.className,"wc-block-components-product-title",{[`${y}__product-title`]:y,[`wc-block-components-product-title--align-${m}`]:m}),style:p.style})}));let p={headingLevel:{type:"number",default:2},showProductLink:{type:"boolean",default:!0},linkTarget:{type:"string"},productId:{type:"number",default:0}};p={...p,align:{type:"string"}};const y=p,g=(0,n.withFilteredAttributes)(y)(m)},3249:(e,t,l)=>{"use strict";l.d(t,{p:()=>i});var n=l(851),o=l(3993),r=l(1194),s=l(9786);function a(e={}){const t={};return(0,s.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function c(e,t){return e&&t?`has-${(0,r.c)(t)}-${e}`:""}const i=e=>{const t=(e=>{const t=(0,o.isObject)(e)?e:{style:{}};let l=t.style;return(0,o.isString)(l)&&(l=JSON.parse(l)||{}),(0,o.isObject)(l)||(l={}),{...t,style:l}})(e),l=function(e){var t,l,r,s,i,d,u;const{backgroundColor:m,textColor:p,gradient:y,style:g}=e,v=c("background-color",m),f=c("color",p),k=function(e){if(e)return`has-${e}-gradient-background`}(y),h=k||(null==g||null===(t=g.color)||void 0===t?void 0:t.gradient);return{className:(0,n.A)(f,k,{[v]:!h&&!!v,"has-text-color":p||(null==g||null===(l=g.color)||void 0===l?void 0:l.text),"has-background":m||(null==g||null===(r=g.color)||void 0===r?void 0:r.background)||y||(null==g||null===(s=g.color)||void 0===s?void 0:s.gradient),"has-link-color":(0,o.isObject)(null==g||null===(i=g.elements)||void 0===i?void 0:i.link)?null==g||null===(d=g.elements)||void 0===d||null===(u=d.link)||void 0===u?void 0:u.color:void 0}),style:a({color:(null==g?void 0:g.color)||{}})}}(t),r=function(e){var t;const l=(null===(t=e.style)||void 0===t?void 0:t.border)||{};return{className:function(e){var t;const{borderColor:l,style:o}=e,r=l?c("border-color",l):"";return(0,n.A)({"has-border-color":!!l||!(null==o||null===(t=o.border)||void 0===t||!t.color),[r]:!!r})}(e),style:a({border:l})}}(t),s=function(e){var t;return{className:void 0,style:a({spacing:(null===(t=e.style)||void 0===t?void 0:t.spacing)||{}})}}(t),i=(e=>{const t=(0,o.isObject)(e.style.typography)?e.style.typography:{},l=(0,o.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:l,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,n.A)(i.className,l.className,r.className,s.className),style:{...i.style,...l.style,...r.style,...s.style}}}},7128:()=>{},6625:()=>{}}]);
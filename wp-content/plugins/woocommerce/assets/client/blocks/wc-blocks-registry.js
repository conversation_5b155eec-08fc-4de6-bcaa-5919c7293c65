(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{__experimentalDeRegisterExpressPaymentMethod:()=>S,__experimentalDeRegisterPaymentMethod:()=>I,__experimentalRegisterProductCollection:()=>Q,getExpressPaymentMethods:()=>E,getPaymentMethods:()=>C,getRegisteredBlockComponents:()=>T,getRegisteredInnerBlocks:()=>x,registerBlockComponent:()=>O,registerExpressPaymentMethod:()=>k,registerInnerBlock:()=>R,registerPaymentMethod:()=>g,registerPaymentMethodExtensionCallbacks:()=>P});const o=window.wp.deprecated;var r=e.n(o);const n=window.wp.data;function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t,o){return(t=function(e){var t=function(e,t){if("object"!==i(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var r=o.call(e,"string");if("object"!==i(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===i(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const s=window.React,l={},c=l,u=(e,t,o)=>{const r=((e,t)=>o=>((null==o?void 0:o.paymentRequirements)||[]).every((e=>t.includes(e)))&&e(o))(e,t);return Object.values(c).some((e=>o in e))?((e,t,o)=>r=>{let n=e(r);if(n){const e={};Object.entries(t).forEach((([t,r])=>{o in r&&"function"==typeof r[o]&&(e[t]=r[o])})),n=Object.keys(e).every((t=>{try{return e[t](r)}catch(e){return console.error(`Error when executing callback for ${o} in ${t}`,e),!0}}))}return n})(r,c,o):r},d=window.wp.element,m=(e,t)=>{if(null!==e&&!(0,d.isValidElement)(e))throw new TypeError(`The ${t} property for the payment method must be a React element or null.`)},p=(e,t=[])=>{const o=t.reduce(((t,o)=>(e.hasOwnProperty(o)||t.push(o),t)),[]);if(o.length>0)throw new TypeError("The payment method configuration object is missing the following properties:"+o.join(", "))},y=()=>null;class v{constructor(e){var t,o,r,n;a(this,"name",void 0),a(this,"content",void 0),a(this,"edit",void 0),a(this,"paymentMethodId",void 0),a(this,"supports",void 0),a(this,"icons",void 0),a(this,"label",void 0),a(this,"ariaLabel",void 0),a(this,"placeOrderButtonLabel",void 0),a(this,"savedTokenComponent",void 0),a(this,"canMakePaymentFromConfig",void 0),v.assertValidConfig(e),this.name=e.name,this.label=e.label,this.placeOrderButtonLabel=e.placeOrderButtonLabel,this.ariaLabel=e.ariaLabel,this.content=e.content,this.savedTokenComponent=e.savedTokenComponent,this.icons=e.icons||null,this.edit=e.edit,this.paymentMethodId=e.paymentMethodId||this.name,this.supports={showSavedCards:(null==e||null===(t=e.supports)||void 0===t?void 0:t.showSavedCards)||(null==e||null===(o=e.supports)||void 0===o?void 0:o.savePaymentInfo)||!1,showSaveOption:(null==e||null===(r=e.supports)||void 0===r?void 0:r.showSaveOption)||!1,features:(null==e||null===(n=e.supports)||void 0===n?void 0:n.features)||["products"]},this.canMakePaymentFromConfig=e.canMakePayment}get canMakePayment(){return u(this.canMakePaymentFromConfig,this.supports.features,this.name)}}a(v,"assertValidConfig",(e=>{var t,o,n,i,a,l,c;if(e.savedTokenComponent=e.savedTokenComponent||(0,s.createElement)(y,null),p(e,["name","label","ariaLabel","content","edit","canMakePayment"]),"string"!=typeof e.name)throw new Error("The name property for the payment method must be a string");if(void 0!==e.icons&&!Array.isArray(e.icons)&&null!==e.icons)throw new Error("The icons property for the payment method must be an array or null.");if("string"!=typeof e.paymentMethodId&&void 0!==e.paymentMethodId)throw new Error("The paymentMethodId property for the payment method must be a string or undefined (in which case it will be the value of the name property).");if("string"!=typeof e.placeOrderButtonLabel&&void 0!==e.placeOrderButtonLabel)throw new TypeError("The placeOrderButtonLabel property for the payment method must be a string");if(((e,t)=>{if(null!==e&&!(0,d.isValidElement)(e)&&"string"!=typeof e)throw new TypeError("The label property for the payment method must be a React element, a string, or null.")})(e.label),m(e.content,"content"),m(e.edit,"edit"),m(e.savedTokenComponent,"savedTokenComponent"),"string"!=typeof e.ariaLabel)throw new TypeError("The ariaLabel property for the payment method must be a string");if("function"!=typeof e.canMakePayment)throw new TypeError("The canMakePayment property for the payment method must be a function.");if(void 0!==(null===(t=e.supports)||void 0===t?void 0:t.showSavedCards)&&"boolean"!=typeof(null===(o=e.supports)||void 0===o?void 0:o.showSavedCards))throw new TypeError("If the payment method includes the `supports.showSavedCards` property, it must be a boolean");if(void 0!==(null===(n=e.supports)||void 0===n?void 0:n.savePaymentInfo)&&r()("Passing savePaymentInfo when registering a payment method.",{alternative:"Pass showSavedCards and showSaveOption",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3686"}),void 0!==(null===(i=e.supports)||void 0===i?void 0:i.features)&&!Array.isArray(null===(a=e.supports)||void 0===a?void 0:a.features))throw new Error("The features property for the payment method must be an array or undefined.");if(void 0!==(null===(l=e.supports)||void 0===l?void 0:l.showSaveOption)&&"boolean"!=typeof(null===(c=e.supports)||void 0===c?void 0:c.showSaveOption))throw new TypeError("If the payment method includes the `supports.showSaveOption` property, it must be a boolean")}));class b{constructor(e){var t,o;a(this,"name",void 0),a(this,"title",void 0),a(this,"description",void 0),a(this,"gatewayId",void 0),a(this,"content",void 0),a(this,"edit",void 0),a(this,"paymentMethodId",void 0),a(this,"supports",void 0),a(this,"canMakePaymentFromConfig",void 0);const r="string"==typeof e.name?e.name.replace(/[_-]/g," "):e.name,n="string"==typeof(null==e?void 0:e.description)&&e.description.length>130?e.description.slice(0,130)+"...":e.description;b.assertValidConfig(e),this.name=e.name,this.title=e.title||r,this.description=n||"",this.gatewayId=e.gatewayId||"",this.content=e.content,this.edit=e.edit,this.paymentMethodId=e.paymentMethodId||this.name,this.supports={features:(null==e||null===(t=e.supports)||void 0===t?void 0:t.features)||["products"],style:(null==e||null===(o=e.supports)||void 0===o?void 0:o.style)||[]},this.canMakePaymentFromConfig=e.canMakePayment}get canMakePayment(){return u(this.canMakePaymentFromConfig,this.supports.features,this.name)}}a(b,"assertValidConfig",(e=>{var t,o;if(p(e,["name","content","edit"]),"string"!=typeof e.name)throw new TypeError("The name property for the express payment method must be a string");if("string"!=typeof e.paymentMethodId&&void 0!==e.paymentMethodId)throw new Error("The paymentMethodId property for the payment method must be a string or undefined (in which case it will be the value of the name property).");if(void 0!==(null===(t=e.supports)||void 0===t?void 0:t.features)&&!Array.isArray(null===(o=e.supports)||void 0===o?void 0:o.features))throw new Error("The features property for the payment method must be an array or undefined.");if(m(e.content,"content"),m(e.edit,"edit"),"function"!=typeof e.canMakePayment)throw new TypeError("The canMakePayment property for the express payment method must be a function.")}));const h="wc/store/payment",f={},w={},g=e=>{let t;"function"==typeof e?(t=e(v),r()("Passing a callback to registerPaymentMethod()",{alternative:"a config options object",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3404"})):t=new v(e),t instanceof v&&(f[t.name]=t)},k=e=>{let t;"function"==typeof e?(t=e(b),r()("Passing a callback to registerExpressPaymentMethod()",{alternative:"a config options object",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/3404"})):t=new b(e),t instanceof b&&(w[t.name]=t)},P=(e,t)=>{l[e]?console.error(`The namespace provided to registerPaymentMethodExtensionCallbacks must be unique. Callbacks have already been registered for the ${e} namespace.`):(l[e]={},Object.entries(t).forEach((([t,o])=>{"function"==typeof o?l[e][t]=o:console.error(`All callbacks provided to registerPaymentMethodExtensionCallbacks must be functions. The callback for the ${t} payment method in the ${e} namespace was not a function.`)})))},I=e=>{delete f[e];const{__internalRemoveAvailablePaymentMethod:t}=(0,n.dispatch)(h);t(e)},S=e=>{delete w[e];const{__internalRemoveAvailableExpressPaymentMethod:t}=(0,n.dispatch)(h);t(e)},C=()=>f,E=()=>w,A={};function T(e){return{..."object"==typeof A[e]&&Object.keys(A[e]).length>0?A[e]:{},...A.any}}function x(e){return r()("getRegisteredInnerBlocks",{version:"2.8.0",alternative:"getRegisteredBlockComponents",plugin:"WooCommerce Blocks"}),T(e)}const q=(e,t,o)=>{const r=typeof e[t];if(r!==o)throw new Error(`Incorrect value for the ${t} argument when registering a block component. It was a ${r}, but must be a ${o}.`)},M=(e,t)=>{if(e[t]){if("function"==typeof e[t])return;if(e[t].$$typeof&&e[t].$$typeof===Symbol.for("react.lazy"))return}throw new Error(`Incorrect value for the ${t} argument when registering a block component. Component must be a valid React Element or Lazy callback.`)};function O(e){e.context||(e.context="any"),q(e,"context","string"),q(e,"blockName","string"),M(e,"component");const{context:t,blockName:o,component:r}=e;A[t]||(A[t]={}),A[t][o]=r}function R(e){r()("registerInnerBlock",{version:"2.8.0",alternative:"registerBlockComponent",plugin:"WooCommerce Blocks",hint:'"main" has been replaced with "context" and is now optional.'}),q(e,"main","string"),O({...e,context:e.main})}const j=window.wp.hooks;let B=function(e){return e.GRID="flex",e.STACK="list",e}({}),L=function(e){return e.ATTRIBUTES="attributes",e.CREATED="created",e.FEATURED="featured",e.HAND_PICKED="hand-picked",e.INHERIT="inherit",e.KEYWORD="keyword",e.ON_SALE="on-sale",e.ORDER="order",e.STOCK_STATUS="stock-status",e.TAXONOMY="taxonomy",e.PRICE_RANGE="price-range",e.FILTERABLE="filterable",e}({});const _=window.wc.wcSettings,F=JSON.parse('{"UU":"woocommerce/product-collection"}');let $=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});const N=F.UU,H=`${N}/product-title`,D=(0,_.getSetting)("stockStatusOptions",[]),U={perPage:9,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",search:"",exclude:[],inherit:!1,taxQuery:{},isProductCollectionBlock:!0,featured:!1,woocommerceOnSale:!1,woocommerceStockStatus:(0,_.getSetting)("hideOutOfStockItems",!1)?Object.keys(function(e,t){const{[t]:o,...r}=e;return r}(D,"outofstock")):Object.keys(D),woocommerceAttributes:[],woocommerceHandPickedProducts:[],timeFrame:void 0,priceRange:void 0,filterable:!1},V={query:U,tagName:"div",displayLayout:{type:B.GRID,columns:3,shrinkColumns:!0},queryContextIncludes:["collection"],forcePageReload:!1},z=[["woocommerce/product-template",{},[["woocommerce/product-image",{imageSizing:$.THUMBNAIL}],["core/post-title",{textAlign:"center",level:3,fontSize:"medium",style:{spacing:{margin:{bottom:"0.75rem",top:"0"}}},isLink:!0,__woocommerceNamespace:H}],["woocommerce/product-price",{textAlign:"center",fontSize:"small"}],["woocommerce/product-button",{textAlign:"center",fontSize:"small"}]]],["core/query-pagination",{layout:{type:"flex",justifyContent:"center"}}],["woocommerce/product-collection-no-results"]],Q=e=>{var t,o,r,n,i,a;if(!(e=>{var t,o,r,n,i,a,s,l,c,u,d,m,p,y,v,b,h,f,w,g,k,P,I,S,C,E,A,T,x,q,M,O;return"object"!=typeof e||null===e?(console.error("Invalid arguments: You must pass an object to __experimentalRegisterProductCollection."),!1):"string"!=typeof e.name||0===e.name.length?(console.error("Invalid name: name must be a non-empty string."),!1):(e.name.match(/^[a-zA-Z0-9-]+\/product-collection\/[a-zA-Z0-9-]+$/)||console.warn('To prevent conflicts with other collections, please use a unique name following the pattern: "<plugin-name>/product-collection/<collection-name>". Ensure "<plugin-name>" is your plugin name and "<collection-name>" is your collection name. Both should consist only of alphanumeric characters and hyphens (e.g., "my-plugin/product-collection/my-collection").'),"string"!=typeof e.title||0===e.title.length?(console.error("Invalid title: title must be a non-empty string."),!1):(void 0!==e.description&&"string"!=typeof e.description&&console.warn("Invalid description: description must be a string."),void 0!==e.category&&"string"!=typeof e.category&&console.warn("Invalid category: category must be a string."),void 0===e.keywords||Array.isArray(e.keywords)||console.warn("Invalid keywords: keywords must be an array of strings."),void 0!==e.icon&&"string"!=typeof e.icon&&"object"!=typeof e.icon&&console.warn("Invalid icon: icon must be a string or an object."),void 0!==e.example&&"object"!=typeof e.example&&console.warn("Invalid example: example must be an object."),void 0===e.scope||Array.isArray(e.scope)||console.warn("Invalid scope: scope must be an array of type WPBlockVariationScope."),void 0!==e.attributes&&"object"!=typeof e.attributes&&console.warn("Invalid attributes: attributes must be an object."),void 0!==(null===(t=e.attributes)||void 0===t?void 0:t.query)&&"object"!=typeof e.attributes.query&&console.warn("Invalid query: query must be an object."),void 0!==(null===(o=e.attributes)||void 0===o||null===(r=o.query)||void 0===r?void 0:r.offset)&&"number"!=typeof e.attributes.query.offset&&console.warn("Invalid offset: offset must be a number."),void 0!==(null===(n=e.attributes)||void 0===n||null===(i=n.query)||void 0===i?void 0:i.order)&&"string"!=typeof e.attributes.query.order&&console.warn("Invalid order: order must be a string."),void 0!==(null===(a=e.attributes)||void 0===a||null===(s=a.query)||void 0===s?void 0:s.orderBy)&&"string"!=typeof e.attributes.query.orderBy&&console.warn("Invalid orderBy: orderBy must be a string."),void 0!==(null===(l=e.attributes)||void 0===l||null===(c=l.query)||void 0===c?void 0:c.pages)&&"number"!=typeof e.attributes.query.pages&&console.warn("Invalid pages: pages must be a number."),void 0!==(null===(u=e.attributes)||void 0===u||null===(d=u.query)||void 0===d?void 0:d.perPage)&&"number"!=typeof e.attributes.query.perPage&&console.warn("Invalid perPage: perPage must be a number."),void 0!==(null===(m=e.attributes)||void 0===m||null===(p=m.query)||void 0===p?void 0:p.search)&&"string"!=typeof e.attributes.query.search&&console.warn("Invalid search: search must be a string."),void 0!==(null===(y=e.attributes)||void 0===y||null===(v=y.query)||void 0===v?void 0:v.taxQuery)&&"object"!=typeof e.attributes.query.taxQuery&&console.warn("Invalid taxQuery: taxQuery must be an object."),void 0!==(null===(b=e.attributes)||void 0===b||null===(h=b.query)||void 0===h?void 0:h.featured)&&"boolean"!=typeof e.attributes.query.featured&&console.warn("Invalid featured: featured must be a boolean."),void 0!==(null===(f=e.attributes)||void 0===f||null===(w=f.query)||void 0===w?void 0:w.timeFrame)&&"object"!=typeof e.attributes.query.timeFrame&&console.warn("Invalid timeFrame: timeFrame must be an object."),void 0!==(null===(g=e.attributes)||void 0===g||null===(k=g.query)||void 0===k?void 0:k.woocommerceOnSale)&&"boolean"!=typeof e.attributes.query.woocommerceOnSale&&console.warn("Invalid woocommerceOnSale: woocommerceOnSale must be a boolean."),void 0===(null===(P=e.attributes)||void 0===P||null===(I=P.query)||void 0===I?void 0:I.woocommerceStockStatus)||Array.isArray(e.attributes.query.woocommerceStockStatus)||console.warn("Invalid woocommerceStockStatus: woocommerceStockStatus must be an array."),void 0===(null===(S=e.attributes)||void 0===S||null===(C=S.query)||void 0===C?void 0:C.woocommerceAttributes)||Array.isArray(e.attributes.query.woocommerceAttributes)||console.warn("Invalid woocommerceAttributes: woocommerceAttributes must be an array."),void 0===(null===(E=e.attributes)||void 0===E||null===(A=E.query)||void 0===A?void 0:A.woocommerceHandPickedProducts)||Array.isArray(e.attributes.query.woocommerceHandPickedProducts)||console.warn("Invalid woocommerceHandPickedProducts: woocommerceHandPickedProducts must be an array."),void 0!==(null===(T=e.attributes)||void 0===T||null===(x=T.query)||void 0===x?void 0:x.priceRange)&&"object"!=typeof e.attributes.query.priceRange&&console.warn("Invalid priceRange: priceRange must be an object."),void 0!==(null===(q=e.attributes)||void 0===q?void 0:q.displayLayout)&&"object"!=typeof e.attributes.displayLayout&&console.warn("Invalid displayLayout: displayLayout must be an object."),void 0===(null===(M=e.attributes)||void 0===M?void 0:M.hideControls)||Array.isArray(e.attributes.hideControls)||console.warn("Invalid hideControls: hideControls must be an array of strings."),void 0===(null===(O=e.attributes)||void 0===O?void 0:O.queryContextIncludes)||Array.isArray(e.attributes.queryContextIncludes)||console.warn("Invalid queryContextIncludes: queryContextIncludes must be an array of strings."),void 0!==e.preview&&("object"==typeof e.preview&&null!==e.preview||console.warn("Invalid preview: preview must be an object."),void 0!==e.preview.setPreviewState&&"function"!=typeof e.preview.setPreviewState&&console.warn("Invalid preview: setPreviewState must be a function."),void 0!==e.preview.initialPreviewState&&("object"!=typeof e.preview.initialPreviewState&&console.warn("Invalid preview: initialPreviewState must be an object."),"boolean"!=typeof e.preview.initialPreviewState.isPreview&&console.warn("Invalid preview: preview.isPreview must be a boolean."),"string"!=typeof e.preview.initialPreviewState.previewMessage&&console.warn("Invalid preview: preview.previewMessage must be a string."))),!(void 0!==e.usesReference&&!Array.isArray(e.usesReference)&&(console.error("Invalid usesReference: usesReference must be an array of strings."),1))))})(e))return void console.error("Collection could not be registered due to invalid configuration.");const{preview:{setPreviewState:l,initialPreviewState:c}={},usesReference:u}=e,d=(null===(t=e.attributes)||void 0===t?void 0:t.query)||{},m=[...new Set([L.INHERIT,...(null===(o=e.attributes)||void 0===o?void 0:o.hideControls)||[]])],p={name:e.name,title:e.title,description:e.description,category:e.category,keywords:e.keywords,icon:e.icon,example:e.example,scope:e.scope,attributes:{query:{...U,...void 0!==d.offset&&{offset:d.offset},...void 0!==d.order&&{order:d.order},...void 0!==d.orderBy&&{orderBy:d.orderBy},...void 0!==d.pages&&{pages:d.pages},...void 0!==d.perPage&&{perPage:d.perPage},...void 0!==d.search&&{search:d.search},...void 0!==d.taxQuery&&{taxQuery:d.taxQuery},...void 0!==d.featured&&{featured:d.featured},...void 0!==d.timeFrame&&{timeFrame:d.timeFrame},...void 0!==d.woocommerceOnSale&&{woocommerceOnSale:d.woocommerceOnSale},...void 0!==d.woocommerceStockStatus&&{woocommerceStockStatus:d.woocommerceStockStatus},...void 0!==d.woocommerceAttributes&&{woocommerceAttributes:d.woocommerceAttributes},...void 0!==d.woocommerceHandPickedProducts&&{woocommerceHandPickedProducts:d.woocommerceHandPickedProducts},...void 0!==d.priceRange&&{priceRange:d.priceRange}},displayLayout:null===(r=e.attributes)||void 0===r?void 0:r.displayLayout,hideControls:m,queryContextIncludes:null===(n=e.attributes)||void 0===n?void 0:n.queryContextIncludes,collection:e.name,inherit:!1},innerBlocks:e.innerBlocks||z,isActive:(e,t)=>e.collection===t.collection,isDefault:!1};if(l||c||Array.isArray(u)&&u.length>0){const e=e=>t=>t.attributes.collection!==p.name?(0,s.createElement)(e,{...t}):(0,s.createElement)(e,{...t,...c||l?{preview:{setPreviewState:l,initialPreviewState:c}}:{},usesReference:u});(0,j.addFilter)("editor.BlockEdit",p.name,e)}var y,v;null!==(i=wp)&&void 0!==i&&null!==(a=i.blocks)&&void 0!==a&&a.registerBlockVariation&&wp.blocks.registerBlockVariation(N,{...p,attributes:{...V,...p.attributes,query:{...U,...null===(y=p.attributes)||void 0===y?void 0:y.query},displayLayout:{...V.displayLayout,...null===(v=p.attributes)||void 0===v?void 0:v.displayLayout}}})};(this.wc=this.wc||{}).wcBlocksRegistry=t})();
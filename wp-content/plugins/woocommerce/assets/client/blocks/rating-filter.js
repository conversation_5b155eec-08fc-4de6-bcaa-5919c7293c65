(()=>{var e,t,o,r={1998:(e,t,o)=>{"use strict";o.r(t);var r=o(1609);const n=window.wp.blocks;var l=o(7104),a=o(846),s=o(851);const c=window.wp.blockEditor;var i=o(7723);const u=window.wp.components;var d=o(195),m=o(224);o(8887);const p=({className:e,rating:t,ratedProductsCount:o})=>{const n=(0,s.A)("wc-block-components-product-rating",e),l={width:t/5*100+"%"},a=(0,i.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,i.__)("Rated %f out of 5","woocommerce"),t),c={__html:(0,i.sprintf)(/* translators: %s is the rating value wrapped in HTML strong tags. */ /* translators: %s is the rating value wrapped in HTML strong tags. */
(0,i.__)("Rated %s out of 5","woocommerce"),(0,i.sprintf)('<strong class="rating">%f</strong>',t))};return(0,r.createElement)("div",{className:n},(0,r.createElement)("div",{className:"wc-block-components-product-rating__stars",role:"img","aria-label":a},(0,r.createElement)("span",{style:l,dangerouslySetInnerHTML:c})),null!==o?(0,r.createElement)("span",{className:"wc-block-components-product-rating-count"},"(",o,")"):null)};var w=o(6087),g=o(923),f=o.n(g);function b(e){const t=(0,w.useRef)(e);return f()(e,t.current)||(t.current=e),t.current}const _=window.wc.wcBlocksData,y=window.wp.data,h=(0,w.createContext)("page"),v=()=>(0,w.useContext)(h),E=(h.Provider,e=>{const t=v();e=e||t;const o=(0,y.useSelect)((t=>t(_.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:r}=(0,y.useDispatch)(_.QUERY_STATE_STORE_KEY);return[o,(0,w.useCallback)((t=>{r(e,t)}),[e,r])]}),k=(e,t,o)=>{const r=v();o=o||r;const n=(0,y.useSelect)((r=>r(_.QUERY_STATE_STORE_KEY).getValueForQueryKey(o,e,t)),[o,e]),{setQueryValue:l}=(0,y.useDispatch)(_.QUERY_STATE_STORE_KEY);return[n,(0,w.useCallback)((t=>{l(o,e,t)}),[o,e,l])]};var C=o(4717);const S=window.wc.wcTypes;var x=o(5574);const T=({queryAttribute:e,queryPrices:t,queryStock:o,queryRating:r,queryState:n,isEditor:l=!1})=>{let a=v();a=`${a}-collection-data`;const[s]=E(a),[c,i]=k("calculate_attribute_counts",[],a),[u,d]=k("calculate_price_range",null,a),[m,p]=k("calculate_stock_status_counts",null,a),[g,f]=k("calculate_rating_counts",null,a),h=b(e||{}),T=b(t),N=b(o),O=b(r);(0,w.useEffect)((()=>{"object"==typeof h&&Object.keys(h).length&&(c.find((e=>(0,S.objectHasProp)(h,"taxonomy")&&e.taxonomy===h.taxonomy))||i([...c,h]))}),[h,c,i]),(0,w.useEffect)((()=>{u!==T&&void 0!==T&&d(T)}),[T,d,u]),(0,w.useEffect)((()=>{m!==N&&void 0!==N&&p(N)}),[N,p,m]),(0,w.useEffect)((()=>{g!==O&&void 0!==O&&f(O)}),[O,f,g]);const[R,P]=(0,w.useState)(l),[A]=(0,C.d7)(R,200);R||P(!0);const j=(0,w.useMemo)((()=>(e=>{const t=e;return Array.isArray(e.calculate_attribute_counts)&&(t.calculate_attribute_counts=(0,x.di)(e.calculate_attribute_counts.map((({taxonomy:e,queryType:t})=>({taxonomy:e,query_type:t})))).asc(["taxonomy","query_type"])),t})(s)),[s]);return(e=>{const{namespace:t,resourceName:o,resourceValues:r=[],query:n={},shouldSelect:l=!0}=e;if(!t||!o)throw new Error("The options object must have valid values for the namespace and the resource properties.");const a=(0,w.useRef)({results:[],isLoading:!0}),s=b(n),c=b(r),i=(()=>{const[,e]=(0,w.useState)();return(0,w.useCallback)((t=>{e((()=>{throw t}))}),[])})(),u=(0,y.useSelect)((e=>{if(!l)return null;const r=e(_.COLLECTIONS_STORE_KEY),n=[t,o,s,c],a=r.getCollectionError(...n);if(a){if(!(0,S.isError)(a))throw new Error("TypeError: `error` object is not an instance of Error constructor");i(a)}return{results:r.getCollection(...n),isLoading:!r.hasFinishedResolution("getCollection",n)}}),[t,o,c,s,l,i]);return null!==u&&(a.current=u),a.current})({namespace:"/wc/store/v1",resourceName:"products/collection-data",query:{...n,page:void 0,per_page:void 0,orderby:void 0,order:void 0,...j},shouldSelect:A})},N=window.wc.wcSettings,O=window.wc.blocksComponents;o(1504);const R=({className:e,isLoading:t,disabled:o,
/* translators: Submit button text for filters. */
label:n=(0,i.__)("Apply","woocommerce"),onClick:l,screenReaderLabel:a=(0,i.__)("Apply filter","woocommerce")})=>(0,r.createElement)("button",{type:"submit",className:(0,s.A)("wp-block-button__link","wc-block-filter-submit-button","wc-block-components-filter-submit-button",{"is-loading":t},e),disabled:o,onClick:l},(0,r.createElement)(O.Label,{label:n,screenReaderLabel:a}));o(8335);const P=({className:e,
/* translators: Reset button text for filters. */
label:t=(0,i.__)("Reset","woocommerce"),onClick:o,screenReaderLabel:n=(0,i.__)("Reset filter","woocommerce")})=>(0,r.createElement)("button",{className:(0,s.A)("wc-block-components-filter-reset-button",e),onClick:o},(0,r.createElement)(O.Label,{label:t,screenReaderLabel:n}));var A=o(8001);o(243);const j=({className:e,style:t,suggestions:o,multiple:n=!0,saveTransform:l=(e=>e.trim().replace(/\s/g,"-")),messages:a={},validateInput:c=(e=>o.includes(e)),label:i="",...u})=>(0,r.createElement)("div",{className:(0,s.A)("wc-blocks-components-form-token-field-wrapper",e,{"single-selection":!n}),style:t},(0,r.createElement)(A.A,{label:i,__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,__experimentalValidateInput:c,saveTransform:l,maxLength:n?void 0:1,suggestions:o,messages:a,...u})),L=window.wp.url,B=(0,N.getSettingWithCoercion)("isRenderingPhpTemplate",!1,S.isBoolean);function F(e){if(B){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,o)=>{o.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(o)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const q=e=>{const t=(0,L.getQueryArgs)(e);return(0,L.addQueryArgs)(e,t)},M=[{label:(0,r.createElement)(p,{key:5,rating:5,ratedProductsCount:null}),value:"5"},{label:(0,r.createElement)(p,{key:4,rating:4,ratedProductsCount:null}),value:"4"},{label:(0,r.createElement)(p,{key:3,rating:3,ratedProductsCount:null}),value:"3"},{label:(0,r.createElement)(p,{key:2,rating:2,ratedProductsCount:null}),value:"2"},{label:(0,r.createElement)(p,{key:1,rating:1,ratedProductsCount:null}),value:"1"}];o(8692);function Q(){return Math.floor(Math.random()*Date.now())}const D=e=>e.trim().replace(/\s/g,"-").replace(/_/g,"-").replace(/-+/g,"-").replace(/[^a-zA-Z0-9-]/g,""),Y=(0,w.createContext)({}),I="rating_filter",V=e=>(0,i.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,i.__)("Rated %s out of 5 filter added.","woocommerce"),e),G=e=>(0,i.sprintf)(/* translators: %s is referring to the average rating value */ /* translators: %s is referring to the average rating value */
(0,i.__)("Rated %s out of 5 filter added.","woocommerce"),e),K=({attributes:e,isEditor:t,noRatingsNotice:o=null})=>{const n=(()=>{const{wrapper:e}=(0,w.useContext)(Y);return t=>{e&&e.current&&(e.current.hidden=!t)}})(),a=(0,N.getSettingWithCoercion)("isRenderingPhpTemplate",!1,S.isBoolean),[c,u]=(0,w.useState)(!1),[g]=E(),{results:_,isLoading:y}=T({queryRating:!0,queryState:g,isEditor:t}),[h,v]=(0,w.useState)(e.isPreview?M:[]),C=!e.isPreview&&y&&0===h.length,x=!e.isPreview&&y,A=(0,w.useMemo)((()=>((e="filter_rating")=>{const t=(o=e,window?(0,L.getQueryArg)(window.location.href,o):null);var o;return t?(0,S.isString)(t)?t.split(","):t:[]})("rating_filter")),[]),[B,K]=(0,w.useState)(A),[W,U]=k("rating",A),[H,$]=(0,w.useState)(Q()),[J,z]=(0,w.useState)(!1),Z="single"!==e.selectType,X=Z?!C&&B.length<h.length:!C&&0===B.length,ee=(0,w.useCallback)((e=>{t||(e&&!a&&U(e),(e=>{if(!window)return;if(0===e.length){const e=(0,L.removeQueryArgs)(window.location.href,I);return void(e!==q(window.location.href)&&F(e))}const t=(0,L.addQueryArgs)(window.location.href,{[I]:e.join(",")});t!==q(window.location.href)&&F(t)})(e))}),[t,U,a]);(0,w.useEffect)((()=>{e.showFilterButton||ee(B)}),[e.showFilterButton,B,ee]);const te=b((0,w.useMemo)((()=>W),[W])),oe=function(e,t){const o=(0,w.useRef)();return(0,w.useEffect)((()=>{o.current===e||(o.current=e)}),[e,t]),o.current}(te);(0,w.useEffect)((()=>{f()(oe,te)||f()(B,te)||K(te)}),[B,te,oe]),(0,w.useEffect)((()=>{c||(U(A),u(!0))}),[U,c,u,A]),(0,w.useEffect)((()=>{if(y||e.isPreview)return;const o=!y&&(0,S.objectHasProp)(_,"rating_counts")&&Array.isArray(_.rating_counts)?[..._.rating_counts].reverse():[];if(t&&0===o.length)return v(M),void z(!0);const n=o.filter((e=>(0,S.isObject)(e)&&Object.keys(e).length>0)).map((t=>{var o;return{label:(0,r.createElement)(p,{key:null==t?void 0:t.rating,rating:null==t?void 0:t.rating,ratedProductsCount:e.showCounts?null==t?void 0:t.count:null}),value:null==t||null===(o=t.rating)||void 0===o?void 0:o.toString()}}));v(n),$(Q())}),[e.showCounts,e.isPreview,_,y,W,t]);const re=(0,w.useCallback)((e=>{const t=B.includes(e);if(!Z){const o=t?[]:[e];return(0,d.speak)(t?G(e):V(e)),void K(o)}if(t){const t=B.filter((t=>t!==e));return(0,d.speak)(G(e)),void K(t)}const o=[...B,e].sort(((e,t)=>Number(t)-Number(e)));(0,d.speak)(V(e)),K(o)}),[B,Z]);return(y||0!==h.length)&&(0,N.getSettingWithCoercion)("hasFilterableProducts",!1,S.isBoolean)?(n(!0),(0,r.createElement)(r.Fragment,null,J&&o,(0,r.createElement)("div",{className:(0,s.A)("wc-block-rating-filter",`style-${e.displayStyle}`,{"is-loading":C})},"dropdown"===e.displayStyle?(0,r.createElement)(r.Fragment,null,(0,r.createElement)(j,{key:H,className:(0,s.A)({"single-selection":!Z,"is-loading":C}),style:{borderStyle:"none"},suggestions:h.filter((e=>!B.includes(e.value))).map((e=>e.value)),disabled:C,placeholder:(0,i.__)("Select Rating","woocommerce"),onChange:e=>{!Z&&e.length>1&&(e=[e[e.length-1]]);const t=[e=e.map((e=>{const t=h.find((t=>t.value===e));return t?t.value:e})),B].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));if(1===t.length)return re(t[0]);const o=[B,e].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));1===o.length&&re(o[0])},value:B,displayTransform:e=>{const t={value:e,label:(0,r.createElement)(p,{key:Number(e),rating:Number(e),ratedProductsCount:0})},o=h.find((t=>t.value===e))||t,{label:n,value:l}=o;return Object.assign({},n,{toLocaleLowerCase:()=>l,substring:(e,t)=>0===e&&1===t?n:""})},saveTransform:D,messages:{added:(0,i.__)("Rating filter added.","woocommerce"),removed:(0,i.__)("Rating filter removed.","woocommerce"),remove:(0,i.__)("Remove rating filter.","woocommerce"),__experimentalInvalid:(0,i.__)("Invalid rating filter.","woocommerce")}}),X&&(0,r.createElement)(l.A,{icon:m.A,size:30})):(0,r.createElement)(O.CheckboxList,{className:"wc-block-rating-filter-list",options:h,checked:B,onChange:e=>{re(e.toString())},isLoading:C,isDisabled:x})),(0,r.createElement)("div",{className:"wc-block-rating-filter__actions"},(B.length>0||t)&&!C&&(0,r.createElement)(P,{onClick:()=>{K([]),U([]),ee([])},screenReaderLabel:(0,i.__)("Reset rating filter","woocommerce")}),e.showFilterButton&&(0,r.createElement)(R,{className:"wc-block-rating-filter__button",isLoading:C,disabled:C||x,onClick:()=>ee(B)})))):(n(!1),null)};o(3538);const W=(0,r.createElement)(u.Notice,{status:"warning",isDismissible:!1},(0,r.createElement)("p",null,(0,i.__)("Your store doesn't have any products with ratings yet. This filter option will display when a product receives a review.","woocommerce"))),U=(0,u.withSpokenMessages)((({attributes:e,setAttributes:t})=>{const{className:o,displayStyle:n,showCounts:l,showFilterButton:a,selectType:d}=e,m=(0,c.useBlockProps)({className:(0,s.A)("wc-block-rating-filter",o)});return(0,r.createElement)(r.Fragment,null,(0,r.createElement)(c.InspectorControls,{key:"inspector"},(0,r.createElement)(u.PanelBody,{title:(0,i.__)("Display Settings","woocommerce")},(0,r.createElement)(u.ToggleControl,{label:(0,i.__)("Display product count","woocommerce"),checked:l,onChange:()=>t({showCounts:!l})}),(0,r.createElement)(u.__experimentalToggleGroupControl,{label:(0,i.__)("Allow selecting multiple options?","woocommerce"),value:d||"multiple",onChange:e=>t({selectType:e}),className:"wc-block-attribute-filter__multiple-toggle"},(0,r.createElement)(u.__experimentalToggleGroupControlOption,{value:"multiple",label:(0,i._x)("Multiple","Number of filters","woocommerce")}),(0,r.createElement)(u.__experimentalToggleGroupControlOption,{value:"single",label:(0,i._x)("Single","Number of filters","woocommerce")})),(0,r.createElement)(u.__experimentalToggleGroupControl,{label:(0,i.__)("Display Style","woocommerce"),value:n,onChange:e=>t({displayStyle:e}),className:"wc-block-attribute-filter__display-toggle"},(0,r.createElement)(u.__experimentalToggleGroupControlOption,{value:"list",label:(0,i.__)("List","woocommerce")}),(0,r.createElement)(u.__experimentalToggleGroupControlOption,{value:"dropdown",label:(0,i.__)("Dropdown","woocommerce")})),(0,r.createElement)(u.ToggleControl,{label:(0,i.__)("Show 'Apply filters' button","woocommerce"),help:(0,i.__)("Products will update when the button is clicked.","woocommerce"),checked:a,onChange:e=>t({showFilterButton:e})}))),(0,r.createElement)("div",{...m},(0,r.createElement)(u.Disabled,null,(0,r.createElement)(K,{attributes:e,isEditor:!0,noRatingsNotice:W}))))})),H=JSON.parse('{"name":"woocommerce/rating-filter","version":"1.0.0","title":"Filter by Rating Controls","description":"Enable customers to filter the product grid by rating.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"html":false,"multiple":false,"color":true,"inserter":false,"lock":false},"attributes":{"className":{"type":"string","default":""},"showCounts":{"type":"boolean","default":false},"displayStyle":{"type":"string","default":"list"},"showFilterButton":{"type":"boolean","default":false},"selectType":{"type":"string","default":"multiple"},"isPreview":{"type":"boolean","default":false}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),$=[{attributes:{...H.attributes,showCounts:{type:"boolean",default:!0}},save:({attributes:e})=>{const{className:t,showCounts:o}=e,n={"data-show-counts":o};return(0,r.createElement)("div",{...c.useBlockProps.save({className:(0,s.A)("is-loading",t)}),...n},(0,r.createElement)("span",{"aria-hidden":!0,className:"wc-block-product-rating-filter__placeholder"}))}}];(0,n.registerBlockType)(H,{icon:{src:(0,r.createElement)(l.A,{icon:a.A,className:"wc-block-editor-components-block-icon"})},attributes:{...H.attributes},edit:U,save({attributes:e}){const{className:t}=e;return(0,r.createElement)("div",{...c.useBlockProps.save({className:(0,s.A)("is-loading",t)})})},deprecated:$})},8335:()=>{},1504:()=>{},243:()=>{},8887:()=>{},3538:()=>{},8692:()=>{},1609:e=>{"use strict";e.exports=window.React},8468:e=>{"use strict";e.exports=window.lodash},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},4040:e=>{"use strict";e.exports=window.wp.deprecated},8107:e=>{"use strict";e.exports=window.wp.dom},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},8558:e=>{"use strict";e.exports=window.wp.keycodes},5573:e=>{"use strict";e.exports=window.wp.primitives},979:e=>{"use strict";e.exports=window.wp.warning}},n={};function l(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e].call(o.exports,o,o.exports,l),o.exports}l.m=r,e=[],l.O=(t,o,r,n)=>{if(!o){var a=1/0;for(u=0;u<e.length;u++){for(var[o,r,n]=e[u],s=!0,c=0;c<o.length;c++)(!1&n||a>=n)&&Object.keys(l.O).every((e=>l.O[e](o[c])))?o.splice(c--,1):(s=!1,n<a&&(a=n));if(s){e.splice(u--,1);var i=r();void 0!==i&&(t=i)}}return t}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[o,r,n]},l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,l.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);l.r(n);var a={};t=t||[null,o({}),o([]),o(o)];for(var s=2&r&&e;"object"==typeof s&&!~t.indexOf(s);s=o(s))Object.getOwnPropertyNames(s).forEach((t=>a[t]=()=>e[t]));return a.default=()=>e,l.d(n,a),n},l.d=(e,t)=>{for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),l.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},l.j=8915,(()=>{var e={8915:0};l.O.j=t=>0===e[t];var t=(t,o)=>{var r,n,[a,s,c]=o,i=0;if(a.some((t=>0!==e[t]))){for(r in s)l.o(s,r)&&(l.m[r]=s[r]);if(c)var u=c(l)}for(t&&t(o);i<a.length;i++)n=a[i],l.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return l.O(u)},o=self.webpackChunkwebpackWcBlocksMainJsonp=self.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var a=l.O(void 0,[94],(()=>l(1998)));a=l.O(a),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["rating-filter"]=a})();
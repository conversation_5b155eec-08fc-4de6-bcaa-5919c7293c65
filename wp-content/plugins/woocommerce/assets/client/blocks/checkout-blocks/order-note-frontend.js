"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[552],{6094:(e,o,t)=>{t.r(o),t.d(o,{default:()=>h});var r=t(1609),s=t(851),c=t(7723),a=t(4656),n=t(4958),l=t(7143),d=t(7594),u=t(6087);const i=({disabled:e,onChange:o,placeholder:t,value:s})=>{const[n,l]=(0,u.useState)(!1),[d,i]=(0,u.useState)("");return(0,r.createElement)("div",{className:"wc-block-checkout__add-note"},(0,r.createElement)(a.CheckboxControl,{disabled:e,label:(0,c.__)("Add a note to your order","woocommerce"),checked:n,onChange:e=>{l(e),e?s!==d&&o(d):(o(""),i(s))}}),n&&(0,r.createElement)(a.Textarea,{disabled:e,onTextChange:o,placeholder:t,value:s}))},h=({className:e})=>{const{needsShipping:o}=(0,n.m)(),{isProcessing:t,orderNotes:u}=(0,l.useSelect)((e=>{const o=e(d.CHECKOUT_STORE_KEY);return{isProcessing:o.isProcessing(),orderNotes:o.getOrderNotes()}})),{__internalSetOrderNotes:h}=(0,l.useDispatch)(d.CHECKOUT_STORE_KEY);return(0,r.createElement)(a.FormStep,{id:"order-notes",showStepNumber:!1,className:(0,s.A)("wc-block-checkout__order-notes",e),disabled:t},(0,r.createElement)(i,{disabled:t,onChange:h,placeholder:o?(0,c.__)("Notes about your order, e.g. special notes for delivery.","woocommerce"):(0,c.__)("Notes about your order.","woocommerce"),value:u}))}}}]);
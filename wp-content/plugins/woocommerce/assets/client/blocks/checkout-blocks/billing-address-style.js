"use strict";(self.webpackChunkwebpackWcBlocksStylingJsonp=self.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[4037],{57539:(e,i,l)=>{l.r(i),l.d(i,{default:()=>m});var s=l(51609),t=l(70851),r=l(41616),n=l(14656),o=l(25251),d=l(47143),c=l(47594),p=l(54108),a=l(17364),h=l(99292),u=l(79672);const m=(0,r.withFilteredAttributes)(a.A)((({title:e,description:i,children:l,className:r})=>{const{showFormStepNumbers:a}=(0,h.Oy)(),m=(0,d.useSelect)((e=>e(c.CHECKOUT_STORE_KEY).isProcessing())),{showCompanyField:F,requireCompanyField:w,showApartmentField:b,requireApartmentField:k,showPhoneField:A,requirePhoneField:g}=(0,h.Oy)(),{showBillingFields:y,forcedBillingAddress:C,useBillingAsShipping:S}=(0,o.C)();return y||S?(e=(0,u.y)(e,C),i=(0,u.q)(i,C),(0,s.createElement)(n.FormStep,{id:"billing-fields",disabled:m,className:(0,t.A)("wc-block-checkout__billing-fields",r),title:e,description:i,showStepNumber:a},(0,s.createElement)(p.A,{showCompanyField:F,requireCompanyField:w,showApartmentField:b,requireApartmentField:k,showPhoneField:A,requirePhoneField:g}),l)):null}))}}]);
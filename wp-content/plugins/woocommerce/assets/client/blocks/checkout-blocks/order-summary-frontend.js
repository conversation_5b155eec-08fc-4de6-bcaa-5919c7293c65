"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[12],{3370:(e,t,c)=>{c.r(t),c.d(t,{default:()=>u});var r=c(1609),n=c(8639),a=c(910),s=c(8509),o=c(1e3);const l=()=>{const{extensions:e,receiveCart:t,...c}=(0,s.V)(),n={extensions:e,cart:c,context:"woocommerce/checkout"};return(0,r.createElement)(o.ExperimentalOrderMeta.Slot,{...n})},u=({children:e,className:t=""})=>{const{cartTotals:c}=(0,s.V)(),o=(0,a.getCurrencyFromPriceResponse)(c);return(0,r.createElement)("div",{className:t},e,(0,r.createElement)("div",{className:"wc-block-components-totals-wrapper"},(0,r.createElement)(n.Ay,{currency:o,values:c})),(0,r.createElement)(l,null))}}}]);
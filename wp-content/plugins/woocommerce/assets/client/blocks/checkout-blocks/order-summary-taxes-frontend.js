"use strict";(self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=self.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4e3],{4507:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var r=a(1616),s=a(1609),c=a(4656),n=a(910),o=a(8509),l=a(5703);const u={showRateAfterTaxName:{type:"boolean",default:(0,l.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!0}}},p=(0,r.withFilteredAttributes)(u)((({className:e,showRateAfterTaxName:t})=>{const{cartTotals:a}=(0,o.V)();if((0,l.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(a.total_tax,10)<=0)return null;const r=(0,n.getCurrencyFromPriceResponse)(a);return(0,s.createElement)(c.TotalsWrapper,{className:e},(0,s.createElement)(c.TotalsTaxes,{showRateAfterTaxName:t,currency:r,values:a}))}))}}]);
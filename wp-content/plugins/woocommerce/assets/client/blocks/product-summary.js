(self.webpackChunkwebpackWcBlocksMainJsonp=self.webpackChunkwebpackWcBlocksMainJsonp||[]).push([[3895],{3326:(t,e,o)=>{"use strict";o.r(e),o.d(e,{default:()=>f});var n=o(1609),s=o(851),r=o(6087),l=o(6004);const c=t=>t.replace(/<\/?[a-z][^>]*?>/gi,""),a=(t,e)=>t.replace(/[\s|\.\,]+$/i,"")+e;var i=o(9446);const u=({source:t,maxLength:e=15,countType:o="words",className:s="",style:u={}})=>{const d=(0,r.useMemo)((()=>((t,e=15,o="words")=>{const n=(0,l.autop)(t);if((0,i.count)(n,o)<=e)return n;const s=(t=>{const e=t.indexOf("</p>");return-1===e?t:t.substr(0,e+4)})(n);return(0,i.count)(s,o)<=e?s:"words"===o?((t,e,o="&hellip;",n=!0)=>{const s=c(t),r=s.split(" ").splice(0,e).join(" ");return r===s?n?(0,l.autop)(s):s:n?(0,l.autop)(a(r,o)):a(r,o)})(s,e):((t,e,o=!0,n="&hellip;",s=!0)=>{const r=c(t),i=r.slice(0,e);if(i===r)return s?(0,l.autop)(r):r;if(o)return(0,l.autop)(a(i,n));const u=i.match(/([\s]+)/g),d=u?u.length:0,p=r.slice(0,e+d);return s?(0,l.autop)(a(p,n)):a(p,n)})(s,e,"characters_including_spaces"===o)})(t,e,o)),[t,e,o]);return(0,n.createElement)(r.RawHTML,{style:u,className:s},d)};var d=o(812),p=o(2796),m=o(3566),y=o(1616);o(7766);const f=(0,y.withProductDataContext)((t=>{const{className:e}=t,{parentClassName:o}=(0,p.useInnerBlockLayoutContext)(),{product:r}=(0,p.useProductDataContext)(),l=(0,m.p)(t);if(!r)return(0,n.createElement)("div",{className:(0,s.A)(e,"wc-block-components-product-summary",{[`${o}__product-summary`]:o})});const c=r.short_description?r.short_description:r.description;return c?(0,n.createElement)(u,{className:(0,s.A)(e,l.className,"wc-block-components-product-summary",{[`${o}__product-summary`]:o}),source:c,maxLength:150,countType:d.r7.wordCountType||"words",style:l.style}):null}))},3566:(t,e,o)=>{"use strict";o.d(e,{p:()=>c});var n=o(851),s=o(3993),r=o(92),l=o(6032);const c=t=>{const e=(t=>{const e=(0,s.isObject)(t)?t:{style:{}};let o=e.style;return(0,s.isString)(o)&&(o=JSON.parse(o)||{}),(0,s.isObject)(o)||(o={}),{...e,style:o}})(t),o=(0,l.BK)(e),c=(0,l.aR)(e),a=(0,l.fo)(e),i=(0,r.x)(e);return{className:(0,n.A)(i.className,o.className,c.className,a.className),style:{...i.style,...o.style,...c.style,...a.style}}}},92:(t,e,o)=>{"use strict";o.d(e,{x:()=>s});var n=o(3993);const s=t=>{const e=(0,n.isObject)(t.style.typography)?t.style.typography:{},o=(0,n.isString)(e.fontFamily)?e.fontFamily:"";return{className:t.fontFamily?`has-${t.fontFamily}-font-family`:o,style:{fontSize:t.fontSize?`var(--wp--preset--font-size--${t.fontSize})`:e.fontSize,fontStyle:e.fontStyle,fontWeight:e.fontWeight,letterSpacing:e.letterSpacing,lineHeight:e.lineHeight,textDecoration:e.textDecoration,textTransform:e.textTransform}}}},6032:(t,e,o)=>{"use strict";o.d(e,{BK:()=>i,aR:()=>u,fo:()=>d});var n=o(851),s=o(1194),r=o(9786),l=o(3993);function c(t={}){const e={};return(0,r.getCSSRules)(t,{selector:""}).forEach((t=>{e[t.key]=t.value})),e}function a(t,e){return t&&e?`has-${(0,s.c)(e)}-${t}`:""}function i(t){var e,o,s,r,i,u,d;const{backgroundColor:p,textColor:m,gradient:y,style:f}=t,v=a("background-color",p),g=a("color",m),h=function(t){if(t)return`has-${t}-gradient-background`}(y),b=h||(null==f||null===(e=f.color)||void 0===e?void 0:e.gradient);return{className:(0,n.A)(g,h,{[v]:!b&&!!v,"has-text-color":m||(null==f||null===(o=f.color)||void 0===o?void 0:o.text),"has-background":p||(null==f||null===(s=f.color)||void 0===s?void 0:s.background)||y||(null==f||null===(r=f.color)||void 0===r?void 0:r.gradient),"has-link-color":(0,l.isObject)(null==f||null===(i=f.elements)||void 0===i?void 0:i.link)?null==f||null===(u=f.elements)||void 0===u||null===(d=u.link)||void 0===d?void 0:d.color:void 0}),style:c({color:(null==f?void 0:f.color)||{}})}}function u(t){var e;const o=(null===(e=t.style)||void 0===e?void 0:e.border)||{};return{className:function(t){var e;const{borderColor:o,style:s}=t,r=o?a("border-color",o):"";return(0,n.A)({"has-border-color":!!o||!(null==s||null===(e=s.border)||void 0===e||!e.color),[r]:!!r})}(t),style:c({border:o})}}function d(t){var e;return{className:void 0,style:c({spacing:(null===(e=t.style)||void 0===e?void 0:e.spacing)||{}})}}},7766:()=>{}}]);
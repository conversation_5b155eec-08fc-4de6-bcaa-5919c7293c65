{"name": "woocommerce/single-product", "version": "1.0.0", "icon": "info", "title": "Single Product", "description": "Display a single product.", "category": "woocommerce", "keywords": ["WooCommerce"], "supports": {"align": ["wide", "full"]}, "attributes": {"isPreview": {"type": "boolean", "default": false}, "productId": {"type": "number"}}, "example": {"attributes": {"isPreview": true}}, "usesContext": ["postId", "postType", "queryId"], "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}
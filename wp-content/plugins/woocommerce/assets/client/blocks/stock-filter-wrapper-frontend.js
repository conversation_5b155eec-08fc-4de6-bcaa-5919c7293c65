(self.webpackChunkwebpackWcBlocksFrontendJsonp=self.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[660],{5237:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var o=s(1609),n=s(7723),r=s(4656);s(5400);const l=({name:e,count:t})=>(0,o.createElement)(o.Fragment,null,e,null!==t&&Number.isFinite(t)&&(0,o.createElement)(r.Label,{label:t.toString(),screenReaderLabel:(0,n.sprintf)(/* translators: %s number of products. */ /* translators: %s number of products. */
(0,n._n)("%s product","%s products",t,"woocommerce"),t),wrapperElement:"span",wrapperProps:{className:"wc-filter-element-label-list-count"}}))},6179:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var o=s(1609);s(1626);const n=({children:e})=>(0,o.createElement)("div",{className:"wc-block-filter-title-placeholder"},e)},4054:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var o=s(1609),n=s(7723),r=s(851),l=s(4656);s(8335);const c=({className:e,
/* translators: Reset button text for filters. */
label:t=(0,n.__)("Reset","woocommerce"),onClick:s,screenReaderLabel:c=(0,n.__)("Reset filter","woocommerce")})=>(0,o.createElement)("button",{className:(0,r.A)("wc-block-components-filter-reset-button",e),onClick:s},(0,o.createElement)(l.Label,{label:t,screenReaderLabel:c}))},1745:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var o=s(1609),n=s(7723),r=s(851),l=s(4656);s(1504);const c=({className:e,isLoading:t,disabled:s,
/* translators: Submit button text for filters. */
label:c=(0,n.__)("Apply","woocommerce"),onClick:a,screenReaderLabel:i=(0,n.__)("Apply filter","woocommerce")})=>(0,o.createElement)("button",{type:"submit",className:(0,r.A)("wp-block-button__link","wc-block-filter-submit-button","wc-block-components-filter-submit-button",{"is-loading":t},e),disabled:s,onClick:a},(0,o.createElement)(l.Label,{label:c,screenReaderLabel:i}))},80:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var o=s(1609),n=s(8001),r=s(851);s(243);const l=({className:e,style:t,suggestions:s,multiple:l=!0,saveTransform:c=(e=>e.trim().replace(/\s/g,"-")),messages:a={},validateInput:i=(e=>s.includes(e)),label:u="",...d})=>(0,o.createElement)("div",{className:(0,r.A)("wc-blocks-components-form-token-field-wrapper",e,{"single-selection":!l}),style:t},(0,o.createElement)(n.A,{label:u,__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,__experimentalValidateInput:i,saveTransform:c,maxLength:l?void 0:1,suggestions:s,messages:a,...d}))},1561:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var o=s(6087),n=s(4717),r=s(3993),l=s(5574),c=s(1573),a=s(9098),i=s(1380),u=s(6372);const d=({queryAttribute:e,queryPrices:t,queryStock:s,queryRating:d,queryState:m,isEditor:f=!1})=>{let g=(0,u._)();g=`${g}-collection-data`;const[p]=(0,a.dJ)(g),[v,b]=(0,a.xd)("calculate_attribute_counts",[],g),[y,h]=(0,a.xd)("calculate_price_range",null,g),[_,k]=(0,a.xd)("calculate_stock_status_counts",null,g),[w,E]=(0,a.xd)("calculate_rating_counts",null,g),S=(0,c.c)(e||{}),A=(0,c.c)(t),C=(0,c.c)(s),x=(0,c.c)(d);(0,o.useEffect)((()=>{"object"==typeof S&&Object.keys(S).length&&(v.find((e=>(0,r.objectHasProp)(S,"taxonomy")&&e.taxonomy===S.taxonomy))||b([...v,S]))}),[S,v,b]),(0,o.useEffect)((()=>{y!==A&&void 0!==A&&h(A)}),[A,h,y]),(0,o.useEffect)((()=>{_!==C&&void 0!==C&&k(C)}),[C,k,_]),(0,o.useEffect)((()=>{w!==x&&void 0!==x&&E(x)}),[x,E,w]);const[N,R]=(0,o.useState)(f),[T]=(0,n.d7)(N,200);N||R(!0);const L=(0,o.useMemo)((()=>(e=>{const t=e;return Array.isArray(e.calculate_attribute_counts)&&(t.calculate_attribute_counts=(0,l.di)(e.calculate_attribute_counts.map((({taxonomy:e,queryType:t})=>({taxonomy:e,query_type:t})))).asc(["taxonomy","query_type"])),t})(p)),[p]);return(0,i.G)({namespace:"/wc/store/v1",resourceName:"products/collection-data",query:{...m,page:void 0,per_page:void 0,orderby:void 0,order:void 0,...L},shouldSelect:T})}},1380:(e,t,s)=>{"use strict";s.d(t,{G:()=>i});var o=s(7594),n=s(7143),r=s(6087),l=s(1573),c=s(7615),a=s(3993);const i=e=>{const{namespace:t,resourceName:s,resourceValues:i=[],query:u={},shouldSelect:d=!0}=e;if(!t||!s)throw new Error("The options object must have valid values for the namespace and the resource properties.");const m=(0,r.useRef)({results:[],isLoading:!0}),f=(0,l.c)(u),g=(0,l.c)(i),p=(0,c.a)(),v=(0,n.useSelect)((e=>{if(!d)return null;const n=e(o.COLLECTIONS_STORE_KEY),r=[t,s,f,g],l=n.getCollectionError(...r);if(l){if(!(0,a.isError)(l))throw new Error("TypeError: `error` object is not an instance of Error constructor");p(l)}return{results:n.getCollection(...r),isLoading:!n.hasFinishedResolution("getCollection",r)}}),[t,s,g,f,d,p]);return null!==v&&(m.current=v),m.current}},9098:(e,t,s)=>{"use strict";s.d(t,{dJ:()=>c,xd:()=>a});var o=s(7594),n=s(7143),r=s(6087),l=(s(923),s(6372));const c=e=>{const t=(0,l._)();e=e||t;const s=(0,n.useSelect)((t=>t(o.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:c}=(0,n.useDispatch)(o.QUERY_STATE_STORE_KEY);return[s,(0,r.useCallback)((t=>{c(e,t)}),[e,c])]},a=(e,t,s)=>{const c=(0,l._)();s=s||c;const a=(0,n.useSelect)((n=>n(o.QUERY_STATE_STORE_KEY).getValueForQueryKey(s,e,t)),[s,e]),{setQueryValue:i}=(0,n.useDispatch)(o.QUERY_STATE_STORE_KEY);return[a,(0,r.useCallback)((t=>{i(s,e,t)}),[s,e,i])]}},6372:(e,t,s)=>{"use strict";s.d(t,{_:()=>r});var o=s(6087);const n=(0,o.createContext)("page"),r=()=>(0,o.useContext)(n);n.Provider},9095:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var o=s(6087);function n(e,t){const s=(0,o.useRef)();return(0,o.useEffect)((()=>{s.current===e||t&&!t(e,s.current)||(s.current=e)}),[e,t]),s.current}},1573:(e,t,s)=>{"use strict";s.d(t,{c:()=>l});var o=s(6087),n=s(923),r=s.n(n);function l(e){const t=(0,o.useRef)(e);return r()(e,t.current)||(t.current=e),t.current}},3249:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var o=s(851),n=s(3993),r=s(1194),l=s(9786);function c(e={}){const t={};return(0,l.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function a(e,t){return e&&t?`has-${(0,r.c)(t)}-${e}`:""}const i=e=>{const t=(e=>{const t=(0,n.isObject)(e)?e:{style:{}};let s=t.style;return(0,n.isString)(s)&&(s=JSON.parse(s)||{}),(0,n.isObject)(s)||(s={}),{...t,style:s}})(e),s=function(e){var t,s,r,l,i,u,d;const{backgroundColor:m,textColor:f,gradient:g,style:p}=e,v=a("background-color",m),b=a("color",f),y=function(e){if(e)return`has-${e}-gradient-background`}(g),h=y||(null==p||null===(t=p.color)||void 0===t?void 0:t.gradient);return{className:(0,o.A)(b,y,{[v]:!h&&!!v,"has-text-color":f||(null==p||null===(s=p.color)||void 0===s?void 0:s.text),"has-background":m||(null==p||null===(r=p.color)||void 0===r?void 0:r.background)||g||(null==p||null===(l=p.color)||void 0===l?void 0:l.gradient),"has-link-color":(0,n.isObject)(null==p||null===(i=p.elements)||void 0===i?void 0:i.link)?null==p||null===(u=p.elements)||void 0===u||null===(d=u.link)||void 0===d?void 0:d.color:void 0}),style:c({color:(null==p?void 0:p.color)||{}})}}(t),r=function(e){var t;const s=(null===(t=e.style)||void 0===t?void 0:t.border)||{};return{className:function(e){var t;const{borderColor:s,style:n}=e,r=s?a("border-color",s):"";return(0,o.A)({"has-border-color":!!s||!(null==n||null===(t=n.border)||void 0===t||!t.color),[r]:!!r})}(e),style:c({border:s})}}(t),l=function(e){var t;return{className:void 0,style:c({spacing:(null===(t=e.style)||void 0===t?void 0:t.spacing)||{}})}}(t),i=(e=>{const t=(0,n.isObject)(e.style.typography)?e.style.typography:{},s=(0,n.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:s,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,o.A)(i.className,s.className,r.className,l.className),style:{...i.style,...s.style,...r.style,...l.style}}}},7615:(e,t,s)=>{"use strict";s.d(t,{a:()=>n});var o=s(6087);const n=()=>{const[,e]=(0,o.useState)();return(0,o.useCallback)((t=>{e((()=>{throw t}))}),[])}},4735:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Q});var o=s(1609),n=s(851),r=s(3249),l=s(3993),c=s(7723),a=s(195),i=s(7104),u=s(224),d=s(1573),m=s(9095),f=s(9098),g=s(1561),p=s(5703),v=s(6087),b=s(4656),y=s(1745),h=s(4054),_=s(6179),k=s(5237),w=s(80),E=s(923),S=s.n(E),A=s(8537),C=s(3832),x=s(3366);const N=[{value:"preview-1",name:"In Stock",label:(0,o.createElement)(k.A,{name:"In Stock",count:3}),textLabel:"In Stock (3)"},{value:"preview-2",name:"Out of stock",label:(0,o.createElement)(k.A,{name:"Out of stock",count:3}),textLabel:"Out of stock (3)"},{value:"preview-3",name:"On backorder",label:(0,o.createElement)(k.A,{name:"On backorder",count:2}),textLabel:"On backorder (2)"}];s(5837);const R=JSON.parse('{"uK":{"F8":{"A":3},"Ox":{"A":"list"},"dc":{"A":"multiple"}}}');function T(){return Math.floor(Math.random()*Date.now())}const L=e=>e.trim().replace(/\s/g,"").replace(/_/g,"-").replace(/-+/g,"-").replace(/[^a-zA-Z0-9-]/g,"");var O=s(4300);const F=x.nD+"stock_status",P=({attributes:e,isEditor:t=!1})=>{const s=(0,O.$Q)(),r=(0,p.getSettingWithCoercion)("isRenderingPhpTemplate",!1,l.isBoolean),[E,R]=(0,v.useState)(!1),{outofstock:P,...Q}=(0,p.getSetting)("stockStatusOptions",{}),q=(0,v.useRef)((0,p.getSetting)("hideOutOfStockItems",!1)?Q:{outofstock:P,...Q}),j=(0,v.useMemo)((()=>((e,t="filter_stock_status")=>{const s=(0,x.Vf)(t);if(!s)return[];const o=(0,l.isString)(s)?s.split(","):s,n=Object.keys(e);return o.filter((e=>n.includes(e)))})(q.current,F)),[]),[$,B]=(0,v.useState)(j),[I,K]=(0,v.useState)(e.isPreview?N:[]),[Y]=(0,v.useState)(Object.entries(q.current).map((([e,t])=>({slug:e,name:t}))).filter((e=>!!e.name)).sort(((e,t)=>e.slug.localeCompare(t.slug)))),[D]=(0,f.dJ)(),[H,V]=(0,f.xd)("stock_status",j),{results:z,isLoading:J}=(0,g.A)({queryStock:!0,queryState:D,isEditor:t}),W=(0,v.useCallback)((e=>(0,l.objectHasProp)(z,"stock_status_counts")&&Array.isArray(z.stock_status_counts)?z.stock_status_counts.find((({status:t,count:s})=>t===e&&0!==Number(s))):null),[z]),[M,U]=(0,v.useState)(T());(0,v.useEffect)((()=>{if(J||e.isPreview)return;const t=Y.map((t=>{const s=W(t.slug);if(!(s||$.includes(t.slug)||(n=t.slug,null!=D&&D.stock_status&&D.stock_status.some((({status:e=[]})=>e.includes(n))))))return null;var n;const r=s?Number(s.count):0;return{value:t.slug,name:(0,A.decodeEntities)(t.name),label:(0,o.createElement)(k.A,{name:(0,A.decodeEntities)(t.name),count:e.showCounts?r:null}),textLabel:e.showCounts?`${(0,A.decodeEntities)(t.name)} (${r})`:(0,A.decodeEntities)(t.name)}})).filter((e=>!!e));K(t),U(T())}),[e.showCounts,e.isPreview,J,W,$,D.stock_status,Y]);const Z="single"!==e.selectType,G=(0,v.useCallback)((e=>{t||(e&&!r&&V(e),(e=>{if(!window)return;if(0===e.length){const e=(0,C.removeQueryArgs)(window.location.href,F);return void(e!==(0,x.Q)(window.location.href)&&(0,x.CH)(e))}const t=(0,C.addQueryArgs)(window.location.href,{[F]:e.join(",")});t!==(0,x.Q)(window.location.href)&&(0,x.CH)(t)})(e))}),[t,V,r]);(0,v.useEffect)((()=>{e.showFilterButton||G($)}),[e.showFilterButton,$,G]);const X=(0,v.useMemo)((()=>H),[H]),ee=(0,d.c)(X),te=(0,m.Z)(ee);(0,v.useEffect)((()=>{S()(te,ee)||S()($,ee)||B(ee)}),[$,ee,te]),(0,v.useEffect)((()=>{E||(V(j),R(!0))}),[V,E,R,j]);const se=(0,v.useCallback)((e=>{const t=e=>{const t=I.find((t=>t.value===e));return t?t.name:null},s=({filterAdded:e,filterRemoved:s})=>{const o=e?t(e):null,n=s?t(s):null;o?(0,a.speak)((0,c.sprintf)(/* translators: %s stock statuses (for example: 'instock'...) */ /* translators: %s stock statuses (for example: 'instock'...) */
(0,c.__)("%s filter added.","woocommerce"),o)):n&&(0,a.speak)((0,c.sprintf)(/* translators: %s stock statuses (for example:'instock'...) */ /* translators: %s stock statuses (for example:'instock'...) */
(0,c.__)("%s filter removed.","woocommerce"),n))},o=$.includes(e);if(!Z){const t=o?[]:[e];return s(o?{filterRemoved:e}:{filterAdded:e}),void B(t)}if(o){const t=$.filter((t=>t!==e));return s({filterRemoved:e}),void B(t)}const n=[...$,e].sort();s({filterAdded:e}),B(n)}),[$,Z,I]);if(!J&&0===I.length)return s(!1),null;const oe=`h${e.headingLevel}`,ne=!e.isPreview&&!q.current||0===I.length,re=!e.isPreview&&J;if(!(0,p.getSettingWithCoercion)("hasFilterableProducts",!1,l.isBoolean))return s(!1),null;const le=Z?!ne&&$.length<I.length:!ne&&0===$.length,ce=(0,o.createElement)(oe,{className:"wc-block-stock-filter__title"},e.heading),ae=ne?(0,o.createElement)(_.A,null,ce):ce;return s(!0),(0,o.createElement)(o.Fragment,null,!t&&e.heading&&ae,(0,o.createElement)("div",{className:(0,n.A)("wc-block-stock-filter",`style-${e.displayStyle}`,{"is-loading":ne})},"dropdown"===e.displayStyle?(0,o.createElement)(o.Fragment,null,(0,o.createElement)(w.A,{key:M,className:(0,n.A)({"single-selection":!Z,"is-loading":ne}),suggestions:I.filter((e=>!$.includes(e.value))).map((e=>e.value)),disabled:ne,placeholder:(0,c.__)("Select stock status","woocommerce"),onChange:e=>{!Z&&e.length>1&&(e=e.slice(-1));const t=[e=e.map((e=>{const t=I.find((t=>t.value===e));return t?t.value:e})),$].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));if(1===t.length)return se(t[0]);const s=[$,e].reduce(((e,t)=>e.filter((e=>!t.includes(e)))));1===s.length&&se(s[0])},value:$,displayTransform:e=>{const t=I.find((t=>t.value===e));return t?t.textLabel:e},saveTransform:L,messages:{added:(0,c.__)("Stock filter added.","woocommerce"),removed:(0,c.__)("Stock filter removed.","woocommerce"),remove:(0,c.__)("Remove stock filter.","woocommerce"),__experimentalInvalid:(0,c.__)("Invalid stock filter.","woocommerce")}}),le&&(0,o.createElement)(i.A,{icon:u.A,size:30})):(0,o.createElement)(b.CheckboxList,{className:"wc-block-stock-filter-list",options:I,checked:$,onChange:se,isLoading:ne,isDisabled:re})),(0,o.createElement)("div",{className:"wc-block-stock-filter__actions"},($.length>0||t)&&!ne&&(0,o.createElement)(h.A,{onClick:()=>{B([]),G([])},screenReaderLabel:(0,c.__)("Reset stock filter","woocommerce")}),e.showFilterButton&&(0,o.createElement)(y.A,{className:"wc-block-stock-filter__button",isLoading:ne,disabled:ne||re,onClick:()=>G($)})))},Q=e=>{const t=(0,r.p)(e),s=(c=e,{heading:(0,l.isString)(null==c?void 0:c.heading)?c.heading:"",headingLevel:(0,l.isString)(null==c?void 0:c.headingLevel)&&parseInt(c.headingLevel,10)||R.uK.F8.A,showFilterButton:"true"===(null==c?void 0:c.showFilterButton),showCounts:"true"===(null==c?void 0:c.showCounts),isPreview:!1,displayStyle:(0,l.isString)(null==c?void 0:c.displayStyle)&&c.displayStyle||R.uK.Ox.A,selectType:(0,l.isString)(null==c?void 0:c.selectType)&&c.selectType||R.uK.dc.A});var c;return(0,o.createElement)("div",{className:(0,n.A)((0,l.isString)(e.className)?e.className:"",t.className),style:t.style},(0,o.createElement)(P,{isEditor:!1,attributes:s}))}},3366:(e,t,s)=>{"use strict";s.d(t,{CH:()=>u,Q:()=>d,Vf:()=>i,nD:()=>a,xB:()=>c});var o=s(3832),n=s(5703),r=s(3993);const l=(0,n.getSettingWithCoercion)("isRenderingPhpTemplate",!1,r.isBoolean),c="query_type_",a="filter_";function i(e){return window?(0,o.getQueryArg)(window.location.href,e):null}function u(e){if(l){const t=new URL(e);t.pathname=t.pathname.replace(/\/page\/[0-9]+/i,""),t.searchParams.delete("paged"),t.searchParams.forEach(((e,s)=>{s.match(/^query(?:-[0-9]+)?-page$/)&&t.searchParams.delete(s)})),window.location.href=t.href}else window.history.replaceState({},"",e)}const d=e=>{const t=(0,o.getQueryArgs)(e);return(0,o.addQueryArgs)(e,t)}},5400:()=>{},1626:()=>{},8335:()=>{},1504:()=>{},243:()=>{},5837:()=>{}}]);
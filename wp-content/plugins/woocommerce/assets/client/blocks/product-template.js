(()=>{var e,t,o,r={3269:(e,t,o)=>{"use strict";o.r(t);const r=window.wp.blocks,n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-template","title":"Product Template","category":"woocommerce","description":"Contains the block elements used to render a product.","keywords":["WooCommerce"],"textdomain":"woocommerce","usesContext":["queryId","query","queryContext","displayLayout","templateSlug","postId","queryContextIncludes","collection","__privateProductCollectionPreviewState"],"supports":{"inserter":false,"reusable":false,"html":false,"align":["wide","full"],"anchor":true,"color":{"gradients":true,"link":true,"__experimentalDefaultControls":{"background":true,"text":true}},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}}}}');var c=o(1609),s=o(851),l=o(6087);const a=window.wp.data;var i=o(7723);const u=window.wp.blockEditor,d=window.wp.components,p=window.wp.coreData,m=window.wc.wcSettings,g=window.wc.wcTypes,y=window.wc.wcBlocksSharedContext;var w=o(2294),f=o(9491);window.wp.url;const h=window.wp.apiFetch;var x=o.n(h);const k=(0,f.createHigherOrderComponent)((e=>class extends l.Component{constructor(...e){super(...e),(0,w.A)(this,"state",{error:null,loading:!1,product:"preview"===this.props.attributes.productId?this.props.attributes.previewProduct:null}),(0,w.A)(this,"loadProduct",(()=>{const{productId:e}=this.props.attributes;"preview"!==e&&(e?(this.setState({loading:!0}),(e=>x()({path:`/wc/store/v1/products/${e}`}))(e).then((e=>{this.setState({product:e,loading:!1,error:null})})).catch((async e=>{const t=await(async e=>{if(!("json"in e))return{message:e.message,type:e.type||"general"};try{const t=await e.json();return{message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}})(e);this.setState({product:null,loading:!1,error:t})}))):this.setState({product:null,loading:!1,error:null}))}))}componentDidMount(){this.loadProduct()}componentDidUpdate(e){e.attributes.productId!==this.props.attributes.productId&&this.loadProduct()}render(){const{error:t,loading:o,product:r}=this.state;return(0,c.createElement)(e,{...this.props,error:t,getProduct:this.loadProduct,isLoading:o,product:r})}}),"withProduct");let v=function(e){return e.Product="product",e.Archive="archive",e.Cart="cart",e.Order="order",e.Site="site",e}({});const b="single-product",_="taxonomy-product_cat",C="taxonomy-product_tag",I=async(e,t,o,r)=>{var n,c;r((n=await(0,a.resolveSelect)(p.store).getEntityRecords(e,t,{_fields:["id"],slug:o}))&&n.length&&null!==(c=n[0])&&void 0!==c&&c.id?n[0].id:null)},S=(e,t={})=>({type:e,sourceData:t});o(1704);const P=JSON.parse('{"name":"woocommerce/product-collection"}');let O=function(e){return e.GRID="flex",e.STACK="list",e}({}),B=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});P.name;const E=(0,m.getSetting)("stockStatusOptions",[]),j=(0,m.getSetting)("hideOutOfStockItems",!1),T=()=>j?Object.keys(function(e,t){const{[t]:o,...r}=e;return r}(E,"outofstock")):Object.keys(E),A=(T(),O.GRID,B.THUMBNAIL,["collection"]),N=()=>{const e=(0,u.useInnerBlocksProps)({className:"wc-block-product"},{__unstableDisableLayoutClassNames:!0});return(0,c.createElement)("li",{...e})},M=(0,l.memo)((({blocks:e,blockContextId:t,isHidden:o,setActiveBlockContextId:r})=>{const n=(0,u.__experimentalUseBlockPreview)({blocks:e,props:{className:"wc-block-product"}}),s=()=>{r(t)},l={display:o?"none":void 0};return(0,c.createElement)("li",{...n,tabIndex:0,role:"button",onClick:s,onKeyPress:s,style:l})})),H=k((({isLoading:e,product:t,displayTemplate:o,blocks:r,blockContext:n,setActiveBlockContextId:s})=>(0,c.createElement)(u.BlockContextProvider,{key:n.postId,value:n},(0,c.createElement)(y.ProductDataContextProvider,{product:t,isLoading:e},o?(0,c.createElement)(N,null):null,(0,c.createElement)(M,{blocks:r,blockContextId:n.postId,setActiveBlockContextId:s,isHidden:o})))));o(1434),(0,r.registerBlockType)(n,{icon:()=>(0,c.createElement)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,c.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 4H18C19.1046 4 20 4.89543 20 6V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4ZM18 5.5H6C5.72386 5.5 5.5 5.72386 5.5 6V9H18.5V6C18.5 5.72386 18.2761 5.5 18 5.5ZM18.5 10.5H10L10 18.5H18C18.2761 18.5 18.5 18.2761 18.5 18V10.5ZM8.5 10.5H5.5V18C5.5 18.2761 5.72386 18.5 6 18.5H8.5L8.5 10.5Z",fill:"#1E1E1E"})),edit:e=>{const{clientId:t,context:{query:{perPage:o,offset:r=0,order:n,orderBy:y,search:w,exclude:f,inherit:h,taxQuery:x,pages:k,...P},queryContext:O=[{page:1}],templateSlug:B,displayLayout:{type:E,columns:j,shrinkColumns:N}={type:"flex",columns:3,shrinkColumns:!1},queryContextIncludes:M=[],__privateProductCollectionPreviewState:L},__unstableLayoutClassNames:D}=e,q=((e,t)=>{const o=e.templateSlug||"",r=e.postId||null,n=(e=>t=>e.replace(`${t}-`,""))(o),c=(e=>t=>e.includes(t)&&e!==t)(o),s=c(b),i=c(_),d=c(C),[p,m]=(0,l.useState)(null),[g,y]=(0,l.useState)(null),[w,f]=(0,l.useState)(null);(0,l.useEffect)((()=>{if(s){const e=n(b);I("postType","product",e,m)}if(i){const e=n(_);I("taxonomy","product_cat",e,y)}if(d){const e=n(C);I("taxonomy","product_tag",e,f)}}),[s,i,d,n]);const{isInSingleProductBlock:h,isInSomeCartCheckoutBlock:x}=(0,a.useSelect)((e=>{const{getBlockParentsByBlockName:o}=e(u.store),r=e=>o(t,e).length>0;return{isInSingleProductBlock:r(["woocommerce/single-product"]),isInSomeCartCheckoutBlock:r(["woocommerce/cart","woocommerce/checkout","woocommerce/mini-cart-contents"])}}),[t]);if(h)return S(v.Product,{productId:r});if(x)return S(v.Cart);if(s)return S(v.Product,{productId:p});const k=(e=>t=>e===t)(o);if(k(b))return S(v.Product,{productId:null});if(i)return S(v.Archive,{taxonomy:"product_cat",termId:g});if(d)return S(v.Archive,{taxonomy:"product_tag",termId:w});if(k(_))return S(v.Archive,{taxonomy:"product_cat",termId:null});if(k(C))return S(v.Archive,{taxonomy:"product_tag",termId:null});if(k("taxonomy-product_attribute"))return S(v.Archive,{taxonomy:null,termId:null});if("page-cart"===o||"page-checkout"===o)return S(v.Cart);const P=k("order-confirmation");return S(P?v.Order:v.Site)})(e.context,e.clientId),[{page:W}]=O,[R,V]=(0,l.useState)(),$="product",F=(0,m.getSettingWithCoercion)("loopShopPerPage",12,g.isNumber),J=[...new Set(M.concat(A))],U=(({clientId:e,queryContextIncludes:t})=>{const o=(0,a.useSelect)((t=>{const{getBlockParentsByBlockName:o,getBlockAttributes:r}=t("core/block-editor"),n=o(e,"woocommerce/product-collection",!0);return null!=n&&n.length?r(n[0]):null}),[e]);return(0,l.useMemo)((()=>{if(!o)return null;const e={};return null!=t&&t.length&&t.forEach((t=>{null!=o&&o[t]&&(e[t]=o[t])})),e}),[t,o])})({clientId:t,queryContextIncludes:J}),{products:Z,blocks:G}=(0,a.useSelect)((e=>{const{getEntityRecords:c,getTaxonomies:s}=e(p.store),{getBlocks:l}=e(u.store),a=s({type:$,per_page:-1,context:"view"}),i={postType:$,offset:o?o*(W-1)+r:0,order:n,orderby:y};if(x&&!h){const e=Object.entries(x).reduce(((e,[t,o])=>{const r=null==a?void 0:a.find((({slug:e})=>e===t));return null!=r&&r.rest_base&&(e[null==r?void 0:r.rest_base]=o),e}),{});Object.keys(e).length&&Object.assign(i,e)}if(o&&(i.per_page=o),w&&(i.search=w),null!=f&&f.length&&(i.exclude=f),h){const{taxonomy:e,slug:t}=((e="")=>{const t="category-",o="taxonomy-product_cat-",r="taxonomy-product_tag-";return e.startsWith(t)?{taxonomy:"category",slug:e.replace(t,"")}:e.startsWith(o)?{taxonomy:"product_cat",slug:e.replace(o,"")}:e.startsWith(r)?{taxonomy:"product_tag",slug:e.replace(r,"")}:{taxonomy:"",slug:""}})(B);if(e&&t){const o=c("taxonomy",e,{context:"view",per_page:1,_fields:["id"],slug:t});if(o){var d;const t=null===(d=o[0])||void 0===d?void 0:d.id;"category"===e?i.categories=t:i[e]=t}}i.per_page=F}return{products:c("postType",$,{...i,...P,productCollectionLocation:q,productCollectionQueryContext:U,previewState:L,...h&&{woocommerceStockStatus:T()}}),blocks:l(t)}}),[o,W,r,n,y,t,w,$,f,h,B,x,P,q,U,F,L]),z=(0,l.useMemo)((()=>null==Z?void 0:Z.map((e=>({postType:e.type,postId:e.id})))),[Z]);let K="";"flex"===E&&j>1&&(K=N?`wc-block-product-template__responsive columns-${j}`:`is-flex-container columns-${j}`);const Q=(0,u.useBlockProps)({className:(0,s.A)(D,"wc-block-product-template",K,{[`is-product-collection-layout-${E}`]:E})});return Z?Z.length?(0,c.createElement)("ul",{...Q},z&&z.map((e=>{var t;const o=e.postId===(R||(null===(t=z[0])||void 0===t?void 0:t.postId));return(0,c.createElement)(H,{key:e.postId,attributes:{productId:e.postId},blocks:G,displayTemplate:o,blockContext:e,setActiveBlockContextId:V})}))):(0,c.createElement)("p",{...Q}," ",(0,i.__)("No results found.","woocommerce")):(0,c.createElement)("p",{...Q},(0,c.createElement)(d.Spinner,{className:"wc-block-product-template__spinner"}))},save:function(){return(0,c.createElement)(u.InnerBlocks.Content,null)}})},1704:()=>{},1434:()=>{},1609:e=>{"use strict";e.exports=window.React},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n}},n={};function c(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e].call(o.exports,o,o.exports,c),o.exports}c.m=r,e=[],c.O=(t,o,r,n)=>{if(!o){var s=1/0;for(u=0;u<e.length;u++){for(var[o,r,n]=e[u],l=!0,a=0;a<o.length;a++)(!1&n||s>=n)&&Object.keys(c.O).every((e=>c.O[e](o[a])))?o.splice(a--,1):(l=!1,n<s&&(s=n));if(l){e.splice(u--,1);var i=r();void 0!==i&&(t=i)}}return t}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[o,r,n]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var n=Object.create(null);c.r(n);var s={};t=t||[null,o({}),o([]),o(o)];for(var l=2&r&&e;"object"==typeof l&&!~t.indexOf(l);l=o(l))Object.getOwnPropertyNames(l).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,c.d(n,s),n},c.d=(e,t)=>{for(var o in t)c.o(t,o)&&!c.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=3,(()=>{var e={3:0};c.O.j=t=>0===e[t];var t=(t,o)=>{var r,n,[s,l,a]=o,i=0;if(s.some((t=>0!==e[t]))){for(r in l)c.o(l,r)&&(c.m[r]=l[r]);if(a)var u=a(c)}for(t&&t(o);i<s.length;i++)n=s[i],c.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return c.O(u)},o=self.webpackChunkwebpackWcBlocksMainJsonp=self.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var s=c.O(void 0,[94],(()=>c(3269)));s=c.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-template"]=s})();
<?php return array('dependencies' => array('wc-blocks-frontend-vendors', 'lodash', 'react', 'wc-blocks-components', 'wc-blocks-data-store', 'wc-settings', 'wc-types', 'wp-a11y', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-polyfill', 'wp-primitives', 'wp-url', 'wp-warning'), 'version' => '74b204ac4d73a8df5b12');

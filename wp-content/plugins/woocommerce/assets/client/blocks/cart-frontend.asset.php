<?php return array('dependencies' => array('wc-cart-checkout-base', 'wc-cart-checkout-vendors', 'react', 'wc-blocks-checkout', 'wc-blocks-components', 'wc-blocks-data-store', 'wc-blocks-registry', 'wc-blocks-shared-context', 'wc-blocks-shared-hocs', 'wc-price-format', 'wc-settings', 'wc-types', 'wp-a11y', 'wp-api-fetch', 'wp-autop', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-plugins', 'wp-polyfill', 'wp-primitives', 'wp-style-engine', 'wp-url', 'wp-wordcount'), 'version' => 'c4176854aaa8bc3e3556');

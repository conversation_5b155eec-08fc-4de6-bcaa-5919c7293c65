/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={67639:(e,t,r)=>{"use strict";var o=r(9523),n=r(72248),s=n(o("String.prototype.indexOf"));e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&s(e,".prototype.")>-1?n(r):r}},72248:(e,t,r)=>{"use strict";var o=r(2073),n=r(9523),s=r(71138),i=n("%TypeError%"),a=n("%Function.prototype.apply%"),c=n("%Function.prototype.call%"),u=n("%Reflect.apply%",!0)||o.call(c,a),l=n("%Object.defineProperty%",!0),E=n("%Math.max%");if(l)try{l({},"a",{value:1})}catch(e){l=null}e.exports=function(e){if("function"!=typeof e)throw new i("a function is required");var t=u(o,c,arguments);return s(t,1+E(0,e.length-(arguments.length-1)),!0)};var d=function(){return u(o,a,arguments)};l?l(e.exports,"apply",{value:d}):e.exports.apply=d},29203:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};e.exports=t},54274:e=>{var t,r;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&r.rotl(e,8)|4278255360&r.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=r.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,o=0;r<e.length;r++,o+=8)t[o>>>5]|=e[r]<<24-o%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var r=[],o=0;o<e.length;o+=3)for(var n=e[o]<<16|e[o+1]<<8|e[o+2],s=0;s<4;s++)8*o+6*s<=8*e.length?r.push(t.charAt(n>>>6*(3-s)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var r=[],o=0,n=0;o<e.length;n=++o%4)0!=n&&r.push((t.indexOf(e.charAt(o-1))&Math.pow(2,-2*n+8)-1)<<2*n|t.indexOf(e.charAt(o))>>>6-2*n);return r}},e.exports=r},11131:(e,t,r)=>{"use strict";var o=r(97547)(),n=r(9523),s=o&&n("%Object.defineProperty%",!0);if(s)try{s({},"a",{value:1})}catch(e){s=!1}var i=n("%SyntaxError%"),a=n("%TypeError%"),c=r(98158);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new a("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new a("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new a("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new a("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new a("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new a("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,n=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,l=arguments.length>6&&arguments[6],E=!!c&&c(e,t);if(s)s(e,t,{configurable:null===u&&E?E.configurable:!u,enumerable:null===o&&E?E.enumerable:!o,value:r,writable:null===n&&E?E.writable:!n});else{if(!l&&(o||n||u))throw new i("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},92888:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r};e.exports=function(e){var n=this;if("function"!=typeof n||"[object Function]"!==t.apply(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var s,i=function(e,t){for(var r=[],o=1,n=0;o<e.length;o+=1,n+=1)r[n]=e[o];return r}(arguments),a=r(0,n.length-i.length),c=[],u=0;u<a;u++)c[u]="$"+u;if(s=Function("binder","return function ("+function(e,t){for(var r="",o=0;o<e.length;o+=1)r+=e[o],o+1<e.length&&(r+=",");return r}(c)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof s){var t=n.apply(this,o(i,arguments));return Object(t)===t?t:this}return n.apply(e,o(i,arguments))})),n.prototype){var l=function(){};l.prototype=n.prototype,s.prototype=new l,l.prototype=null}return s}},2073:(e,t,r)=>{"use strict";var o=r(92888);e.exports=Function.prototype.bind||o},9523:(e,t,r)=>{"use strict";var o,n=SyntaxError,s=Function,i=TypeError,a=function(e){try{return s('"use strict"; return ('+e+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(e){c=null}var u=function(){throw new i},l=c?function(){try{return u}catch(e){try{return c(arguments,"callee").get}catch(e){return u}}}():u,E=r(72770)(),d=r(57877)(),p=Object.getPrototypeOf||(d?function(e){return e.__proto__}:null),T={},S="undefined"!=typeof Uint8Array&&p?p(Uint8Array):o,_={"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":E&&p?p([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":s,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":E&&p?p(p([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&E&&p?p((new Map)[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&E&&p?p((new Set)[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":E&&p?p(""[Symbol.iterator]()):o,"%Symbol%":E?Symbol:o,"%SyntaxError%":n,"%ThrowTypeError%":l,"%TypedArray%":S,"%TypeError%":i,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet};if(p)try{null.error}catch(e){var y=p(p(e));_["%Error.prototype%"]=y}var f=function e(t){var r;if("%AsyncFunction%"===t)r=a("async function () {}");else if("%GeneratorFunction%"===t)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=a("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&p&&(r=p(n.prototype))}return _[t]=r,r},R={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},g=r(2073),m=r(69990),O=g.call(Function.call,Array.prototype.concat),h=g.call(Function.apply,Array.prototype.splice),A=g.call(Function.call,String.prototype.replace),I=g.call(Function.call,String.prototype.slice),C=g.call(Function.call,RegExp.prototype.exec),P=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,U=/\\(\\)?/g,v=function(e,t){var r,o=e;if(m(R,o)&&(o="%"+(r=R[o])[0]+"%"),m(_,o)){var s=_[o];if(s===T&&(s=f(o)),void 0===s&&!t)throw new i("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:s}}throw new n("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new i('"allowMissing" argument must be a boolean');if(null===C(/^%?[^%]*%?$/,e))throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=I(e,0,1),r=I(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var o=[];return A(e,P,(function(e,t,r,n){o[o.length]=r?A(n,U,"$1"):t||e})),o}(e),o=r.length>0?r[0]:"",s=v("%"+o+"%",t),a=s.name,u=s.value,l=!1,E=s.alias;E&&(o=E[0],h(r,O([0,1],E)));for(var d=1,p=!0;d<r.length;d+=1){var T=r[d],S=I(T,0,1),y=I(T,-1);if(('"'===S||"'"===S||"`"===S||'"'===y||"'"===y||"`"===y)&&S!==y)throw new n("property names with quotes must have matching quotes");if("constructor"!==T&&p||(l=!0),m(_,a="%"+(o+="."+T)+"%"))u=_[a];else if(null!=u){if(!(T in u)){if(!t)throw new i("base intrinsic for "+e+" exists, but the property is not available.");return}if(c&&d+1>=r.length){var f=c(u,T);u=(p=!!f)&&"get"in f&&!("originalValue"in f.get)?f.get:u[T]}else p=m(u,T),u=u[T];p&&!l&&(_[a]=u)}}return u}},98158:(e,t,r)=>{"use strict";var o=r(9523)("%Object.getOwnPropertyDescriptor%",!0);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},97547:(e,t,r)=>{"use strict";var o=r(9523)("%Object.defineProperty%",!0),n=function(){if(o)try{return o({},"a",{value:1}),!0}catch(e){return!1}return!1};n.hasArrayLengthDefineBug=function(){if(!n())return null;try{return 1!==o([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},57877:e=>{"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},72770:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(69578);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},69578:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(e,t);if(42!==n.value||!0!==n.enumerable)return!1}return!0}},69990:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,s=r(2073);e.exports=s.call(o,n)},17476:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},5681:(e,t,r)=>{var o,n,s,i,a;o=r(54274),n=r(29203).utf8,s=r(17476),i=r(29203).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?i.stringToBytes(e):n.stringToBytes(e):s(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=o.bytesToWords(e),c=8*e.length,u=1732584193,l=-271733879,E=-1732584194,d=271733878,p=0;p<r.length;p++)r[p]=16711935&(r[p]<<8|r[p]>>>24)|4278255360&(r[p]<<24|r[p]>>>8);r[c>>>5]|=128<<c%32,r[14+(c+64>>>9<<4)]=c;var T=a._ff,S=a._gg,_=a._hh,y=a._ii;for(p=0;p<r.length;p+=16){var f=u,R=l,g=E,m=d;u=T(u,l,E,d,r[p+0],7,-680876936),d=T(d,u,l,E,r[p+1],12,-389564586),E=T(E,d,u,l,r[p+2],17,606105819),l=T(l,E,d,u,r[p+3],22,-1044525330),u=T(u,l,E,d,r[p+4],7,-176418897),d=T(d,u,l,E,r[p+5],12,1200080426),E=T(E,d,u,l,r[p+6],17,-1473231341),l=T(l,E,d,u,r[p+7],22,-45705983),u=T(u,l,E,d,r[p+8],7,1770035416),d=T(d,u,l,E,r[p+9],12,-1958414417),E=T(E,d,u,l,r[p+10],17,-42063),l=T(l,E,d,u,r[p+11],22,-1990404162),u=T(u,l,E,d,r[p+12],7,1804603682),d=T(d,u,l,E,r[p+13],12,-40341101),E=T(E,d,u,l,r[p+14],17,-1502002290),u=S(u,l=T(l,E,d,u,r[p+15],22,1236535329),E,d,r[p+1],5,-165796510),d=S(d,u,l,E,r[p+6],9,-1069501632),E=S(E,d,u,l,r[p+11],14,643717713),l=S(l,E,d,u,r[p+0],20,-373897302),u=S(u,l,E,d,r[p+5],5,-701558691),d=S(d,u,l,E,r[p+10],9,38016083),E=S(E,d,u,l,r[p+15],14,-660478335),l=S(l,E,d,u,r[p+4],20,-405537848),u=S(u,l,E,d,r[p+9],5,568446438),d=S(d,u,l,E,r[p+14],9,-1019803690),E=S(E,d,u,l,r[p+3],14,-187363961),l=S(l,E,d,u,r[p+8],20,1163531501),u=S(u,l,E,d,r[p+13],5,-1444681467),d=S(d,u,l,E,r[p+2],9,-51403784),E=S(E,d,u,l,r[p+7],14,1735328473),u=_(u,l=S(l,E,d,u,r[p+12],20,-1926607734),E,d,r[p+5],4,-378558),d=_(d,u,l,E,r[p+8],11,-2022574463),E=_(E,d,u,l,r[p+11],16,1839030562),l=_(l,E,d,u,r[p+14],23,-35309556),u=_(u,l,E,d,r[p+1],4,-1530992060),d=_(d,u,l,E,r[p+4],11,1272893353),E=_(E,d,u,l,r[p+7],16,-155497632),l=_(l,E,d,u,r[p+10],23,-1094730640),u=_(u,l,E,d,r[p+13],4,681279174),d=_(d,u,l,E,r[p+0],11,-358537222),E=_(E,d,u,l,r[p+3],16,-722521979),l=_(l,E,d,u,r[p+6],23,76029189),u=_(u,l,E,d,r[p+9],4,-640364487),d=_(d,u,l,E,r[p+12],11,-421815835),E=_(E,d,u,l,r[p+15],16,530742520),u=y(u,l=_(l,E,d,u,r[p+2],23,-995338651),E,d,r[p+0],6,-198630844),d=y(d,u,l,E,r[p+7],10,1126891415),E=y(E,d,u,l,r[p+14],15,-1416354905),l=y(l,E,d,u,r[p+5],21,-57434055),u=y(u,l,E,d,r[p+12],6,1700485571),d=y(d,u,l,E,r[p+3],10,-1894986606),E=y(E,d,u,l,r[p+10],15,-1051523),l=y(l,E,d,u,r[p+1],21,-2054922799),u=y(u,l,E,d,r[p+8],6,1873313359),d=y(d,u,l,E,r[p+15],10,-30611744),E=y(E,d,u,l,r[p+6],15,-1560198380),l=y(l,E,d,u,r[p+13],21,1309151649),u=y(u,l,E,d,r[p+4],6,-145523070),d=y(d,u,l,E,r[p+11],10,-1120210379),E=y(E,d,u,l,r[p+2],15,718787259),l=y(l,E,d,u,r[p+9],21,-343485551),u=u+f>>>0,l=l+R>>>0,E=E+g>>>0,d=d+m>>>0}return o.endian([u,l,E,d])})._ff=function(e,t,r,o,n,s,i){var a=e+(t&r|~t&o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._gg=function(e,t,r,o,n,s,i){var a=e+(t&o|r&~o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._hh=function(e,t,r,o,n,s,i){var a=e+(t^r^o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._ii=function(e,t,r,o,n,s,i){var a=e+(r^(t|~o))+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=o.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?i.bytesToString(r):o.bytesToHex(r)}},94527:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,s=o&&n&&"function"==typeof n.get?n.get:null,i=o&&Map.prototype.forEach,a="function"==typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=a&&c&&"function"==typeof c.get?c.get:null,l=a&&Set.prototype.forEach,E="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,d="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,T=Boolean.prototype.valueOf,S=Object.prototype.toString,_=Function.prototype.toString,y=String.prototype.match,f=String.prototype.slice,R=String.prototype.replace,g=String.prototype.toUpperCase,m=String.prototype.toLowerCase,O=RegExp.prototype.test,h=Array.prototype.concat,A=Array.prototype.join,I=Array.prototype.slice,C=Math.floor,P="function"==typeof BigInt?BigInt.prototype.valueOf:null,U=Object.getOwnPropertySymbols,v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,N="function"==typeof Symbol&&"object"==typeof Symbol.iterator,D="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,w=Object.prototype.propertyIsEnumerable,b=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function k(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||O.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-C(-e):C(e);if(o!==e){var n=String(o),s=f.call(t,n.length+1);return R.call(n,r,"$&_")+"."+R.call(R.call(s,/([0-9]{3})/g,"$&_"),/_$/,"")}}return R.call(t,r,"$&_")}var G=r(93452),M=G.custom,L=Q(M)?M:null;function F(e,t,r){var o="double"===(r.quoteStyle||t)?'"':"'";return o+e+o}function q(e){return R.call(String(e),/"/g,"&quot;")}function j(e){return!("[object Array]"!==$(e)||D&&"object"==typeof e&&D in e)}function x(e){return!("[object RegExp]"!==$(e)||D&&"object"==typeof e&&D in e)}function Q(e){if(N)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!v)return!1;try{return v.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,a){var c=o||{};if(V(c,"quoteStyle")&&"single"!==c.quoteStyle&&"double"!==c.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(V(c,"maxStringLength")&&("number"==typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var S=!V(c,"customInspect")||c.customInspect;if("boolean"!=typeof S&&"symbol"!==S)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(V(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(V(c,"numericSeparator")&&"boolean"!=typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var g=c.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Y(t,c);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var O=String(t);return g?k(t,O):O}if("bigint"==typeof t){var C=String(t)+"n";return g?k(t,C):C}var U=void 0===c.depth?5:c.depth;if(void 0===n&&(n=0),n>=U&&U>0&&"object"==typeof t)return j(t)?"[Array]":"[Object]";var M,K=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=A.call(Array(e.indent+1)," ")}return{base:r,prev:A.call(Array(t+1),r)}}(c,n);if(void 0===a)a=[];else if(H(a,t)>=0)return"[Circular]";function W(t,r,o){if(r&&(a=I.call(a)).push(r),o){var s={depth:c.depth};return V(c,"quoteStyle")&&(s.quoteStyle=c.quoteStyle),e(t,s,n+1,a)}return e(t,c,n+1,a)}if("function"==typeof t&&!x(t)){var ee=function(e){if(e.name)return e.name;var t=y.call(_.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),te=X(t,W);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(te.length>0?" { "+A.call(te,", ")+" }":"")}if(Q(t)){var re=N?R.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):v.call(t);return"object"!=typeof t||N?re:J(re)}if((M=t)&&"object"==typeof M&&("undefined"!=typeof HTMLElement&&M instanceof HTMLElement||"string"==typeof M.nodeName&&"function"==typeof M.getAttribute)){for(var oe="<"+m.call(String(t.nodeName)),ne=t.attributes||[],se=0;se<ne.length;se++)oe+=" "+ne[se].name+"="+F(q(ne[se].value),"double",c);return oe+=">",t.childNodes&&t.childNodes.length&&(oe+="..."),oe+"</"+m.call(String(t.nodeName))+">"}if(j(t)){if(0===t.length)return"[]";var ie=X(t,W);return K&&!function(e){for(var t=0;t<e.length;t++)if(H(e[t],"\n")>=0)return!1;return!0}(ie)?"["+Z(ie,K)+"]":"[ "+A.call(ie,", ")+" ]"}if(function(e){return!("[object Error]"!==$(e)||D&&"object"==typeof e&&D in e)}(t)){var ae=X(t,W);return"cause"in Error.prototype||!("cause"in t)||w.call(t,"cause")?0===ae.length?"["+String(t)+"]":"{ ["+String(t)+"] "+A.call(ae,", ")+" }":"{ ["+String(t)+"] "+A.call(h.call("[cause]: "+W(t.cause),ae),", ")+" }"}if("object"==typeof t&&S){if(L&&"function"==typeof t[L]&&G)return G(t,{depth:U-n});if("symbol"!==S&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!s||!e||"object"!=typeof e)return!1;try{s.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ce=[];return i&&i.call(t,(function(e,r){ce.push(W(r,t,!0)+" => "+W(e,t))})),z("Map",s.call(t),ce,K)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{s.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ue=[];return l&&l.call(t,(function(e){ue.push(W(e,t))})),z("Set",u.call(t),ue,K)}if(function(e){if(!E||!e||"object"!=typeof e)return!1;try{E.call(e,E);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return B("WeakMap");if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{E.call(e,E)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return B("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return B("WeakRef");if(function(e){return!("[object Number]"!==$(e)||D&&"object"==typeof e&&D in e)}(t))return J(W(Number(t)));if(function(e){if(!e||"object"!=typeof e||!P)return!1;try{return P.call(e),!0}catch(e){}return!1}(t))return J(W(P.call(t)));if(function(e){return!("[object Boolean]"!==$(e)||D&&"object"==typeof e&&D in e)}(t))return J(T.call(t));if(function(e){return!("[object String]"!==$(e)||D&&"object"==typeof e&&D in e)}(t))return J(W(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==$(e)||D&&"object"==typeof e&&D in e)}(t)&&!x(t)){var le=X(t,W),Ee=b?b(t)===Object.prototype:t instanceof Object||t.constructor===Object,de=t instanceof Object?"":"null prototype",pe=!Ee&&D&&Object(t)===t&&D in t?f.call($(t),8,-1):de?"Object":"",Te=(Ee||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(pe||de?"["+A.call(h.call([],pe||[],de||[]),": ")+"] ":"");return 0===le.length?Te+"{}":K?Te+"{"+Z(le,K)+"}":Te+"{ "+A.call(le,", ")+" }"}return String(t)};var K=Object.prototype.hasOwnProperty||function(e){return e in this};function V(e,t){return K.call(e,t)}function $(e){return S.call(e)}function H(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return-1}function Y(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return Y(f.call(e,0,t.maxStringLength),t)+o}return F(R.call(R.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,W),"single",t)}function W(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+g.call(t.toString(16))}function J(e){return"Object("+e+")"}function B(e){return e+" { ? }"}function z(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):A.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+A.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=j(e),o=[];if(r){o.length=e.length;for(var n=0;n<e.length;n++)o[n]=V(e,n)?t(e[n],e):""}var s,i="function"==typeof U?U(e):[];if(N){s={};for(var a=0;a<i.length;a++)s["$"+i[a]]=i[a]}for(var c in e)V(e,c)&&(r&&String(Number(c))===c&&c<e.length||N&&s["$"+c]instanceof Symbol||(O.call(/[^\w$]/,c)?o.push(t(c,e)+": "+t(e[c],e)):o.push(c+": "+t(e[c],e))));if("function"==typeof U)for(var u=0;u<i.length;u++)w.call(e,i[u])&&o.push("["+t(i[u])+"]: "+t(e[i[u]],e));return o}},83949:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o="RFC3986";e.exports={default:o,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:o}},67905:(e,t,r)=>{"use strict";var o=r(85095),n=r(95990),s=r(83949);e.exports={formats:s,parse:n,stringify:o}},95990:(e,t,r)=>{"use strict";var o=r(37748),n=Object.prototype.hasOwnProperty,s=Array.isArray,i={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:o.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},a=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},u=function(e,t,r,o){if(e){var s=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=r.depth>0&&/(\[[^[\]]*])/.exec(s),u=a?s.slice(0,a.index):s,l=[];if(u){if(!r.plainObjects&&n.call(Object.prototype,u)&&!r.allowPrototypes)return;l.push(u)}for(var E=0;r.depth>0&&null!==(a=i.exec(s))&&E<r.depth;){if(E+=1,!r.plainObjects&&n.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(a[1])}return a&&l.push("["+s.slice(a.index)+"]"),function(e,t,r,o){for(var n=o?t:c(t,r),s=e.length-1;s>=0;--s){var i,a=e[s];if("[]"===a&&r.parseArrays)i=[].concat(n);else{i=r.plainObjects?Object.create(null):{};var u="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,l=parseInt(u,10);r.parseArrays||""!==u?!isNaN(l)&&a!==u&&String(l)===u&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(i=[])[l]=n:"__proto__"!==u&&(i[u]=n):i={0:n}}n=i}return n}(l,t,r,o)}};e.exports=function(e,t){var r=function(e){if(!e)return i;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?i.charset:e.charset;return{allowDots:void 0===e.allowDots?i.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var l="string"==typeof e?function(e,t){var r,u={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,E=t.parameterLimit===1/0?void 0:t.parameterLimit,d=l.split(t.delimiter,E),p=-1,T=t.charset;if(t.charsetSentinel)for(r=0;r<d.length;++r)0===d[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===d[r]?T="utf-8":"utf8=%26%2310003%3B"===d[r]&&(T="iso-8859-1"),p=r,r=d.length);for(r=0;r<d.length;++r)if(r!==p){var S,_,y=d[r],f=y.indexOf("]="),R=-1===f?y.indexOf("="):f+1;-1===R?(S=t.decoder(y,i.decoder,T,"key"),_=t.strictNullHandling?null:""):(S=t.decoder(y.slice(0,R),i.decoder,T,"key"),_=o.maybeMap(c(y.slice(R+1),t),(function(e){return t.decoder(e,i.decoder,T,"value")}))),_&&t.interpretNumericEntities&&"iso-8859-1"===T&&(_=a(_)),y.indexOf("[]=")>-1&&(_=s(_)?[_]:_),n.call(u,S)?u[S]=o.combine(u[S],_):u[S]=_}return u}(e,r):e,E=r.plainObjects?Object.create(null):{},d=Object.keys(l),p=0;p<d.length;++p){var T=d[p],S=u(T,l[T],r,"string"==typeof e);E=o.merge(E,S,r)}return!0===r.allowSparse?E:o.compact(E)}},85095:(e,t,r)=>{"use strict";var o=r(44852),n=r(37748),s=r(83949),i=Object.prototype.hasOwnProperty,a={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},c=Array.isArray,u=Array.prototype.push,l=function(e,t){u.apply(e,c(t)?t:[t])},E=Date.prototype.toISOString,d=s.default,p={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:d,formatter:s.formatters[d],indices:!1,serializeDate:function(e){return E.call(e)},skipNulls:!1,strictNullHandling:!1},T={},S=function e(t,r,s,i,a,u,E,d,S,_,y,f,R,g,m,O){for(var h,A=t,I=O,C=0,P=!1;void 0!==(I=I.get(T))&&!P;){var U=I.get(t);if(C+=1,void 0!==U){if(U===C)throw new RangeError("Cyclic object value");P=!0}void 0===I.get(T)&&(C=0)}if("function"==typeof d?A=d(r,A):A instanceof Date?A=y(A):"comma"===s&&c(A)&&(A=n.maybeMap(A,(function(e){return e instanceof Date?y(e):e}))),null===A){if(a)return E&&!g?E(r,p.encoder,m,"key",f):r;A=""}if("string"==typeof(h=A)||"number"==typeof h||"boolean"==typeof h||"symbol"==typeof h||"bigint"==typeof h||n.isBuffer(A))return E?[R(g?r:E(r,p.encoder,m,"key",f))+"="+R(E(A,p.encoder,m,"value",f))]:[R(r)+"="+R(String(A))];var v,N=[];if(void 0===A)return N;if("comma"===s&&c(A))g&&E&&(A=n.maybeMap(A,E)),v=[{value:A.length>0?A.join(",")||null:void 0}];else if(c(d))v=d;else{var D=Object.keys(A);v=S?D.sort(S):D}for(var w=i&&c(A)&&1===A.length?r+"[]":r,b=0;b<v.length;++b){var k=v[b],G="object"==typeof k&&void 0!==k.value?k.value:A[k];if(!u||null!==G){var M=c(A)?"function"==typeof s?s(w,k):w:w+(_?"."+k:"["+k+"]");O.set(t,C);var L=o();L.set(T,O),l(N,e(G,M,s,i,a,u,"comma"===s&&g&&c(A)?null:E,d,S,_,y,f,R,g,m,L))}}return N};e.exports=function(e,t){var r,n=e,u=function(e){if(!e)return p;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=s.default;if(void 0!==e.format){if(!i.call(s.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var o=s.formatters[r],n=p.filter;return("function"==typeof e.filter||c(e.filter))&&(n=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:void 0===e.allowDots?p.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}}(t);"function"==typeof u.filter?n=(0,u.filter)("",n):c(u.filter)&&(r=u.filter);var E,d=[];if("object"!=typeof n||null===n)return"";E=t&&t.arrayFormat in a?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var T=a[E];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var _="comma"===T&&t&&t.commaRoundTrip;r||(r=Object.keys(n)),u.sort&&r.sort(u.sort);for(var y=o(),f=0;f<r.length;++f){var R=r[f];u.skipNulls&&null===n[R]||l(d,S(n[R],R,T,_,u.strictNullHandling,u.skipNulls,u.encode?u.encoder:null,u.filter,u.sort,u.allowDots,u.serializeDate,u.format,u.formatter,u.encodeValuesOnly,u.charset,y))}var g=d.join(u.delimiter),m=!0===u.addQueryPrefix?"?":"";return u.charsetSentinel&&("iso-8859-1"===u.charset?m+="utf8=%26%2310003%3B&":m+="utf8=%E2%9C%93&"),g.length>0?m+g:""}},37748:(e,t,r)=>{"use strict";var o=r(83949),n=Object.prototype.hasOwnProperty,s=Array.isArray,i=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),a=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:a,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),c=0;c<a.length;++c){var u=a[c],l=i[u];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(t.push({obj:i,prop:u}),r.push(l))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(s(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}}(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,s){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var c="",u=0;u<a.length;++u){var l=a.charCodeAt(u);45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||s===o.RFC1738&&(40===l||41===l)?c+=a.charAt(u):l<128?c+=i[l]:l<2048?c+=i[192|l>>6]+i[128|63&l]:l<55296||l>=57344?c+=i[224|l>>12]+i[128|l>>6&63]+i[128|63&l]:(u+=1,l=65536+((1023&l)<<10|1023&a.charCodeAt(u)),c+=i[240|l>>18]+i[128|l>>12&63]+i[128|l>>6&63]+i[128|63&l])}return c},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(s(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(s(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var i=t;return s(t)&&!s(r)&&(i=a(t,o)),s(t)&&s(r)?(r.forEach((function(r,s){if(n.call(t,s)){var i=t[s];i&&"object"==typeof i&&r&&"object"==typeof r?t[s]=e(i,r,o):t.push(r)}else t[s]=r})),t):Object.keys(r).reduce((function(t,s){var i=r[s];return n.call(t,s)?t[s]=e(t[s],i,o):t[s]=i,t}),i)}}},71138:(e,t,r)=>{"use strict";var o=r(9523),n=r(11131),s=r(97547)(),i=r(98158),a=o("%TypeError%"),c=o("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new a("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||c(t)!==t)throw new a("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,u=!0;if("length"in e&&i){var l=i(e,"length");l&&!l.configurable&&(o=!1),l&&!l.writable&&(u=!1)}return(o||u||!r)&&(s?n(e,"length",t,!0,!0):n(e,"length",t)),e}},44852:(e,t,r)=>{"use strict";var o=r(9523),n=r(67639),s=r(94527),i=o("%TypeError%"),a=o("%WeakMap%",!0),c=o("%Map%",!0),u=n("WeakMap.prototype.get",!0),l=n("WeakMap.prototype.set",!0),E=n("WeakMap.prototype.has",!0),d=n("Map.prototype.get",!0),p=n("Map.prototype.set",!0),T=n("Map.prototype.has",!0),S=function(e,t){for(var r,o=e;null!==(r=o.next);o=r)if(r.key===t)return o.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new i("Side channel does not contain "+s(e))},get:function(o){if(a&&o&&("object"==typeof o||"function"==typeof o)){if(e)return u(e,o)}else if(c){if(t)return d(t,o)}else if(r)return function(e,t){var r=S(e,t);return r&&r.value}(r,o)},has:function(o){if(a&&o&&("object"==typeof o||"function"==typeof o)){if(e)return E(e,o)}else if(c){if(t)return T(t,o)}else if(r)return function(e,t){return!!S(e,t)}(r,o);return!1},set:function(o,n){a&&o&&("object"==typeof o||"function"==typeof o)?(e||(e=new a),l(e,o,n)):c?(t||(t=new c),p(t,o,n)):(r||(r={key:{},next:null}),function(e,t,r){var o=S(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}}(r,o,n))}};return o}},93452:()=>{}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";r.r(o),r.d(o,{COUNTRIES_STORE_NAME:()=>Es,DAY:()=>Ge,EXPERIMENTAL_PRODUCT_ATTRIBUTES_STORE_NAME:()=>gc,EXPERIMENTAL_PRODUCT_ATTRIBUTE_TERMS_STORE_NAME:()=>Nc,EXPERIMENTAL_PRODUCT_CATEGORIES_STORE_NAME:()=>Uc,EXPERIMENTAL_PRODUCT_FORM_STORE_NAME:()=>nu,EXPERIMENTAL_PRODUCT_SHIPPING_CLASSES_STORE_NAME:()=>Oc,EXPERIMENTAL_PRODUCT_TAGS_STORE_NAME:()=>Cc,EXPERIMENTAL_PRODUCT_VARIATIONS_STORE_NAME:()=>$c,EXPERIMENTAL_SHIPPING_ZONES_STORE_NAME:()=>Ac,EXPERIMENTAL_TAX_CLASSES_STORE_NAME:()=>cu,EXPORT_STORE_NAME:()=>Wu,HOUR:()=>ke,IMPORT_STORE_NAME:()=>El,ITEMS_STORE_NAME:()=>ti,MAX_PER_PAGE:()=>De,MINUTE:()=>be,MONTH:()=>Le,NAMESPACE:()=>Ue,NAVIGATION_STORE_NAME:()=>Gs,NOTES_STORE_NAME:()=>Pn,ONBOARDING_STORE_NAME:()=>Yo,OPTIONS_STORE_NAME:()=>tr,ORDERS_STORE_NAME:()=>Ka,PAYMENT_GATEWAYS_STORE_NAME:()=>Oi,PLUGINS_STORE_NAME:()=>dr,PRODUCTS_STORE_NAME:()=>Aa,QUERY_DEFAULTS:()=>Fe,REPORTS_STORE_NAME:()=>Kn,REVIEWS_STORE_NAME:()=>un,SECOND:()=>we,SETTINGS_STORE_NAME:()=>Xe,SHIPPING_METHODS_STORE_NAME:()=>Ni,USER_STORE_NAME:()=>le,WCS_NAMESPACE:()=>Ne,WC_ADMIN_NAMESPACE:()=>ve,WEEK:()=>Me,getFilterQuery:()=>Iu,getLeaderboard:()=>Fs,getReportChartData:()=>wu,getReportTableData:()=>Gu,getReportTableQuery:()=>ku,getSummaryNumbers:()=>Uu,getTooltipValueFormat:()=>bu,getVisibleTasks:()=>Ou,isRestApiError:()=>We,pluginNames:()=>rt,productReadOnlyProperties:()=>dl,searchItemsByString:()=>qs,useOptionsHydration:()=>_u,useSettings:()=>fu,useUser:()=>mu,useUserPreferences:()=>gu,withCurrentUserHydration:()=>pu,withNavigationHydration:()=>Tu,withOnboardingHydration:()=>du,withOptionsHydration:()=>yu,withPluginsHydration:()=>Su,withSettingsHydration:()=>Eu});var e={};r.r(e),r.d(e,{getDirtyKeys:()=>fe,getIsDirty:()=>Re,getLastSettingsErrorForGroup:()=>he,getSetting:()=>Oe,getSettings:()=>ye,getSettingsError:()=>Ae,getSettingsForGroup:()=>ge,getSettingsGroupNames:()=>_e,isUpdateSettingsRequesting:()=>me});var t={};r.r(t),r.d(t,{clearIsDirty:()=>Ve,clearSettings:()=>Ye,persistSettingsForGroup:()=>$e,setIsRequesting:()=>Ke,updateAndPersistSettingsForGroup:()=>He,updateErrorForGroup:()=>Qe,updateSettingsForGroup:()=>xe});var n={};r.r(n),r.d(n,{getSettings:()=>Be,getSettingsForGroup:()=>ze});var s={};r.r(s),r.d(s,{getActivePlugins:()=>ot,getInstalledPlugins:()=>nt,getJetpackConnectUrl:()=>ut,getJetpackConnectionData:()=>ct,getPaypalOnboardingStatus:()=>Et,getPluginInstallState:()=>lt,getPluginsError:()=>it,getRecommendedPlugins:()=>dt,isJetpackConnected:()=>at,isPluginsRequesting:()=>st});var i={};r.r(i),r.d(i,{activatePlugins:()=>wt,connectToJetpack:()=>kt,connectToJetpackWithFailureRedirect:()=>Mt,createErrorNotice:()=>Pt,dismissRecommendedPlugins:()=>Ft,installAndActivatePlugins:()=>bt,installJetpackAndConnect:()=>Gt,installPlugins:()=>Dt,setError:()=>ht,setIsRequesting:()=>Ot,setPaypalOnboardingStatus:()=>Ut,setRecommendedPlugins:()=>vt,updateActivePlugins:()=>gt,updateInstalledPlugins:()=>mt,updateIsJetpackConnected:()=>At,updateJetpackConnectUrl:()=>Ct,updateJetpackConnectionData:()=>It});var a={};r.r(a),r.d(a,{getOption:()=>jt,getOptionsRequestingError:()=>xt,getOptionsUpdatingError:()=>Kt,isOptionsUpdating:()=>Qt});var c={};r.r(c),r.d(c,{receiveOptions:()=>$t,setIsUpdating:()=>Wt,setRequestingError:()=>Ht,setUpdatingError:()=>Yt,updateOptions:()=>Jt});var u={};r.r(u),r.d(u,{getOption:()=>er});var l={};r.r(l),r.d(l,{getActivePlugins:()=>or,getInstalledPlugins:()=>nr,getJetpackConnectUrl:()=>ar,getJetpackConnectionData:()=>ir,getPaypalOnboardingStatus:()=>ur,getRecommendedPlugins:()=>Er,isJetpackConnected:()=>sr});var E={};r.r(E),r.d(E,{getEmailPrefill:()=>Pr,getFreeExtensions:()=>fr,getJetpackAuthUrl:()=>vr,getOnboardingError:()=>Ir,getPaymentGatewaySuggestions:()=>Ar,getProductTypes:()=>Ur,getProfileItems:()=>Rr,getTask:()=>hr,getTaskList:()=>Or,getTaskLists:()=>gr,getTaskListsByIds:()=>mr,isOnboardingRequesting:()=>Cr});var d={};r.r(d),r.d(d,{actionTask:()=>Po,actionTaskError:()=>po,actionTaskRequest:()=>To,actionTaskSuccess:()=>So,coreProfilerCompleted:()=>bo,coreProfilerCompletedError:()=>No,coreProfilerCompletedRequest:()=>Do,coreProfilerCompletedSuccess:()=>wo,dismissTask:()=>Oo,dismissTaskError:()=>Jr,dismissTaskRequest:()=>Br,dismissTaskSuccess:()=>zr,getFreeExtensionsError:()=>Mr,getFreeExtensionsSuccess:()=>Lr,getProductTypesError:()=>yo,getProductTypesSuccess:()=>_o,getTaskListsError:()=>xr,getTaskListsSuccess:()=>Qr,hideTaskList:()=>Ao,hideTaskListError:()=>to,hideTaskListRequest:()=>ro,hideTaskListSuccess:()=>oo,installAndActivatePluginsAsync:()=>Uo,keepCompletedTaskList:()=>fo,keepCompletedTaskListSuccess:()=>co,optimisticallyCompleteTask:()=>Co,optimisticallyCompleteTaskRequest:()=>ao,setEmailPrefill:()=>Eo,setError:()=>Fr,setIsRequesting:()=>qr,setJetpackAuthUrl:()=>vo,setPaymentMethods:()=>lo,setProfileItems:()=>jr,snoozeTask:()=>go,snoozeTaskError:()=>Kr,snoozeTaskRequest:()=>Vr,snoozeTaskSuccess:()=>$r,undoDismissTask:()=>ho,undoDismissTaskError:()=>Zr,undoDismissTaskRequest:()=>Xr,undoDismissTaskSuccess:()=>eo,undoSnoozeTask:()=>mo,undoSnoozeTaskError:()=>Hr,undoSnoozeTaskRequest:()=>Yr,undoSnoozeTaskSuccess:()=>Wr,unhideTaskList:()=>Io,unhideTaskListError:()=>no,unhideTaskListRequest:()=>so,unhideTaskListSuccess:()=>io,updateProfileItems:()=>Ro,visitedTask:()=>uo});var p={};r.r(p),r.d(p,{getEmailPrefill:()=>Mo,getFreeExtensions:()=>Qo,getJetpackAuthUrl:()=>Vo,getPaymentGatewaySuggestions:()=>xo,getProductTypes:()=>Ko,getProfileItems:()=>Go,getTask:()=>jo,getTaskList:()=>qo,getTaskLists:()=>Lo,getTaskListsByIds:()=>Fo});var T={};r.r(T),r.d(T,{getReviews:()=>Jo,getReviewsError:()=>zo,getReviewsTotalCount:()=>Bo});var S={};r.r(S),r.d(S,{deleteReview:()=>nn,setError:()=>tn,setReview:()=>en,setReviewIsUpdating:()=>Xo,updateReview:()=>on,updateReviews:()=>rn});var _={};r.r(_),r.d(_,{getReviews:()=>sn,getReviewsTotalCount:()=>an});var y={};r.r(y),r.d(y,{getNotes:()=>En,getNotesError:()=>dn,isNotesRequesting:()=>pn});var f={};r.r(f),r.d(f,{batchUpdateNotes:()=>In,removeAllNotes:()=>An,removeNote:()=>hn,setError:()=>Rn,setIsRequesting:()=>gn,setNote:()=>Sn,setNoteIsUpdating:()=>_n,setNotes:()=>yn,setNotesQuery:()=>fn,triggerNoteAction:()=>On,updateNote:()=>mn});var R={};r.r(R),r.d(R,{getNotes:()=>Cn});var g={};r.r(g),r.d(g,{getReportItems:()=>Dn,getReportItemsError:()=>Nn,getReportStats:()=>wn,getReportStatsError:()=>bn});var m={};r.r(m),r.d(m,{setReportItems:()=>Mn,setReportItemsError:()=>Gn,setReportStats:()=>Ln,setReportStatsError:()=>Fn});var O={};r.r(O),r.d(O,{getReportItems:()=>jn,getReportStats:()=>xn});var h={};r.r(h),r.d(h,{geolocate:()=>Jn,getCountries:()=>Yn,getCountry:()=>Wn,getLocale:()=>Hn,getLocales:()=>$n});var A={};r.r(A),r.d(A,{geolocationError:()=>ns,geolocationSuccess:()=>os,getCountriesError:()=>rs,getCountriesSuccess:()=>ts,getLocalesError:()=>es,getLocalesSuccess:()=>Xn});var I={};r.r(I),r.d(I,{geolocate:()=>ls,getCountries:()=>us,getCountry:()=>cs,getLocale:()=>is,getLocales:()=>as});var C={};r.r(C),r.d(C,{getFavorites:()=>Ts,getMenuItems:()=>ps,getPersistedQuery:()=>_s,isNavigationRequesting:()=>Ss});var P={};r.r(P),r.d(P,{addFavorite:()=>ws,addFavoriteFailure:()=>Is,addFavoriteRequest:()=>As,addFavoriteSuccess:()=>Cs,addMenuItems:()=>gs,getFavoritesFailure:()=>ms,getFavoritesRequest:()=>Os,getFavoritesSuccess:()=>hs,onHistoryChange:()=>Ns,onLoad:()=>Ds,removeFavorite:()=>bs,removeFavoriteFailure:()=>Us,removeFavoriteRequest:()=>Ps,removeFavoriteSuccess:()=>vs,setMenuItems:()=>Rs});var U={};r.r(U),r.d(U,{getFavorites:()=>ks});var v={};r.r(v),r.d(v,{getItems:()=>xs,getItemsError:()=>Ks,getItemsTotalCount:()=>Qs});var N={};r.r(N),r.d(N,{createProductFromTemplate:()=>Bs,setError:()=>Ws,setItem:()=>$s,setItems:()=>Hs,setItemsTotalCount:()=>Ys,updateProductStock:()=>Js});var D={};r.r(D),r.d(D,{getItems:()=>zs,getItemsTotalCount:()=>Zs,getReviewsTotalCount:()=>Xs});var w={};r.r(w),r.d(w,{getPaymentGatewayError:()=>ui,getPaymentGatewayRequest:()=>ci,getPaymentGatewaySuccess:()=>li,getPaymentGatewaysError:()=>ai,getPaymentGatewaysRequest:()=>si,getPaymentGatewaysSuccess:()=>ii,updatePaymentGateway:()=>Ti,updatePaymentGatewayError:()=>pi,updatePaymentGatewayRequest:()=>di,updatePaymentGatewaySuccess:()=>Ei});var b={};r.r(b),r.d(b,{getPaymentGateway:()=>yi,getPaymentGateways:()=>_i});var k={};r.r(k),r.d(k,{getPaymentGateway:()=>fi,getPaymentGatewayError:()=>gi,getPaymentGateways:()=>Ri,isPaymentGatewayUpdating:()=>mi});var G={};r.r(G),r.d(G,{getShippingMethodsError:()=>Ci,getShippingMethodsRequest:()=>Ai,getShippingMethodsSuccess:()=>Ii});var M={};r.r(M),r.d(M,{getShippingMethods:()=>Pi});var L={};r.r(L),r.d(L,{getShippingMethods:()=>Ui,isShippingMethodsUpdating:()=>vi});var F={};r.r(F),r.d(F,{getCreateProductError:()=>Vi,getDeleteProductError:()=>Hi,getPermalinkParts:()=>Wi,getProduct:()=>ji,getProducts:()=>xi,getProductsError:()=>Ki,getProductsTotalCount:()=>Qi,getRelatedProducts:()=>Ji,getSuggestedProducts:()=>Bi,getUpdateProductError:()=>$i,isPending:()=>Yi});var q={};r.r(q),r.d(q,{createProduct:()=>ca,createProductError:()=>ta,deleteProduct:()=>Ta,deleteProductError:()=>pa,deleteProductStart:()=>Ea,deleteProductSuccess:()=>da,duplicateProduct:()=>la,duplicateProductError:()=>ra,getProductError:()=>ea,getProductSuccess:()=>Xi,getProductsError:()=>sa,getProductsSuccess:()=>na,getProductsTotalCountError:()=>aa,getProductsTotalCountSuccess:()=>ia,setSuggestedProductAction:()=>Sa,updateProduct:()=>ua,updateProductError:()=>oa});var j={};r.r(j),r.d(j,{getPermalinkParts:()=>Oa,getProduct:()=>Ra,getProducts:()=>fa,getProductsTotalCount:()=>ma,getRelatedProducts:()=>ga,getSuggestedProducts:()=>ha});var x={};r.r(x),r.d(x,{getOrders:()=>Na,getOrdersError:()=>wa,getOrdersTotalCount:()=>Da});var Q={};r.r(Q),r.d(Q,{getOrderError:()=>Ma,getOrderSuccess:()=>Ga,getOrdersError:()=>Fa,getOrdersSuccess:()=>La,getOrdersTotalCountError:()=>ja,getOrdersTotalCountSuccess:()=>qa});var K={};r.r(K),r.d(K,{getOrders:()=>xa,getOrdersTotalCount:()=>Qa});var V={};r.r(V),r.d(V,{batchUpdateProductVariations:()=>Qc,batchUpdateProductVariationsError:()=>xc,generateProductVariations:()=>jc,generateProductVariationsError:()=>Lc,generateProductVariationsRequest:()=>Fc,generateProductVariationsSuccess:()=>qc});var $={};r.r($),r.d($,{generateProductVariationsError:()=>Vc,isGeneratingVariations:()=>Kc});var H={};r.r(H),r.d(H,{getField:()=>Wc,getFields:()=>Yc,getProductForm:()=>Jc});var Y={};r.r(Y),r.d(Y,{getFieldsError:()=>Xc,getFieldsSuccess:()=>Zc,getProductFormError:()=>tu,getProductFormSuccess:()=>eu});var W={};r.r(W),r.d(W,{getFields:()=>ru,getProductForm:()=>ou});var J={};r.r(J),r.d(J,{getTaxClasses:()=>au});var B={};r.r(B),r.d(B,{getError:()=>Qu,getExportId:()=>xu,isExportRequesting:()=>ju});var z={};r.r(z),r.d(z,{setError:()=>Hu,setExportId:()=>Vu,setIsRequesting:()=>$u,startExport:()=>Yu});var Z={};r.r(Z),r.d(Z,{getFormSettings:()=>zu,getImportError:()=>el,getImportStarted:()=>Bu,getImportStatus:()=>Zu,getImportTotals:()=>Xu});var X={};r.r(X),r.d(X,{setImportError:()=>al,setImportPeriod:()=>ol,setImportStarted:()=>rl,setImportStatus:()=>sl,setImportTotals:()=>il,setSkipPrevious:()=>nl,updateImportation:()=>cl});var ee={};r.r(ee),r.d(ee,{getImportStatus:()=>ul,getImportTotals:()=>ll}),window.wp.coreData;const te=window.wp.data,re=window.wp.dataControls,oe="wc/admin/settings",ne=window.wp.url,se=window.wp.apiFetch;var ie=r.n(se);const ae=e=>({type:"FETCH_WITH_HEADERS",options:e}),ce={...re.controls,FETCH_WITH_HEADERS:e=>ie()({...e.options,parse:!1}).then((e=>Promise.all([e.headers,e.status,e.json()]))).then((([e,t,r])=>({headers:e,status:t,data:r}))).catch((e=>e.json().then((e=>{throw e}))))},ue="core",le=ue;function Ee(e,t){if(t){if(Array.isArray(t))return[...t].sort();if("object"==typeof t)return Object.entries(t).sort().reduce(((e,[t,r])=>({...e,[t]:r})),{})}return t}function de(e,...t){return`${e}:${JSON.stringify(t,Ee).replace(/\\"/g,'"')}`}function pe(e,t){const{_fields:r,page:o,per_page:n,order:s,orderby:i,...a}=t;return de(e,a)}function*Te(e,t){const r=(0,ne.addQueryArgs)(e,t),o=-1===t.per_page,n=o?re.apiFetch:ae,s=yield n({path:r,method:"GET"});if(o&&!("data"in s))return{items:s,totalCount:s.length};if(!o&&"data"in s){const e=parseInt(s.headers.get("x-wp-total")||"",10);return{items:s.data,totalCount:e}}}function*Se(e){if(!(yield(0,re.select)(le,"getCurrentUser")).capabilities[e])throw new Error(`User does not have ${e} capability.`)}const _e=e=>[...new Set(Object.keys(e).map((e=>function(e){const t=e.indexOf(":");return t<0?e:e.substring(0,t)}(e))))],ye=(e,t)=>{const r={},o=e[t]&&e[t].data||[];return Array.isArray(o)&&0!==o.length?(o.forEach((o=>{r[o]=e[de(t,o)].data})),r):r},fe=(e,t)=>e[t].dirty||[],Re=(e,t,r=[])=>{const o=fe(e,t);return 0!==o.length&&r.some((e=>o.includes(e)))},ge=(e,t,r)=>{const o=ye(e,t);return r.reduce(((e,t)=>(e[t]=o[t]||{},e)),{})},me=(e,t)=>e[t]&&Boolean(e[t].isRequesting);function Oe(e,t,r,o=!1,n=((e,t)=>e)){const s=de(t,r);return n(e[s]&&e[s].data||o,o)}const he=(e,t)=>{const r=e[t].data;return Array.isArray(r)&&0!==r.length?[...r].pop().error:e[t].error},Ae=(e,t,r)=>r?e[de(t,r)].error||!1:e[t]&&e[t].error||!1,Ie=window.wp.i18n,Ce=window.lodash,Pe="/jetpack/v4",Ue="/wc-analytics",ve="/wc-admin",Ne="/wc/v1",De=100,we=1e3,be=60*we,ke=60*be,Ge=24*ke,Me=7*Ge,Le=365*Ge/12,Fe={pageSize:25,period:"month",compare:"previous_year",noteTypes:["info","marketing","survey","warning"]},qe={UPDATE_SETTINGS_FOR_GROUP:"UPDATE_SETTINGS_FOR_GROUP",UPDATE_ERROR_FOR_GROUP:"UPDATE_ERROR_FOR_GROUP",CLEAR_SETTINGS:"CLEAR_SETTINGS",SET_IS_REQUESTING:"SET_IS_REQUESTING",CLEAR_IS_DIRTY:"CLEAR_IS_DIRTY"},je=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;function xe(e,t,r=new Date){return{type:qe.UPDATE_SETTINGS_FOR_GROUP,group:e,data:t,time:r}}function Qe(e,t,r,o=new Date){return{type:qe.UPDATE_ERROR_FOR_GROUP,group:e,data:t,error:r,time:o}}function Ke(e,t){return{type:qe.SET_IS_REQUESTING,group:e,isRequesting:t}}function Ve(e){return{type:qe.CLEAR_IS_DIRTY,group:e}}function*$e(e){yield Ke(e,!0);const t=yield je(oe,"getDirtyKeys",e);if(0===t.length)return void(yield Ke(e,!1));const r=yield je(oe,"getSettingsForGroup",e,t),o=`${Ue}/settings/${e}/batch`,n=t.reduce(((e,t)=>{const o=Object.keys(r[t]).map((e=>({id:e,value:r[t][e]})));return(0,Ce.concat)(e,o)}),[]);try{const t=yield(0,re.apiFetch)({path:o,method:"POST",data:{update:n}});if(yield Ke(e,!1),!t)throw new Error((0,Ie.__)("There was a problem updating your settings.","woocommerce"));yield Ve(e)}catch(t){throw yield Qe(e,null,t),yield Ke(e,!1),t}}function*He(e,t){yield Ke(e,!0),yield xe(e,t),yield*$e(e)}function Ye(){return{type:qe.CLEAR_SETTINGS}}const We=e=>void 0!==e.code&&void 0!==e.message,Je=te.controls&&te.controls.dispatch?te.controls.dispatch:re.dispatch;function*Be(e){yield Je(oe,"setIsRequesting",e,!0);try{const t=Ue+"/settings/"+e;return xe(e,{[e]:(yield(0,re.apiFetch)({path:t,method:"GET"})).reduce(((e,t)=>(e[t.id]=t.value,e)),{})})}catch(t){if(t instanceof Error||We(t))return Qe(e,null,t.message);throw`Unexpected error ${t}`}}function*ze(e){return Be(e)}const Ze=(e,{group:t,groupIds:r,data:o,time:n,error:s})=>(r.forEach((r=>{e[de(t,r)]={data:o[r],lastReceived:n,error:s}})),e);(0,te.registerStore)(oe,{reducer:(e={},t)=>{var r;const o={};switch(t.type){case qe.SET_IS_REQUESTING:e={...e,[t.group]:{...e[t.group],isRequesting:t.isRequesting}};break;case qe.CLEAR_IS_DIRTY:e={...e,[t.group]:{...e[t.group],dirty:[]}};break;case qe.UPDATE_SETTINGS_FOR_GROUP:case qe.UPDATE_ERROR_FOR_GROUP:const{data:n,group:s,time:i}=t,a=n?Object.keys(n):[],c=t.type===qe.UPDATE_ERROR_FOR_GROUP?t.error:null;if(null===n)e={...e,[s]:{data:e[s]?e[s].data:[],error:c,lastReceived:i}};else{const t=e[s];e={...e,[s]:{data:t&&t.data&&Array.isArray(t.data)?[...t.data,...a]:a,error:c,lastReceived:i,isRequesting:(null===(r=e[s])||void 0===r?void 0:r.isRequesting)||!1,dirty:e[s]&&e[s].dirty?(0,Ce.union)(e[s].dirty,a):a},...Ze(o,{group:s,groupIds:a,data:n,time:i,error:c})}}break;case qe.CLEAR_SETTINGS:e={}}return e},actions:t,controls:re.controls,selectors:e,resolvers:n});const Xe=oe,et="wc/admin/plugins",tt="/wc-paypal/v1",rt={"facebook-for-woocommerce":(0,Ie.__)("Facebook for WooCommerce","woocommerce"),jetpack:(0,Ie.__)("Jetpack","woocommerce"),"klarna-checkout-for-woocommerce":(0,Ie.__)("Klarna Checkout for WooCommerce","woocommerce"),"klarna-payments-for-woocommerce":(0,Ie.__)("Klarna Payments for WooCommerce","woocommerce"),"mailchimp-for-woocommerce":(0,Ie.__)("Mailchimp for WooCommerce","woocommerce"),"creative-mail-by-constant-contact":(0,Ie.__)("Creative Mail for WooCommerce","woocommerce"),"woocommerce-gateway-paypal-express-checkout":(0,Ie.__)("WooCommerce PayPal","woocommerce"),"woocommerce-gateway-stripe":(0,Ie.__)("WooCommerce Stripe","woocommerce"),"woocommerce-payfast-gateway":(0,Ie.__)("WooCommerce Payfast","woocommerce"),"woocommerce-payments":(0,Ie.__)("WooPayments","woocommerce"),"woocommerce-services":(0,Ie.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-services:shipping":(0,Ie.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-services:tax":(0,Ie.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-shipstation-integration":(0,Ie.__)("WooCommerce ShipStation Gateway","woocommerce"),"woocommerce-mercadopago":(0,Ie.__)("Mercado Pago payments for WooCommerce","woocommerce"),"google-listings-and-ads":(0,Ie.__)("Google for WooCommerce","woocommerce"),"woo-razorpay":(0,Ie.__)("Razorpay","woocommerce"),mailpoet:(0,Ie.__)("MailPoet","woocommerce"),"pinterest-for-woocommerce":(0,Ie.__)("Pinterest for WooCommerce","woocommerce"),"tiktok-for-business:alt":(0,Ie.__)("TikTok for WooCommerce","woocommerce"),codistoconnect:(0,Ie.__)("Omnichannel for WooCommerce","woocommerce")},ot=e=>e.active||[],nt=e=>e.installed||[],st=(e,t)=>e.requesting[t]||!1,it=(e,t)=>e.errors[t]||!1,at=e=>e.jetpackConnection,ct=e=>e.jetpackConnectionData,ut=(e,t)=>e.jetpackConnectUrls[t.redirect_url],lt=(e,t)=>e.active.includes(t)?"activated":e.installed.includes(t)?"installed":"unavailable",Et=e=>e.paypalOnboardingStatus,dt=(e,t)=>e.recommended[t],pt=window.wc.tracks;var Tt;!function(e){e.UPDATE_ACTIVE_PLUGINS="UPDATE_ACTIVE_PLUGINS",e.UPDATE_INSTALLED_PLUGINS="UPDATE_INSTALLED_PLUGINS",e.SET_IS_REQUESTING="SET_IS_REQUESTING",e.SET_ERROR="SET_ERROR",e.UPDATE_JETPACK_CONNECTION="UPDATE_JETPACK_CONNECTION",e.UPDATE_JETPACK_CONNECT_URL="UPDATE_JETPACK_CONNECT_URL",e.UPDATE_JETPACK_CONNECTION_DATA="UPDATE_JETPACK_CONNECTION_DATA",e.SET_PAYPAL_ONBOARDING_STATUS="SET_PAYPAL_ONBOARDING_STATUS",e.SET_RECOMMENDED_PLUGINS="SET_RECOMMENDED_PLUGINS"}(Tt||(Tt={}));const St=te.controls&&te.controls.dispatch?te.controls.dispatch:re.dispatch,_t=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;class yt extends Error{constructor(e,t){super(e),this.data=t}}const ft=(e,t)=>"object"==typeof t&&null!==t&&e[0]in t,Rt=(e="install",t,r)=>(0,Ie.sprintf)((0,Ie._n)("Could not %(actionType)s %(pluginName)s plugin, %(error)s","Could not %(actionType)s the following plugins: %(pluginName)s with these Errors: %(error)s",Object.keys(t).length||1,"woocommerce"),{actionType:e,pluginName:t.join(", "),error:r});function gt(e,t=!1){return{type:Tt.UPDATE_ACTIVE_PLUGINS,active:e,replace:t}}function mt(e,t=!1){return{type:Tt.UPDATE_INSTALLED_PLUGINS,installed:e,replace:t}}function Ot(e,t){return{type:Tt.SET_IS_REQUESTING,selector:e,isRequesting:t}}function ht(e,t){return{type:Tt.SET_ERROR,selector:e,error:t}}function At(e){return{type:Tt.UPDATE_JETPACK_CONNECTION,jetpackConnection:e}}function It(e){return{type:Tt.UPDATE_JETPACK_CONNECTION_DATA,results:e}}function Ct(e,t){return{type:Tt.UPDATE_JETPACK_CONNECT_URL,jetpackConnectUrl:t,redirectUrl:e}}const Pt=e=>St("core/notices","createNotice","error",e);function Ut(e){return{type:Tt.SET_PAYPAL_ONBOARDING_STATUS,paypalOnboardingStatus:e}}function vt(e,t){return{type:Tt.SET_RECOMMENDED_PLUGINS,recommendedType:e,plugins:t}}function*Nt(e,t,r){let o;switch(o=ft(t,r)?Object.values(r).join(", \n"):We(r)||r instanceof Error?r.message:JSON.stringify(r),e){case"install":(0,pt.recordEvent)("install_plugins_error",{plugins:t.join(", "),message:o});break;case"activate":(0,pt.recordEvent)("activate_plugins_error",{plugins:t.join(", "),message:o})}throw new yt(Rt(e,t,o),r)}function*Dt(e,t=!1){var r,o;yield Ot("installPlugins",!0);try{const n=yield(0,re.apiFetch)({path:`${ve}/plugins/install`,method:"POST",data:{plugins:e.join(","),async:t}});if((null===(r=n.data.installed)||void 0===r?void 0:r.length)&&(yield mt(n.data.installed)),(null===(o=n.errors)||void 0===o?void 0:o.errors)&&Object.keys(n.errors.errors).length)throw n.errors.errors;return n}catch(t){yield ht("installPlugins",t),yield Nt("install",e,t)}finally{yield Ot("installPlugins",!1)}}function*wt(e){yield Ot("activatePlugins",!0);try{const t=yield(0,re.apiFetch)({path:`${ve}/plugins/activate`,method:"POST",data:{plugins:e.join(",")}});if(t.data.activated.length&&(yield gt(t.data.activated)),Object.keys(t.errors.errors).length)throw t.errors.errors;return t}catch(t){yield ht("activatePlugins",t),yield Nt("activate",e,t)}finally{yield Ot("activatePlugins",!1)}}function*bt(e){try{const t=yield St(et,"installPlugins",e),r=yield St(et,"activatePlugins",e);return{...r,data:{...r.data,...t.data}}}catch(e){throw e}}function*kt(e){const t=yield _t(et,"getJetpackConnectUrl",{redirect_url:e("admin.php?page=wc-admin")}),r=yield _t(et,"getPluginsError","getJetpackConnectUrl");if(r)throw new Error(r);return t}function*Gt(e,t){try{yield St(et,"installPlugins",["jetpack"]),yield St(et,"activatePlugins",["jetpack"]);const e=yield St(et,"connectToJetpack",t);window.location.href=e}catch(t){if(!(t instanceof Error))throw t;yield e(t.message)}}function*Mt(e,t,r){try{const e=yield St(et,"connectToJetpack",r);window.location.href=e}catch(r){if(!(r instanceof Error))throw r;yield t(r.message),window.location.href=e}}const Lt=["payments"];function*Ft(e){if(!Lt.includes(e))return[];const t=yield _t(et,"getRecommendedPlugins",e);let r;yield vt(e,[]);try{const e=ve+"/payment-gateway-suggestions/dismiss";r=yield(0,re.apiFetch)({path:e,method:"POST"})}catch(e){r=!1}return r||(yield vt(e,t)),r}const qt="wc/admin/options",jt=(e,t)=>e[t],xt=(e,t)=>e.requestingErrors[t]||!1,Qt=e=>e.isUpdating||!1,Kt=e=>e.updatingError||!1,Vt={RECEIVE_OPTIONS:"RECEIVE_OPTIONS",SET_IS_REQUESTING:"SET_IS_REQUESTING",SET_IS_UPDATING:"SET_IS_UPDATING",SET_REQUESTING_ERROR:"SET_REQUESTING_ERROR",SET_UPDATING_ERROR:"SET_UPDATING_ERROR"};function $t(e){return{type:Vt.RECEIVE_OPTIONS,options:e}}function Ht(e,t){return{type:Vt.SET_REQUESTING_ERROR,error:e,name:t}}function Yt(e){return{type:Vt.SET_UPDATING_ERROR,error:e}}function Wt(e){return{type:Vt.SET_IS_UPDATING,isUpdating:e}}function*Jt(e){yield Wt(!0),yield $t(e);try{const t=yield(0,re.apiFetch)({path:ve+"/options",method:"POST",data:e});if(yield Wt(!1),"object"!=typeof t)throw new Error(`Invalid update options response from server: ${t}`);return{success:!0,...t}}catch(e){if(yield Yt(e),"object"!=typeof e)throw new Error(`Unexpected error: ${e}`);return{success:!1,...e}}}const Bt=e=>({type:"BATCH_FETCH",optionName:e});let zt=[];const Zt={},Xt={...re.controls,BATCH_FETCH:async({optionName:e})=>(zt.push(e),await(async e=>new Promise((async(t,r)=>((e,t,r)=>{let o,n=null;const s=(...t)=>{n=t,o&&clearTimeout(o),o=setTimeout((()=>{o=null,n&&e(...n)}),100)};return s.flush=()=>{o&&n&&(e(...n),clearTimeout(o),o=null)},s})((()=>{if(Zt.hasOwnProperty(e))return Zt[e].then(t).catch(r);0===zt.length&&zt.push(e);const o=[...new Set(zt)],n=o.join(","),s=ie()({path:`${ve}/options?options=${n}`});o.forEach((async e=>{Zt[e]=s;try{await s}catch(e){}finally{delete Zt[e]}})),zt=[],s.then(t).catch(r)}))())))(e))};function*er(e){try{const t=yield Bt(e);yield $t(t)}catch(t){yield Ht(t,e)}}(0,te.registerStore)(qt,{reducer:(e={isUpdating:!1,requestingErrors:{}},t)=>{switch(t.type){case Vt.RECEIVE_OPTIONS:e={...e,...t.options};break;case Vt.SET_IS_UPDATING:e={...e,isUpdating:t.isUpdating};break;case Vt.SET_REQUESTING_ERROR:e={...e,requestingErrors:{[t.name]:t.error}};break;case Vt.SET_UPDATING_ERROR:e={...e,error:t.error,updatingError:t.error,isUpdating:!1}}return e},actions:c,controls:Xt,selectors:a,resolvers:u});const tr=qt,rr=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;function*or(){yield Ot("getActivePlugins",!0);try{yield Se("manage_woocommerce");const e=ve+"/plugins/active",t=yield(0,re.apiFetch)({path:e,method:"GET"});yield gt(t.plugins,!0)}catch(e){yield ht("getActivePlugins",e)}}function*nr(){yield Ot("getInstalledPlugins",!0);try{yield Se("manage_woocommerce");const e=ve+"/plugins/installed",t=yield(0,re.apiFetch)({path:e,method:"GET"});yield mt(t.plugins,!0)}catch(e){yield ht("getInstalledPlugins",e)}}function*sr(){yield Ot("isJetpackConnected",!0);try{const e=Pe+"/connection",t=yield(0,re.apiFetch)({path:e,method:"GET"});yield At(t.hasConnectedOwner)}catch(e){yield ht("isJetpackConnected",e)}yield Ot("isJetpackConnected",!1)}function*ir(){yield Ot("getJetpackConnectionData",!0);try{yield Se("manage_woocommerce");const e=Pe+"/connection/data",t=yield(0,re.apiFetch)({path:e,method:"GET"});yield It(t)}catch(e){yield ht("getJetpackConnectionData",e)}yield Ot("getJetpackConnectionData",!1)}function*ar(e){yield Ot("getJetpackConnectUrl",!0);try{const t=(0,ne.addQueryArgs)(ve+"/plugins/connect-jetpack",e),r=yield(0,re.apiFetch)({path:t,method:"GET"});yield Ct(e.redirect_url,r.connectAction)}catch(e){yield ht("getJetpackConnectUrl",e)}yield Ot("getJetpackConnectUrl",!1)}function*cr(){const e=yield rr(tr,"getOption","woocommerce-ppcp-settings"),t=e.merchant_email_production&&e.merchant_id_production&&e.client_id_production&&e.client_secret_production;yield Ut({production:{state:t?"onboarded":"unknown",onboarded:!!t}})}function*ur(){yield Ot("getPaypalOnboardingStatus",!0);const e=yield rr(et,"getPluginsError","getPaypalOnboardingStatus");if(e&&e.data&&404===e.data.status)yield cr();else try{const e=tt+"/onboarding/get-status",t=yield(0,re.apiFetch)({path:e,method:"GET"});yield Ut(t)}catch(e){yield cr(),yield ht("getPaypalOnboardingStatus",e)}yield Ot("getPaypalOnboardingStatus",!1)}const lr=["payments"];function*Er(e){if(!lr.includes(e))return[];yield Ot("getRecommendedPlugins",!0);try{const t=ve+"/payment-gateway-suggestions",r=yield(0,re.apiFetch)({path:t,method:"GET"});yield vt(e,r)}catch(e){yield ht("getRecommendedPlugins",e)}yield Ot("getRecommendedPlugins",!1)}(0,te.registerStore)(et,{reducer:(e={active:[],installed:[],requesting:{},errors:{},jetpackConnectUrls:{},recommended:{}},t)=>{if(t&&"type"in t)switch(t.type){case Tt.UPDATE_ACTIVE_PLUGINS:e={...e,active:t.replace?t.active:(0,Ce.concat)(e.active,t.active),requesting:{...e.requesting,getActivePlugins:!1,activatePlugins:!1},errors:{...e.errors,getActivePlugins:!1,activatePlugins:!1}};break;case Tt.UPDATE_INSTALLED_PLUGINS:e={...e,installed:t.replace?t.installed:(0,Ce.concat)(e.installed,t.installed),requesting:{...e.requesting,getInstalledPlugins:!1,installPlugins:!1},errors:{...e.errors,getInstalledPlugins:!1,installPlugin:!1}};break;case Tt.SET_IS_REQUESTING:e={...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}};break;case Tt.SET_ERROR:e={...e,requesting:{...e.requesting,[t.selector]:!1},errors:{...e.errors,[t.selector]:t.error}};break;case Tt.UPDATE_JETPACK_CONNECTION:e={...e,jetpackConnection:t.jetpackConnection};break;case Tt.UPDATE_JETPACK_CONNECTION_DATA:e={...e,jetpackConnectionData:t.results};break;case Tt.UPDATE_JETPACK_CONNECT_URL:e={...e,jetpackConnectUrls:{...e.jetpackConnectUrls,[t.redirectUrl]:t.jetpackConnectUrl}};break;case Tt.SET_PAYPAL_ONBOARDING_STATUS:e={...e,paypalOnboardingStatus:t.paypalOnboardingStatus};break;case Tt.SET_RECOMMENDED_PLUGINS:e={...e,recommended:{...e.recommended,[t.recommendedType]:t.plugins}}}return e},actions:i,controls:re.controls,selectors:s,resolvers:l});const dr=et,pr="wc/admin/onboarding";var Tr={};function Sr(e){return[e]}function _r(e,t,r){var o;if(e.length!==t.length)return!1;for(o=r;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}function yr(e,t){var r,o=t||Sr;function n(){r=new WeakMap}function s(){var t,n,s,i,a,c=arguments.length;for(i=new Array(c),s=0;s<c;s++)i[s]=arguments[s];for(t=function(e){var t,o,n,s,i,a=r,c=!0;for(t=0;t<e.length;t++){if(!(i=o=e[t])||"object"!=typeof i){c=!1;break}a.has(o)?a=a.get(o):(n=new WeakMap,a.set(o,n),a=n)}return a.has(Tr)||((s=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=c,a.set(Tr,s)),a.get(Tr)}(a=o.apply(null,i)),t.isUniqueByDependants||(t.lastDependants&&!_r(a,t.lastDependants,0)&&t.clear(),t.lastDependants=a),n=t.head;n;){if(_r(n.args,i,1))return n!==t.head&&(n.prev.next=n.next,n.next&&(n.next.prev=n.prev),n.next=t.head,n.prev=null,t.head.prev=n,t.head=n),n.val;n=n.next}return n={val:e.apply(null,i)},i[0]=null,n.args=i,t.head&&(t.head.prev=n,n.next=t.head),t.head=n,n.val}return s.getDependants=o,s.clear=n,n(),s}const fr=e=>e.freeExtensions||[],Rr=e=>e.profileItems||{},gr=yr((e=>Object.values(e.taskLists)),(e=>[e.taskLists])),mr=yr(((e,t)=>t.map((t=>e.taskLists[t]))),((e,t)=>t.map((t=>e.taskLists[t])))),Or=(e,t)=>e.taskLists[t],hr=(e,t)=>Object.keys(e.taskLists).reduce(((r,o)=>r||e.taskLists[o].tasks.find((e=>e.id===t))),void 0),Ar=e=>e.paymentMethods||[],Ir=(e,t)=>e.errors[t]||!1,Cr=(e,t)=>e.requesting[t]||!1,Pr=e=>e.emailPrefill||"",Ur=e=>e.productTypes||{},vr=(e,t)=>e.jetpackAuthUrls[t.redirectUrl]||"",Nr={SET_ERROR:"SET_ERROR",SET_IS_REQUESTING:"SET_IS_REQUESTING",SET_PROFILE_ITEMS:"SET_PROFILE_ITEMS",SET_EMAIL_PREFILL:"SET_EMAIL_PREFILL",GET_PAYMENT_METHODS_SUCCESS:"GET_PAYMENT_METHODS_SUCCESS",GET_PRODUCT_TYPES_SUCCESS:"GET_PRODUCT_TYPES_SUCCESS",GET_PRODUCT_TYPES_ERROR:"GET_PRODUCT_TYPES_ERROR",GET_FREE_EXTENSIONS_ERROR:"GET_FREE_EXTENSIONS_ERROR",GET_FREE_EXTENSIONS_SUCCESS:"GET_FREE_EXTENSIONS_SUCCESS",GET_TASK_LISTS_ERROR:"GET_TASK_LISTS_ERROR",GET_TASK_LISTS_SUCCESS:"GET_TASK_LISTS_SUCCESS",DISMISS_TASK_ERROR:"DISMISS_TASK_ERROR",DISMISS_TASK_REQUEST:"DISMISS_TASK_REQUEST",DISMISS_TASK_SUCCESS:"DISMISS_TASK_SUCCESS",UNDO_DISMISS_TASK_ERROR:"UNDO_DISMISS_TASK_ERROR",UNDO_DISMISS_TASK_REQUEST:"UNDO_DISMISS_TASK_REQUEST",UNDO_DISMISS_TASK_SUCCESS:"UNDO_DISMISS_TASK_SUCCESS",SNOOZE_TASK_ERROR:"SNOOZE_TASK_ERROR",SNOOZE_TASK_REQUEST:"SNOOZE_TASK_REQUEST",SNOOZE_TASK_SUCCESS:"SNOOZE_TASK_SUCCESS",UNDO_SNOOZE_TASK_ERROR:"UNDO_SNOOZE_TASK_ERROR",UNDO_SNOOZE_TASK_REQUEST:"UNDO_SNOOZE_TASK_REQUEST",UNDO_SNOOZE_TASK_SUCCESS:"UNDO_SNOOZE_TASK_SUCCESS",HIDE_TASK_LIST_ERROR:"HIDE_TASK_LIST_ERROR",HIDE_TASK_LIST_REQUEST:"HIDE_TASK_LIST_REQUEST",HIDE_TASK_LIST_SUCCESS:"HIDE_TASK_LIST_SUCCESS",UNHIDE_TASK_LIST_ERROR:"UNHIDE_TASK_LIST_ERROR",UNHIDE_TASK_LIST_REQUEST:"UNHIDE_TASK_LIST_REQUEST",UNHIDE_TASK_LIST_SUCCESS:"UNHIDE_TASK_LIST_SUCCESS",OPTIMISTICALLY_COMPLETE_TASK_REQUEST:"OPTIMISTICALLY_COMPLETE_TASK_REQUEST",ACTION_TASK_ERROR:"ACTION_TASK_ERROR",ACTION_TASK_REQUEST:"ACTION_TASK_REQUEST",ACTION_TASK_SUCCESS:"ACTION_TASK_SUCCESS",VISITED_TASK:"VISITED_TASK",KEEP_COMPLETED_TASKS_REQUEST:"KEEP_COMPLETED_TASKS_REQUEST",KEEP_COMPLETED_TASKS_SUCCESS:"KEEP_COMPLETED_TASKS_SUCCESS",SET_JETPACK_AUTH_URL:"SET_JETPACK_AUTH_URL",CORE_PROFILER_COMPLETED_REQUEST:"CORE_PROFILER_COMPLETED_REQUEST",CORE_PROFILER_COMPLETED_SUCCESS:"CORE_PROFILER_COMPLETED_SUCCESS",CORE_PROFILER_COMPLETED_ERROR:"CORE_PROFILER_COMPLETED_ERROR"},Dr=window.wp.hooks;var wr=r(67905);const br=window.wp.deprecated;var kr=r.n(br);class Gr{constructor(){this.filteredTasks=(0,Dr.applyFilters)("woocommerce_admin_onboarding_task_list",[],function(){const e=window.location&&window.location.search;if(!e)return{};const t=e.substring(1);return(0,wr.parse)(t)}()),this.filteredTasks&&this.filteredTasks.length>0&&kr()("woocommerce_admin_onboarding_task_list",{version:"2.10.0",alternative:"TaskLists::add_task()",plugin:"@woocommerce/data"}),this.tasks=this.filteredTasks.reduce(((e,t)=>({...e,[t.key]:t})),{})}hasDeprecatedTasks(){return this.filteredTasks.length>0}getPostData(){return this.hasDeprecatedTasks()?{extended_tasks:this.filteredTasks.map((e=>({title:e.title,content:e.content,additional_info:e.additionalInfo,time:e.time,level:e.level?parseInt(e.level,10):3,list_id:e.type||"extended",can_view:e.visible,id:e.key,is_snoozeable:e.allowRemindMeLater,is_dismissable:e.isDismissable,is_complete:e.completed})))}:null}mergeDeprecatedCallbackFunctions(e){if(this.filteredTasks.length>0)for(const t of e)t.tasks=t.tasks.map((e=>this.tasks&&this.tasks[e.id]?{...this.tasks[e.id],...e,isDeprecated:!0}:e));return e}static possiblyPruneTaskData(e,t){return e.time||e.title?e:t.reduce(((t,r)=>({...t,[r]:e[r]})),{id:e.id})}}function Mr(e){return{type:Nr.GET_FREE_EXTENSIONS_ERROR,error:e}}function Lr(e){return{type:Nr.GET_FREE_EXTENSIONS_SUCCESS,freeExtensions:e}}function Fr(e,t){return{type:Nr.SET_ERROR,selector:e,error:t}}function qr(e,t){return{type:Nr.SET_IS_REQUESTING,selector:e,isRequesting:t}}function jr(e,t=!1){return{type:Nr.SET_PROFILE_ITEMS,profileItems:e,replace:t}}function xr(e){return{type:Nr.GET_TASK_LISTS_ERROR,error:e}}function Qr(e){return{type:Nr.GET_TASK_LISTS_SUCCESS,taskLists:e}}function Kr(e,t){return{type:Nr.SNOOZE_TASK_ERROR,taskId:e,error:t}}function Vr(e){return{type:Nr.SNOOZE_TASK_REQUEST,taskId:e}}function $r(e){return{type:Nr.SNOOZE_TASK_SUCCESS,task:e}}function Hr(e,t){return{type:Nr.UNDO_SNOOZE_TASK_ERROR,taskId:e,error:t}}function Yr(e){return{type:Nr.UNDO_SNOOZE_TASK_REQUEST,taskId:e}}function Wr(e){return{type:Nr.UNDO_SNOOZE_TASK_SUCCESS,task:e}}function Jr(e,t){return{type:Nr.DISMISS_TASK_ERROR,taskId:e,error:t}}function Br(e){return{type:Nr.DISMISS_TASK_REQUEST,taskId:e}}function zr(e){return{type:Nr.DISMISS_TASK_SUCCESS,task:e}}function Zr(e,t){return{type:Nr.UNDO_DISMISS_TASK_ERROR,taskId:e,error:t}}function Xr(e){return{type:Nr.UNDO_DISMISS_TASK_REQUEST,taskId:e}}function eo(e){return{type:Nr.UNDO_DISMISS_TASK_SUCCESS,task:e}}function to(e,t){return{type:Nr.HIDE_TASK_LIST_ERROR,taskListId:e,error:t}}function ro(e){return{type:Nr.HIDE_TASK_LIST_REQUEST,taskListId:e}}function oo(e){return{type:Nr.HIDE_TASK_LIST_SUCCESS,taskList:e,taskListId:e.id}}function no(e,t){return{type:Nr.UNHIDE_TASK_LIST_ERROR,taskListId:e,error:t}}function so(e){return{type:Nr.UNHIDE_TASK_LIST_REQUEST,taskListId:e}}function io(e){return{type:Nr.UNHIDE_TASK_LIST_SUCCESS,taskList:e,taskListId:e.id}}function ao(e){return{type:Nr.OPTIMISTICALLY_COMPLETE_TASK_REQUEST,taskId:e}}function co(e,t){return{type:Nr.KEEP_COMPLETED_TASKS_SUCCESS,taskListId:e,keepCompletedTaskList:t}}function uo(e){return{type:Nr.VISITED_TASK,taskId:e}}function lo(e){return{type:Nr.GET_PAYMENT_METHODS_SUCCESS,paymentMethods:e}}function Eo(e){return{type:Nr.SET_EMAIL_PREFILL,emailPrefill:e}}function po(e,t){return{type:Nr.ACTION_TASK_ERROR,taskId:e,error:t}}function To(e){return{type:Nr.ACTION_TASK_REQUEST,taskId:e}}function So(e){return{type:Nr.ACTION_TASK_SUCCESS,task:e}}function _o(e){return{type:Nr.GET_PRODUCT_TYPES_SUCCESS,productTypes:e}}function yo(e){return{type:Nr.GET_PRODUCT_TYPES_ERROR,error:e}}function*fo(e){const t=yield te.controls.dispatch(qt,"updateOptions",{woocommerce_task_list_keep_completed:"yes"});t&&t.success&&(yield co(e,"yes"))}function*Ro(e){yield qr("updateProfileItems",!0),yield Fr("updateProfileItems",null);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/profile`,method:"POST",data:e});if(t&&"success"===t.status)return yield jr(e),yield qr("updateProfileItems",!1),t;throw new Error}catch(e){throw yield Fr("updateProfileItems",e),yield qr("updateProfileItems",!1),e}}function*go(e){yield Vr(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/snooze`,method:"POST"});yield $r(Gr.possiblyPruneTaskData(t,["isSnoozed","isDismissed","snoozedUntil"]))}catch(t){throw yield Kr(e,t),new Error}}function*mo(e){yield Yr(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/undo_snooze`,method:"POST"});yield Wr(Gr.possiblyPruneTaskData(t,["isSnoozed","isDismissed","snoozedUntil"]))}catch(t){throw yield Hr(e,t),new Error}}function*Oo(e){yield Br(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/dismiss`,method:"POST"});yield zr(Gr.possiblyPruneTaskData(t,["isDismissed","isSnoozed"]))}catch(t){throw yield Jr(e,t),new Error}}function*ho(e){yield Xr(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/undo_dismiss`,method:"POST"});yield eo(Gr.possiblyPruneTaskData(t,["isDismissed","isSnoozed"]))}catch(t){throw yield Zr(e,t),new Error}}function*Ao(e){yield ro(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/hide`,method:"POST"});yield oo(t)}catch(t){throw yield to(e,t),new Error}}function*Io(e){yield so(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/unhide`,method:"POST"});yield io(t)}catch(t){throw yield no(e,t),new Error}}function*Co(e){yield ao(e)}function*Po(e){yield To(e);try{const t=yield(0,re.apiFetch)({path:`${ve}/onboarding/tasks/${e}/action`,method:"POST"});yield So(Gr.possiblyPruneTaskData(t,["isActioned"]))}catch(t){throw yield po(e,t),new Error}}function*Uo(e){yield qr("installAndActivatePluginsAsync",!0);try{return yield(0,re.apiFetch)({path:`${ve}/onboarding/plugins/install-and-activate-async`,method:"POST",data:{plugins:e}})}catch(e){throw e}finally{yield qr("installAndActivatePluginsAsync",!1)}}function vo(e,t,r=""){return{type:Nr.SET_JETPACK_AUTH_URL,results:e,redirectUrl:t,from:r}}function No(e){return{type:Nr.CORE_PROFILER_COMPLETED_ERROR,error:e}}function Do(){return{type:Nr.CORE_PROFILER_COMPLETED_REQUEST}}function wo(){return{type:Nr.CORE_PROFILER_COMPLETED_SUCCESS}}function*bo(){yield Do();try{yield(0,re.apiFetch)({path:`${ve}/launch-your-store/initialize-coming-soon`,method:"POST"})}catch(e){throw yield No(e),e}finally{yield wo()}}const ko=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;function*Go(){try{const e=yield(0,re.apiFetch)({path:ve+"/onboarding/profile",method:"GET"});yield jr(e,!0)}catch(e){yield Fr("getProfileItems",e)}}function*Mo(){try{const e=yield(0,re.apiFetch)({path:ve+"/onboarding/profile/experimental_get_email_prefill",method:"GET"});yield Eo(e.email)}catch(e){yield Fr("getEmailPrefill",e)}}function*Lo(){const e=new Gr;try{yield Se("manage_woocommerce");const t=yield(0,re.apiFetch)({path:ve+"/onboarding/tasks",method:e.hasDeprecatedTasks()?"POST":"GET",data:e.getPostData()});e.mergeDeprecatedCallbackFunctions(t),yield Qr(t)}catch(e){yield xr(e)}}function*Fo(){yield ko(pr,"getTaskLists")}function*qo(){yield ko(pr,"getTaskLists")}function*jo(){yield ko(pr,"getTaskLists")}function*xo(e=!1){let t=ve+"/payment-gateway-suggestions";e&&(t+="?force_default_suggestions=true");try{const e=yield(0,re.apiFetch)({path:t,method:"GET"});yield lo(e)}catch(e){yield Fr("getPaymentGatewaySuggestions",e)}}function*Qo(){try{const e=yield(0,re.apiFetch)({path:ve+"/onboarding/free-extensions",method:"GET"});yield Lr(e)}catch(e){yield Mr(e)}}function*Ko(){try{const e=yield(0,re.apiFetch)({path:ve+"/onboarding/product-types",method:"GET"});yield _o(e)}catch(e){yield yo(e)}}function*Vo(e){var t;try{let r=ve+"/onboarding/plugins/jetpack-authorization-url?redirect_url="+encodeURIComponent(e.redirectUrl);e.from&&(r+="&from="+e.from);const o=yield(0,re.apiFetch)({path:r,method:"GET"});yield vo(o,e.redirectUrl,null!==(t=e.from)&&void 0!==t?t:"")}catch(e){yield Fr("getJetpackAuthUrl",e)}}const $o={errors:{},freeExtensions:[],profileItems:{business_extensions:null,completed:null,industry:null,number_employees:null,other_platform:null,other_platform_name:null,product_count:null,product_types:null,revenue:null,selling_venues:null,setup_client:null,skipped:null,theme:null,wccom_connected:null,is_agree_marketing:null,store_email:null,is_store_country_set:null},emailPrefill:"",paymentMethods:[],productTypes:{},requesting:{},taskLists:{},jetpackAuthUrls:{}},Ho=(e,t)=>Object.keys(e).reduce(((r,o)=>({...r,[o]:{...e[o],tasks:e[o].tasks.map((e=>t.id===e.id?{...e,...t}:e))}})),{...e});(0,te.registerStore)(pr,{reducer:(e=$o,t)=>{switch(t.type){case Nr.SET_PROFILE_ITEMS:return{...e,profileItems:t.replace?t.profileItems:{...e.profileItems,...t.profileItems}};case Nr.SET_EMAIL_PREFILL:return{...e,emailPrefill:t.emailPrefill};case Nr.SET_ERROR:return{...e,errors:{...e.errors,[t.selector]:t.error}};case Nr.SET_IS_REQUESTING:return{...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}};case Nr.GET_PAYMENT_METHODS_SUCCESS:return{...e,paymentMethods:t.paymentMethods};case Nr.GET_PRODUCT_TYPES_SUCCESS:return{...e,productTypes:t.productTypes};case Nr.GET_PRODUCT_TYPES_ERROR:return{...e,errors:{...e.errors,productTypes:t.error}};case Nr.GET_FREE_EXTENSIONS_ERROR:return{...e,errors:{...e.errors,getFreeExtensions:t.error}};case Nr.GET_FREE_EXTENSIONS_SUCCESS:return{...e,freeExtensions:t.freeExtensions};case Nr.GET_TASK_LISTS_ERROR:return{...e,errors:{...e.errors,getTaskLists:t.error}};case Nr.GET_TASK_LISTS_SUCCESS:return{...e,taskLists:t.taskLists.reduce(((e,t)=>({...e,[t.id]:t})),e.taskLists||{})};case Nr.DISMISS_TASK_ERROR:return{...e,errors:{...e.errors,dismissTask:t.error},taskLists:Ho(e.taskLists,{id:t.taskId,isDismissed:!1})};case Nr.DISMISS_TASK_REQUEST:return{...e,requesting:{...e.requesting,dismissTask:!0},taskLists:Ho(e.taskLists,{id:t.taskId,isDismissed:!0})};case Nr.DISMISS_TASK_SUCCESS:return{...e,requesting:{...e.requesting,dismissTask:!1},taskLists:Ho(e.taskLists,t.task)};case Nr.UNDO_DISMISS_TASK_ERROR:return{...e,errors:{...e.errors,undoDismissTask:t.error},taskLists:Ho(e.taskLists,{id:t.taskId,isDismissed:!0})};case Nr.UNDO_DISMISS_TASK_REQUEST:return{...e,requesting:{...e.requesting,undoDismissTask:!0},taskLists:Ho(e.taskLists,{id:t.taskId,isDismissed:!1})};case Nr.UNDO_DISMISS_TASK_SUCCESS:return{...e,requesting:{...e.requesting,undoDismissTask:!1},taskLists:Ho(e.taskLists,t.task)};case Nr.SNOOZE_TASK_ERROR:return{...e,errors:{...e.errors,snoozeTask:t.error},taskLists:Ho(e.taskLists,{id:t.taskId,isSnoozed:!1})};case Nr.SNOOZE_TASK_REQUEST:return{...e,requesting:{...e.requesting,snoozeTask:!0},taskLists:Ho(e.taskLists,{id:t.taskId,isSnoozed:!0})};case Nr.SNOOZE_TASK_SUCCESS:return{...e,requesting:{...e.requesting,snoozeTask:!1},taskLists:Ho(e.taskLists,t.task)};case Nr.UNDO_SNOOZE_TASK_ERROR:return{...e,errors:{...e.errors,undoSnoozeTask:t.error},taskLists:Ho(e.taskLists,{id:t.taskId,isSnoozed:!0})};case Nr.UNDO_SNOOZE_TASK_REQUEST:return{...e,requesting:{...e.requesting,undoSnoozeTask:!0},taskLists:Ho(e.taskLists,{id:t.taskId,isSnoozed:!1})};case Nr.UNDO_SNOOZE_TASK_SUCCESS:return{...e,requesting:{...e.requesting,undoSnoozeTask:!1},taskLists:Ho(e.taskLists,t.task)};case Nr.HIDE_TASK_LIST_ERROR:return{...e,errors:{...e.errors,hideTaskList:t.error},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!1,isVisible:!0}}};case Nr.HIDE_TASK_LIST_REQUEST:return{...e,requesting:{...e.requesting,hideTaskList:!0},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!0,isVisible:!1}}};case Nr.HIDE_TASK_LIST_SUCCESS:return{...e,requesting:{...e.requesting,hideTaskList:!1},taskLists:{...e.taskLists,[t.taskListId]:t.taskList}};case Nr.UNHIDE_TASK_LIST_ERROR:return{...e,errors:{...e.errors,unhideTaskList:t.error},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!0,isVisible:!1}}};case Nr.UNHIDE_TASK_LIST_REQUEST:return{...e,requesting:{...e.requesting,unhideTaskList:!0},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!1,isVisible:!0}}};case Nr.UNHIDE_TASK_LIST_SUCCESS:return{...e,requesting:{...e.requesting,unhideTaskList:!1},taskLists:{...e.taskLists,[t.taskListId]:t.taskList}};case Nr.KEEP_COMPLETED_TASKS_SUCCESS:return{...e,taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],keepCompletedTaskList:t.keepCompletedTaskList}}};case Nr.OPTIMISTICALLY_COMPLETE_TASK_REQUEST:return{...e,taskLists:Ho(e.taskLists,{id:t.taskId,isComplete:!0})};case Nr.VISITED_TASK:return{...e,taskLists:Ho(e.taskLists,{id:t.taskId,isVisited:!0})};case Nr.ACTION_TASK_ERROR:return{...e,errors:{...e.errors,actionTask:t.error},taskLists:Ho(e.taskLists,{id:t.taskId,isActioned:!1})};case Nr.ACTION_TASK_REQUEST:return{...e,requesting:{...e.requesting,actionTask:!0},taskLists:Ho(e.taskLists,{id:t.taskId,isActioned:!0})};case Nr.ACTION_TASK_SUCCESS:return{...e,requesting:{...e.requesting,actionTask:!1},taskLists:Ho(e.taskLists,t.task)};case Nr.SET_JETPACK_AUTH_URL:return{...e,jetpackAuthUrls:{...e.jetpackAuthUrls,[t.redirectUrl]:t.results}};case Nr.CORE_PROFILER_COMPLETED_REQUEST:return{...e,requesting:{...e.requesting,coreProfilerCompleted:!0}};case Nr.CORE_PROFILER_COMPLETED_SUCCESS:return{...e,requesting:{...e.requesting,coreProfilerCompleted:!1}};case Nr.CORE_PROFILER_COMPLETED_ERROR:return{...e,errors:{...e.errors,coreProfilerCompleted:t.error},requesting:{...e.requesting,coreProfilerCompleted:!1}};default:return e}},actions:d,controls:re.controls,selectors:E,resolvers:p});const Yo=pr,Wo="wc/admin/reviews",Jo=(e,t)=>{const r=JSON.stringify(t);return(e.reviews[r]&&e.reviews[r].data||[]).map((t=>e.data[t]))},Bo=(e,t)=>{const r=JSON.stringify(t);return e.reviews[r]&&e.reviews[r].totalCount},zo=(e,t)=>{const r=JSON.stringify(t);return e.errors[r]},Zo={UPDATE_REVIEWS:"UPDATE_REVIEWS",SET_REVIEW:"SET_REVIEW",SET_ERROR:"SET_ERROR",SET_REVIEW_IS_UPDATING:"SET_REVIEW_IS_UPDATING"};function Xo(e,t){return{type:Zo.SET_REVIEW_IS_UPDATING,reviewId:e,isUpdating:t}}function en(e,t){return{type:Zo.SET_REVIEW,reviewId:e,reviewData:t}}function tn(e,t){return{type:Zo.SET_ERROR,query:e,error:t}}function rn(e,t,r){return{type:Zo.UPDATE_REVIEWS,reviews:t,query:e,totalCount:r}}function*on(e,t,r){yield Xo(e,!0);try{const o=(0,ne.addQueryArgs)(`${Ue}/products/reviews/${e}`,r||{}),n=yield(0,re.apiFetch)({path:o,method:"PUT",data:t});yield en(e,n),yield Xo(e,!1)}catch(t){throw yield tn("updateReview",t),yield Xo(e,!1),new Error}}function*nn(e){yield Xo(e,!0);try{const t=`${Ue}/products/reviews/${e}`,r=yield(0,re.apiFetch)({path:t,method:"DELETE"});return yield en(e,r),yield Xo(e,!1),r}catch(t){throw yield tn("deleteReview",t),yield Xo(e,!1),new Error}}function*sn(e){try{const t=(0,ne.addQueryArgs)(`${Ue}/products/reviews`,e),r=yield ae({path:t,method:"GET"}),o=r.headers.get("x-wp-total");if(void 0===o)throw new Error("Malformed response from server. 'x-wp-total' header is missing when retrieving ./products/reviews.");const n=parseInt(o,10);yield rn(e,r.data,n)}catch(t){yield tn(JSON.stringify(e),t)}}function*an(e){yield sn(e)}const cn={reviews:{},errors:{},data:{}};(0,te.registerStore)(Wo,{reducer:(e=cn,t)=>{switch(t.type){case Zo.UPDATE_REVIEWS:const r=[],o=t.reviews.reduce(((t,o)=>(r.push(o.id),t[o.id]={...e.data[o.id]||{},...o},t)),{});return{...e,reviews:{...e.reviews,[JSON.stringify(t.query)]:{data:r,totalCount:t.totalCount}},data:{...e.data,...o}};case Zo.SET_REVIEW:return{...e,data:{...e.data,[t.reviewId]:t.reviewData}};case Zo.SET_ERROR:return{...e,errors:{...e.errors,[t.query]:t.error}};case Zo.SET_REVIEW_IS_UPDATING:return{...e,data:{...e.data,[t.reviewId]:{...e.data[t.reviewId],isUpdating:t.isUpdating}}};default:return e}},actions:S,controls:ce,selectors:T,resolvers:_});const un=Wo,ln="wc/admin/notes",En=yr(((e,t)=>(e.noteQueries[JSON.stringify(t)]||[]).map((t=>e.notes[t]))),((e,t)=>[e.noteQueries[JSON.stringify(t)],e.notes])),dn=(e,t)=>e.errors[t]||!1,pn=(e,t)=>e.requesting[t]||!1,Tn={SET_ERROR:"SET_ERROR",SET_NOTE:"SET_NOTE",SET_NOTE_IS_UPDATING:"SET_NOTE_IS_UPDATING",SET_NOTES:"SET_NOTES",SET_NOTES_QUERY:"SET_NOTES_QUERY",SET_IS_REQUESTING:"SET_IS_REQUESTING"};function Sn(e,t){return{type:Tn.SET_NOTE,noteId:e,noteFields:t}}function _n(e,t){return{type:Tn.SET_NOTE_IS_UPDATING,noteId:e,isUpdating:t}}function yn(e){return{type:Tn.SET_NOTES,notes:e}}function fn(e,t){return{type:Tn.SET_NOTES_QUERY,query:e,noteIds:t}}function Rn(e,t){return{type:Tn.SET_ERROR,error:t,selector:e}}function gn(e,t){return{type:Tn.SET_IS_REQUESTING,selector:e,isRequesting:t}}function*mn(e,t){yield gn("updateNote",!0),yield _n(e,!0);try{const r=`${Ue}/admin/notes/${e}`,o=yield(0,re.apiFetch)({path:r,method:"PUT",data:t});yield Sn(e,o),yield gn("updateNote",!1),yield _n(e,!1)}catch(t){throw yield Rn("updateNote",t),yield gn("updateNote",!1),yield _n(e,!1),new Error}}function*On(e,t){yield gn("triggerNoteAction",!0);const r=`${Ue}/admin/notes/${e}/action/${t}`;try{const t=yield(0,re.apiFetch)({path:r,method:"POST"});yield mn(e,t),yield gn("triggerNoteAction",!1)}catch(e){throw yield Rn("triggerNoteAction",e),yield gn("triggerNoteAction",!1),new Error}}function*hn(e){yield gn("removeNote",!0),yield _n(e,!0);try{const t=`${Ue}/admin/notes/delete/${e}`,r=yield(0,re.apiFetch)({path:t,method:"DELETE"});return yield Sn(e,r),yield gn("removeNote",!1),r}catch(t){throw yield Rn("removeNote",t),yield gn("removeNote",!1),yield _n(e,!1),new Error}}function*An(e={}){yield gn("removeAllNotes",!0);try{const t=(0,ne.addQueryArgs)(`${Ue}/admin/notes/delete/all`,e),r=yield(0,re.apiFetch)({path:t,method:"DELETE"});return yield yn(r),yield gn("removeAllNotes",!1),r}catch(e){throw yield Rn("removeAllNotes",e),yield gn("removeAllNotes",!1),new Error}}function*In(e,t){yield gn("batchUpdateNotes",!0);try{const r=`${Ue}/admin/notes/update`,o=yield(0,re.apiFetch)({path:r,method:"PUT",data:{noteIds:e,...t}});yield yn(o),yield gn("batchUpdateNotes",!1)}catch(e){throw yield Rn("updateNote",e),yield gn("batchUpdateNotes",!1),new Error}}function*Cn(e={}){const t=(0,ne.addQueryArgs)(`${Ue}/admin/notes`,e);try{yield Se("manage_woocommerce");const r=yield(0,re.apiFetch)({path:t});yield yn(r),yield fn(e,r.map((e=>e.id)))}catch(e){yield Rn("getNotes",e)}}(0,te.registerStore)(ln,{reducer:(e={errors:{},noteQueries:{},notes:{},requesting:{}},t)=>{switch(t.type){case Tn.SET_NOTES:e={...e,notes:{...e.notes,...t.notes.reduce(((e,t)=>(e[t.id]=t,e)),{})}};break;case Tn.SET_NOTES_QUERY:e={...e,noteQueries:{...e.noteQueries,[JSON.stringify(t.query)]:t.noteIds}};break;case Tn.SET_ERROR:e={...e,errors:{...e.errors,[t.selector]:t.error}};break;case Tn.SET_NOTE:e={...e,notes:{...e.notes,[t.noteId]:t.noteFields}};break;case Tn.SET_NOTE_IS_UPDATING:e={...e,notes:{...e.notes,[t.noteId]:{...e.notes[t.noteId],isUpdating:t.isUpdating}}};break;case Tn.SET_IS_REQUESTING:e={...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}}}return e},actions:f,controls:re.controls,selectors:y,resolvers:R});const Pn=ln,Un="wc/admin/reports",vn={},Nn=(e,t,r)=>{const o=de(t,r);return e.itemErrors[o]||!1},Dn=(e,t,r)=>{const o=de(t,r);return e.items[o]||vn},wn=(e,t,r)=>{const o=de(t,r);return e.stats[o]||vn},bn=(e,t,r)=>{const o=de(t,r);return e.statErrors[o]||!1},kn={SET_ITEM_ERROR:"SET_ITEM_ERROR",SET_STAT_ERROR:"SET_STAT_ERROR",SET_REPORT_ITEMS:"SET_REPORT_ITEMS",SET_REPORT_STATS:"SET_REPORT_STATS"};function Gn(e,t,r){const o=de(e,t);return{type:kn.SET_ITEM_ERROR,resourceName:o,error:r}}function Mn(e,t,r){const o=de(e,t);return{type:kn.SET_REPORT_ITEMS,resourceName:o,items:r}}function Ln(e,t,r){const o=de(e,t);return{type:kn.SET_REPORT_STATS,resourceName:o,stats:r}}function Fn(e,t,r){const o=de(e,t);return{type:kn.SET_STAT_ERROR,resourceName:o,error:r}}const qn=(e,t,r)=>r.map((r=>{const o=t.headers.get(r);if(void 0===o)throw new Error(`Malformed response from server. '${r}' header is missing when retrieving ./report/${e}.`);return parseInt(o,10)}));function*jn(e,t){const r={parse:!1,path:(0,ne.addQueryArgs)(`${Ue}/reports/${e}`,t)};if("performance-indicators"!==e||t.stats)try{const o=yield ae(r),n=o.data,[s,i]=qn(e,o,["x-wp-total","x-wp-totalpages"]);yield Mn(e,t,{data:n,totalResults:s,totalPages:i})}catch(r){yield Gn(e,t,r)}else yield Mn(e,t,{data:[],totalResults:0,totalPages:0})}function*xn(e,t){const r={parse:!1,path:(0,ne.addQueryArgs)(`${Ue}/reports/${e}/stats`,t)};try{const o=yield ae(r),n=o.data,[s,i]=qn(e,o,["x-wp-total","x-wp-totalpages"]);yield Ln(e,t,{data:n,totalResults:s,totalPages:i})}catch(r){yield Fn(e,t,r)}}const Qn={itemErrors:{},items:{},statErrors:{},stats:{}};(0,te.registerStore)(Un,{reducer:(e=Qn,t)=>{switch(t.type){case kn.SET_REPORT_ITEMS:return{...e,items:{...e.items,[t.resourceName]:t.items}};case kn.SET_REPORT_STATS:return{...e,stats:{...e.stats,[t.resourceName]:t.stats}};case kn.SET_ITEM_ERROR:return{...e,itemErrors:{...e.itemErrors,[t.resourceName]:t.error}};case kn.SET_STAT_ERROR:return{...e,statErrors:{...e.statErrors,[t.resourceName]:t.error}};default:return e}},actions:m,controls:ce,selectors:g,resolvers:O});const Kn=Un,Vn="wc/admin/countries",$n=e=>e.locales,Hn=(e,t)=>{const r=t.split(":")[0];return e.locales[r]},Yn=e=>e.countries,Wn=(e,t)=>e.countries.find((e=>e.code===t)),Jn=e=>e.geolocation;var Bn,zn;(zn=Bn||(Bn={})).GET_LOCALES_ERROR="GET_LOCALES_ERROR",zn.GET_LOCALES_SUCCESS="GET_LOCALES_SUCCESS",zn.GET_COUNTRIES_ERROR="GET_COUNTRIES_ERROR",zn.GET_COUNTRIES_SUCCESS="GET_COUNTRIES_SUCCESS",zn.GEOLOCATION_SUCCESS="GEOLOCATION_SUCCESS",zn.GEOLOCATION_ERROR="GEOLOCATION_ERROR";const Zn=Bn;function Xn(e){return{type:Zn.GET_LOCALES_SUCCESS,locales:e}}function es(e){return{type:Zn.GET_LOCALES_ERROR,error:e}}function ts(e){return{type:Zn.GET_COUNTRIES_SUCCESS,countries:e}}function rs(e){return{type:Zn.GET_COUNTRIES_ERROR,error:e}}function os(e){return{type:Zn.GEOLOCATION_SUCCESS,geolocation:e}}function ns(e){return{type:Zn.GEOLOCATION_ERROR,error:e}}const ss=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;function*is(){yield ss(Vn,"getLocales")}function*as(){try{const e=Ue+"/data/countries/locales";return Xn(yield(0,re.apiFetch)({path:e,method:"GET"}))}catch(e){return es(e)}}function*cs(){yield ss(Vn,"getCountries")}function*us(){try{const e=Ue+"/data/countries";return ts(yield(0,re.apiFetch)({path:e,method:"GET"}))}catch(e){return rs(e)}}const ls=()=>async({dispatch:e})=>{try{const t=`https://public-api.wordpress.com/geo/?v=${(new Date).getTime()}`,r=await fetch(t,{method:"GET"}),o=await r.json();e.geolocationSuccess(o)}catch(t){e.geolocationError(t)}};(0,te.registerStore)(Vn,{reducer:(e={errors:{},locales:{},countries:[],geolocation:void 0},t)=>{switch(t.type){case Zn.GET_LOCALES_SUCCESS:e={...e,locales:t.locales};break;case Zn.GET_LOCALES_ERROR:e={...e,errors:{...e.errors,locales:t.error}};break;case Zn.GET_COUNTRIES_SUCCESS:e={...e,countries:t.countries};break;case Zn.GET_COUNTRIES_ERROR:e={...e,errors:{...e.errors,countries:t.error}};break;case Zn.GEOLOCATION_SUCCESS:e={...e,geolocation:t.geolocation};break;case Zn.GEOLOCATION_ERROR:e={...e,errors:{...e.errors,geolocation:t.error}}}return e},actions:A,controls:re.controls,selectors:h,resolvers:I});const Es=Vn,ds="woocommerce-navigation",ps=e=>(0,Dr.applyFilters)("woocommerce_navigation_menu_items",e.menuItems),Ts=e=>e.favorites||[],Ss=(e,t)=>e.requesting[t]||!1,_s=e=>e.persistedQuery||{},ys=window.wc.navigation,fs={ADD_MENU_ITEMS:"ADD_MENU_ITEMS",SET_MENU_ITEMS:"SET_MENU_ITEMS",ON_HISTORY_CHANGE:"ON_HISTORY_CHANGE",ADD_FAVORITE_FAILURE:"ADD_FAVORITE_FAILURE",ADD_FAVORITE_REQUEST:"ADD_FAVORITE_REQUEST",ADD_FAVORITE_SUCCESS:"ADD_FAVORITE_SUCCESS",GET_FAVORITES_FAILURE:"GET_FAVORITES_FAILURE",GET_FAVORITES_REQUEST:"GET_FAVORITES_REQUEST",GET_FAVORITES_SUCCESS:"GET_FAVORITES_SUCCESS",REMOVE_FAVORITE_FAILURE:"REMOVE_FAVORITE_FAILURE",REMOVE_FAVORITE_REQUEST:"REMOVE_FAVORITE_REQUEST",REMOVE_FAVORITE_SUCCESS:"REMOVE_FAVORITE_SUCCESS"};function Rs(e){return{type:fs.SET_MENU_ITEMS,menuItems:e}}function gs(e){return{type:fs.ADD_MENU_ITEMS,menuItems:e}}function ms(e){return{type:fs.GET_FAVORITES_FAILURE,error:e}}function Os(e){return{type:fs.GET_FAVORITES_REQUEST,favorites:e}}function hs(e){return{type:fs.GET_FAVORITES_SUCCESS,favorites:e}}function As(e){return{type:fs.ADD_FAVORITE_REQUEST,favorite:e}}function Is(e,t){return{type:fs.ADD_FAVORITE_FAILURE,favorite:e,error:t}}function Cs(e){return{type:fs.ADD_FAVORITE_SUCCESS,favorite:e}}function Ps(e){return{type:fs.REMOVE_FAVORITE_REQUEST,favorite:e}}function Us(e,t){return{type:fs.REMOVE_FAVORITE_FAILURE,favorite:e,error:t}}function vs(e){return{type:fs.REMOVE_FAVORITE_SUCCESS,favorite:e}}function*Ns(){const e=(0,ys.getPersistedQuery)();if(!Object.keys(e).length)return null;yield{type:fs.ON_HISTORY_CHANGE,persistedQuery:e}}function*Ds(){yield Ns()}function*ws(e){yield As(e);try{const t=yield ie()({path:`${ve}/navigation/favorites/me`,method:"POST",data:{item_id:e}});if(t)return yield Cs(e),t;throw new Error}catch(t){throw yield Is(e,t),new Error}}function*bs(e){yield Ps(e);try{const t=yield ie()({path:`${ve}/navigation/favorites/me`,method:"DELETE",data:{item_id:e}});if(t)return yield vs(e),t;throw new Error}catch(t){throw yield Us(e,t),new Error}}function*ks(){yield Os();try{const e=yield(0,re.apiFetch)({path:`${ve}/navigation/favorites/me`});if(e)return void(yield hs(e));throw new Error}catch(e){throw yield ms(e),new Error}}(0,te.registerStore)(ds,{reducer:(e={error:null,menuItems:[],favorites:[],requesting:{},persistedQuery:{}},t)=>{switch(t.type){case fs.SET_MENU_ITEMS:e={...e,menuItems:t.menuItems};break;case fs.ADD_MENU_ITEMS:e={...e,menuItems:[...e.menuItems,...t.menuItems]};break;case fs.ON_HISTORY_CHANGE:e={...e,persistedQuery:t.persistedQuery};break;case fs.GET_FAVORITES_FAILURE:e={...e,requesting:{...e.requesting,getFavorites:!1}};break;case fs.GET_FAVORITES_REQUEST:e={...e,requesting:{...e.requesting,getFavorites:!0}};break;case fs.GET_FAVORITES_SUCCESS:e={...e,favorites:t.favorites,requesting:{...e.requesting,getFavorites:!1}};break;case fs.ADD_FAVORITE_FAILURE:e={...e,error:t.error,requesting:{...e.requesting,addFavorite:!1}};break;case fs.ADD_FAVORITE_REQUEST:e={...e,requesting:{...e.requesting,addFavorite:!0}};break;case fs.ADD_FAVORITE_SUCCESS:const r=e.favorites.includes(t.favorite)?e.favorites:[...e.favorites,t.favorite];e={...e,favorites:r,menuItems:e.menuItems.map((e=>e.id===t.favorite?{...e,menuId:"favorites"}:e)),requesting:{...e.requesting,addFavorite:!1}};break;case fs.REMOVE_FAVORITE_FAILURE:e={...e,requesting:{...e.requesting,error:t.error,removeFavorite:!1}};break;case fs.REMOVE_FAVORITE_REQUEST:e={...e,requesting:{...e.requesting,removeFavorite:!0}};break;case fs.REMOVE_FAVORITE_SUCCESS:const o=e.favorites.filter((e=>e!==t.favorite));e={...e,favorites:o,menuItems:e.menuItems.map((e=>e.id===t.favorite?{...e,menuId:"plugins"}:e)),requesting:{...e.requesting,removeFavorite:!1}}}return e},actions:P,controls:re.controls,resolvers:U,selectors:C}),(async()=>{const{onLoad:e,onHistoryChange:t}=(0,te.dispatch)(ds);await e(),(0,ys.addHistoryListener)((async()=>{setTimeout((async()=>{await t()}),0)}))})();const Gs=ds,Ms="wc/admin/items",Ls=window.wc.date;function Fs(e){const t="leaderboards",{per_page:r,persisted_query:o,query:n,select:s,filterQuery:i}=e,{getItems:a,getItemsError:c,isResolving:u}=s(Ms),l={isRequesting:!1,isError:!1,rows:[]},E=(0,Ls.getCurrentDates)(n,e.defaultDateRange),d={...i,after:(0,Ls.appendTimestamp)(E.primary.after,"start"),before:(0,Ls.appendTimestamp)(E.primary.before,"end"),per_page:r,persisted_query:JSON.stringify(o)},p=a(t,d);if(u("getItems",[t,d]))return{...l,isRequesting:!0};if(c(t,d))return{...l,isError:!0};const T=p.get(e.id);return{...l,rows:null==T?void 0:T.rows}}function qs(e,t,r,o={}){const{getItems:n,getItemsError:s,isResolving:i}=e,a={};let c=!1,u=!1;return r.forEach((e=>{const r={search:e,per_page:10,...o};n(t,r).forEach(((e,t)=>{a[t]=e})),i("getItems",[t,r])&&(c=!0),s(t,r)&&(u=!0)})),{items:a,isRequesting:c,isError:u}}function js(e,t){const{_fields:r,page:o,per_page:n,...s}=t;return de("total-"+e,{...s})}const xs=yr(((e,t,r,o=new Map)=>{const n=de(t,r);let s;return e.items[n]&&"object"==typeof e.items[n]&&(s=e.items[n].data),s?s.reduce(((r,o)=>{var n;return r.set(o,null===(n=e.data[t])||void 0===n?void 0:n[o]),r}),new Map):o}),((e,t,r)=>{const o=de(t,r);return[e.items[o]]})),Qs=(e,t,r,o=0)=>{const n=js(t,r);return e.items.hasOwnProperty(n)?e.items[n]:o},Ks=(e,t,r)=>{const o=de(t,r);return e.errors[o]},Vs={SET_ITEM:"SET_ITEM",SET_ITEMS:"SET_ITEMS",SET_ITEMS_TOTAL_COUNT:"SET_ITEMS_TOTAL_COUNT",SET_ERROR:"SET_ERROR"};function $s(e,t,r){return{type:Vs.SET_ITEM,id:t,item:r,itemType:e}}function Hs(e,t,r,o){return{type:Vs.SET_ITEMS,items:r,itemType:e,query:t,totalCount:o}}function Ys(e,t,r){return{type:Vs.SET_ITEMS_TOTAL_COUNT,itemType:e,query:t,totalCount:r}}function Ws(e,t,r){return{type:Vs.SET_ERROR,itemType:e,query:t,error:r}}function*Js(e,t){const r={...e,stock_quantity:t},{id:o,parent_id:n,type:s}=r;yield $s("products",o,r);let i=Ue;i+="variation"===s?`/products/${n}/variations/${o}`:`/products/${o}`;try{return yield(0,re.apiFetch)({path:i,method:"PUT",data:r}),!0}catch(t){return yield $s("products",o,e),yield Ws("products",{id:o},t),!1}}function*Bs(e,t){try{const r=(0,ne.addQueryArgs)(`${ve}/onboarding/tasks/create_product_from_template`,t||{}),o=yield(0,re.apiFetch)({path:r,method:"POST",data:e});return yield $s("products",o.id,o),o}catch(e){throw yield Ws("createProductFromTemplate",t,e),e}}function*zs(e,t){try{const r="categories"===e?"products/categories":e,{items:o,totalCount:n}=yield Te(`${Ue}/${r}`,t);yield Ys(e,t,n),yield Hs(e,t,o)}catch(r){yield Ws(e,t,r)}}function*Zs(e,t){try{const r={...t,page:1,per_page:1},o="categories"===e?"products/categories":e,{totalCount:n}=yield Te(`${Ue}/${o}`,r);yield Ys(e,t,n)}catch(r){yield Ws(e,t,r)}}function*Xs(e,t){yield Zs(e,t)}const ei={items:{},errors:{},data:{}};(0,te.registerStore)(Ms,{reducer:(e=ei,t)=>{switch(t.type){case Vs.SET_ITEM:const r=e.data[t.itemType]||{};return{...e,data:{...e.data,[t.itemType]:{...r,[t.id]:{...r[t.id]||{},...t.item}}}};case Vs.SET_ITEMS:const o=[],n=t.items.reduce(((e,t)=>(o.push(t.id),e[t.id]=t,e)),{}),s=de(t.itemType,t.query);return{...e,items:{...e.items,[s]:{data:o}},data:{...e.data,[t.itemType]:{...e.data[t.itemType],...n}}};case Vs.SET_ITEMS_TOTAL_COUNT:const i=js(t.itemType,t.query);return{...e,items:{...e.items,[i]:t.totalCount}};case Vs.SET_ERROR:return{...e,errors:{...e.errors,[de(t.itemType,t.query)]:t.error}};default:return e}},actions:N,controls:ce,selectors:v,resolvers:D});const ti=Ms;var ri;!function(e){e.GET_PAYMENT_GATEWAYS_REQUEST="GET_PAYMENT_GATEWAYS_REQUEST",e.GET_PAYMENT_GATEWAYS_SUCCESS="GET_PAYMENT_GATEWAYS_SUCCESS",e.GET_PAYMENT_GATEWAYS_ERROR="GET_PAYMENT_GATEWAYS_ERROR",e.UPDATE_PAYMENT_GATEWAY_REQUEST="UPDATE_PAYMENT_GATEWAY_REQUEST",e.UPDATE_PAYMENT_GATEWAY_SUCCESS="UPDATE_PAYMENT_GATEWAY_SUCCESS",e.UPDATE_PAYMENT_GATEWAY_ERROR="UPDATE_PAYMENT_GATEWAY_ERROR",e.GET_PAYMENT_GATEWAY_REQUEST="GET_PAYMENT_GATEWAY_REQUEST",e.GET_PAYMENT_GATEWAY_SUCCESS="GET_PAYMENT_GATEWAY_SUCCESS",e.GET_PAYMENT_GATEWAY_ERROR="GET_PAYMENT_GATEWAY_ERROR"}(ri||(ri={}));const oi="wc/payment-gateways",ni="wc/v3";function si(){return{type:ri.GET_PAYMENT_GATEWAYS_REQUEST}}function ii(e){return{type:ri.GET_PAYMENT_GATEWAYS_SUCCESS,paymentGateways:e}}function ai(e){return{type:ri.GET_PAYMENT_GATEWAYS_ERROR,error:e}}function ci(){return{type:ri.GET_PAYMENT_GATEWAY_REQUEST}}function ui(e){return{type:ri.GET_PAYMENT_GATEWAY_ERROR,error:e}}function li(e){return{type:ri.GET_PAYMENT_GATEWAY_SUCCESS,paymentGateway:e}}function Ei(e){return{type:ri.UPDATE_PAYMENT_GATEWAY_SUCCESS,paymentGateway:e}}function di(){return{type:ri.UPDATE_PAYMENT_GATEWAY_REQUEST}}function pi(e){return{type:ri.UPDATE_PAYMENT_GATEWAY_ERROR,error:e}}function*Ti(e,t){try{yield di();const r=yield(0,re.apiFetch)({method:"PUT",path:ni+"/payment_gateways/"+e,body:JSON.stringify(t)});if(r&&r.id===e)return yield Ei(r),r}catch(e){throw yield pi(e),e}}const Si=te.controls&&te.controls.dispatch?te.controls.dispatch:re.dispatch;function*_i(){yield si();try{const e=yield(0,re.apiFetch)({path:ni+"/payment_gateways"});yield ii(e);for(let t=0;t<e.length;t++)yield Si(oi,"finishResolution","getPaymentGateway",[e[t].id])}catch(e){yield ai(e)}}function*yi(e){yield ci();try{const t=yield(0,re.apiFetch)({path:ni+"/payment_gateways/"+e});if(t&&t.id)return yield li(t),t}catch(e){yield ui(e)}}function fi(e,t){return e.paymentGateways.find((e=>e.id===t))}function Ri(e){return e.paymentGateways}function gi(e,t){return e.errors[t]||null}function mi(e){return e.isUpdating||!1}const Oi=oi;var hi;function Ai(){return{type:hi.GET_SHIPPING_METHODS_REQUEST}}function Ii(e){return{type:hi.GET_SHIPPING_METHODS_SUCCESS,shippingMethods:e}}function Ci(e){return{type:hi.GET_SHIPPING_METHODS_ERROR,error:e}}function*Pi(e=!1){let t=ve+"/shipping-partner-suggestions";e&&(t+="?force_default_suggestions=true"),yield Ai();try{const e=yield(0,re.apiFetch)({path:t,method:"GET"});yield Ii(e)}catch(e){yield Ci(e)}}(0,te.registerStore)(oi,{actions:w,selectors:k,resolvers:b,controls:re.controls,reducer:(e={paymentGateways:[],isUpdating:!1,errors:{}},t)=>{if(t&&"type"in t)switch(t.type){case ri.GET_PAYMENT_GATEWAYS_REQUEST:case ri.GET_PAYMENT_GATEWAY_REQUEST:return e;case ri.GET_PAYMENT_GATEWAYS_SUCCESS:return{...e,paymentGateways:t.paymentGateways};case ri.GET_PAYMENT_GATEWAYS_ERROR:return{...e,errors:{...e.errors,getPaymentGateways:t.error}};case ri.GET_PAYMENT_GATEWAY_ERROR:return{...e,errors:{...e.errors,getPaymentGateway:t.error}};case ri.UPDATE_PAYMENT_GATEWAY_REQUEST:return{...e,isUpdating:!0};case ri.UPDATE_PAYMENT_GATEWAY_SUCCESS:case ri.GET_PAYMENT_GATEWAY_SUCCESS:return function(e,t){const r=e.paymentGateways.findIndex((e=>e.id===t.id));return-1===r?{...e,paymentGateways:[...e.paymentGateways,t],isUpdating:!1}:{...e,paymentGateways:[...e.paymentGateways.slice(0,r),t,...e.paymentGateways.slice(r+1)],isUpdating:!1}}(e,t.paymentGateway);case ri.UPDATE_PAYMENT_GATEWAY_ERROR:return{...e,errors:{...e.errors,updatePaymentGateway:t.error},isUpdating:!1}}return e}}),function(e){e.GET_SHIPPING_METHODS_REQUEST="GET_SHIPPING_METHODS_REQUEST",e.GET_SHIPPING_METHODS_SUCCESS="GET_SHIPPING_METHODS_SUCCESS",e.GET_SHIPPING_METHODS_ERROR="GET_SHIPPING_METHODS_ERROR"}(hi||(hi={}));const Ui=e=>e.shippingMethods||[];function vi(e){return e.isUpdating||!1}const Ni="wc/shipping-methods",Di=(0,te.createReduxStore)(Ni,{reducer:(e={shippingMethods:[],isUpdating:!1,errors:{}},t)=>{if(t&&"type"in t)switch(t.type){case hi.GET_SHIPPING_METHODS_REQUEST:return{...e,isUpdating:!0};case hi.GET_SHIPPING_METHODS_SUCCESS:return{...e,shippingMethods:t.shippingMethods,isUpdating:!1};case hi.GET_SHIPPING_METHODS_ERROR:return{...e,isUpdating:!1,errors:{...e.errors,getShippingMethods:t.error}}}return e},selectors:L,resolvers:M,controls:re.controls,actions:G});(0,te.register)(Di);const wi="wc/admin/products",bi="/wc/v3/products",ki=/%(?:postname|pagename)%/,Gi=`${bi}/suggested-products`,Mi="product";function Li(e){return de(Mi,e)}function Fi(e){const{_fields:t,page:r,per_page:o,...n}=e;return Li(n)}function qi(e={}){var t,r,o;if(!Object.keys(e).length)return"default";const n={...e};return null===(t=e.categories)||void 0===t||t.sort(),null===(r=e.tags)||void 0===r||r.sort(),null===(o=e.attributes)||void 0===o||o.sort(),JSON.stringify(n)}const ji=(e,t,r=void 0)=>e.data[t]||r,xi=yr(((e,t,r=void 0)=>{const o=Li(t),n=e.products[o]?e.products[o].data:void 0;if(!n)return r;if(t&&void 0!==t._fields){const r=t._fields;return n.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return n.map((t=>e.data[t]))}),((e,t)=>{const r=Li(t),o=e.products[r]?e.products[r].data:void 0;return[e.products[r],...(o||[]).map((t=>e.data[t]))]})),Qi=(e,t,r=void 0)=>{const o=Fi(t);return e.productsCount.hasOwnProperty(o)?e.productsCount[o]:r},Ki=(e,t)=>{const r=Li(t);return e.errors[r]},Vi=(e,t)=>{const r=Li(t);return e.errors[r]},$i=(e,t,r)=>{const o=Li(r);return e.errors[`update/${t}/${o}`]},Hi=(e,t)=>e.errors[`delete/${t}`],Yi=(e,t,r)=>{var o;return void 0!==r&&"createProduct"!==t?(null===(o=e.pending[t])||void 0===o?void 0:o[r])||!1:"createProduct"===t&&e.pending[t]||!1},Wi=yr(((e,t)=>{const r=e.data[t];if(r&&r.permalink_template){const e=r.slug||r.generated_slug,[t,o]=r.permalink_template.split(ki);return{prefix:t,postName:e,suffix:o}}return null}),((e,t)=>[e.data[t]])),Ji=yr(((e,t)=>{const r=e.data[t];return(null==r?void 0:r.related_ids)&&xi(e,{include:r.related_ids})||[]}),((e,t)=>[e.data[t]]));function Bi(e,t){const r=qi(t);return e.suggestedProducts[r]?e.suggestedProducts[r].items:[]}var zi;!function(e){e.CREATE_PRODUCT_START="CREATE_PRODUCT_START",e.CREATE_PRODUCT_ERROR="CREATE_PRODUCT_ERROR",e.CREATE_PRODUCT_SUCCESS="CREATE_PRODUCT_SUCCESS",e.GET_PRODUCT_SUCCESS="GET_PRODUCT_SUCCESS",e.GET_PRODUCT_ERROR="GET_PRODUCT_ERROR",e.GET_PRODUCTS_SUCCESS="GET_PRODUCTS_SUCCESS",e.GET_PRODUCTS_ERROR="GET_PRODUCTS_ERROR",e.GET_PRODUCTS_TOTAL_COUNT_SUCCESS="GET_PRODUCTS_TOTAL_COUNT_SUCCESS",e.GET_PRODUCTS_TOTAL_COUNT_ERROR="GET_PRODUCTS_TOTAL_COUNT_ERROR",e.UPDATE_PRODUCT_START="UPDATE_PRODUCT_START",e.UPDATE_PRODUCT_ERROR="UPDATE_PRODUCT_ERROR",e.UPDATE_PRODUCT_SUCCESS="UPDATE_PRODUCT_SUCCESS",e.DELETE_PRODUCT_START="DELETE_PRODUCT_START",e.DELETE_PRODUCT_ERROR="DELETE_PRODUCT_ERROR",e.DELETE_PRODUCT_SUCCESS="DELETE_PRODUCT_SUCCESS",e.DUPLICATE_PRODUCT_START="DUPLICATE_PRODUCT_START",e.DUPLICATE_PRODUCT_ERROR="DUPLICATE_PRODUCT_ERROR",e.DUPLICATE_PRODUCT_SUCCESS="DUPLICATE_PRODUCT_SUCCESS",e.SET_SUGGESTED_PRODUCTS="SET_SUGGESTED_PRODUCTS"}(zi||(zi={}));const Zi=zi;function Xi(e,t){return{type:Zi.GET_PRODUCT_SUCCESS,id:e,product:t}}function ea(e,t){return{type:Zi.GET_PRODUCT_ERROR,productId:e,error:t}}function ta(e,t){return{type:Zi.CREATE_PRODUCT_ERROR,query:e,error:t}}function ra(e,t){return{type:Zi.DUPLICATE_PRODUCT_ERROR,id:e,error:t}}function oa(e,t){return{type:Zi.UPDATE_PRODUCT_ERROR,id:e,error:t}}function na(e,t,r){return{type:Zi.GET_PRODUCTS_SUCCESS,products:t,query:e,totalCount:r}}function sa(e,t){return{type:Zi.GET_PRODUCTS_ERROR,query:e,error:t}}function ia(e,t){return{type:Zi.GET_PRODUCTS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function aa(e,t){return{type:Zi.GET_PRODUCTS_TOTAL_COUNT_ERROR,query:e,error:t}}function*ca(e){yield{type:Zi.CREATE_PRODUCT_START};try{const t=yield(0,re.apiFetch)({path:bi,method:"POST",data:e});return yield function(e,t){return{type:Zi.CREATE_PRODUCT_SUCCESS,id:e,product:t}}(t.id,t),t}catch(t){throw yield ta(e,t),t}}function*ua(e,t){yield function(e){return{type:Zi.UPDATE_PRODUCT_START,id:e}}(e);try{const r=yield(0,re.apiFetch)({path:`${bi}/${e}`,method:"PUT",data:t});return yield function(e,t){return{type:Zi.UPDATE_PRODUCT_SUCCESS,id:e,product:t}}(r.id,r),r}catch(t){throw yield oa(e,t),t}}function*la(e,t){yield function(e){return{type:Zi.DUPLICATE_PRODUCT_START,id:e}}(e);try{const r=yield(0,re.apiFetch)({path:`${bi}/${e}/duplicate`,method:"POST",data:t});return yield function(e,t){return{type:Zi.DUPLICATE_PRODUCT_SUCCESS,id:e,product:t}}(r.id,r),r}catch(t){throw yield ra(e,t),t}}function Ea(e){return{type:Zi.DELETE_PRODUCT_START,id:e}}function da(e,t,r){return{type:Zi.DELETE_PRODUCT_SUCCESS,id:e,product:t,force:r}}function pa(e,t){return{type:Zi.DELETE_PRODUCT_ERROR,id:e,error:t}}function*Ta(e,t=!1){yield Ea(e);try{const r=t?`${bi}/${e}?force=true`:`${bi}/${e}`,o=yield(0,re.apiFetch)({path:r,method:"DELETE"});return yield da(o.id,o,t),o}catch(t){throw yield pa(e,t),t}}function Sa(e,t){return{type:Zi.SET_SUGGESTED_PRODUCTS,key:e,items:t}}const _a=te.controls&&te.controls.dispatch?te.controls.dispatch:re.dispatch,ya=te.controls&&te.controls.resolveSelect?te.controls.resolveSelect:re.select;function*fa(e){const t={...e};t&&t._fields&&!t._fields.includes("id")&&(t._fields=["id",...t._fields]);try{const{items:r,totalCount:o}=yield Te(bi,t);return yield ia(e,o),yield na(e,r,o),r}catch(t){throw yield sa(e,t),t}}function*Ra(e){try{const t=yield(0,re.apiFetch)({path:(0,ne.addQueryArgs)(`${bi}/${e}`,{context:"edit"}),method:"GET"});return yield Xi(e,t),yield _a(wi,"finishResolution","getPermalinkParts",[e]),t}catch(t){throw yield ea(e,t),t}}function*ga(e){try{const t=(yield ya(wi,"getProduct",e)).related_ids;return(null==t?void 0:t.length)?yield ya(wi,"getProducts",{include:t}):[]}catch(e){throw e}}function*ma(e){try{const t={...e,page:1,per_page:1},{totalCount:r}=yield Te(bi,t);return yield ia(e,r),r}catch(t){throw yield aa(e,t),t}}function*Oa(e){yield ya(wi,"getProduct",[e])}const ha=e=>async({dispatch:t})=>{const r=qi(e),o=await ie()({path:(0,ne.addQueryArgs)(Gi,e)});t.setSuggestedProductAction(r,o)};(0,te.registerStore)(wi,{__experimentalUseThunks:!0,reducer:(e={products:{},productsCount:{},errors:{},data:{},pending:{},suggestedProducts:{}},t)=>{if(t&&"type"in t)switch(t.type){case Zi.CREATE_PRODUCT_START:return{...e,pending:{createProduct:!0}};case Zi.UPDATE_PRODUCT_START:return{...e,pending:{updateProduct:{...e.pending.updateProduct||{},[t.id]:!0}}};case Zi.DUPLICATE_PRODUCT_START:return{...e,pending:{duplicateProduct:{...e.pending.duplicateProduct||{},[t.id]:!0}}};case Zi.CREATE_PRODUCT_SUCCESS:case Zi.GET_PRODUCT_SUCCESS:case Zi.UPDATE_PRODUCT_SUCCESS:case Zi.DUPLICATE_PRODUCT_SUCCESS:const r=e.data||{};return{...e,data:{...r,[t.id]:{...r[t.id]||{},...t.product}},pending:{createProduct:!1,duplicateProduct:{...e.pending.duplicateProduct||{},[t.id]:!1},updateProduct:{...e.pending.updateProduct||{},[t.id]:!1}}};case Zi.GET_PRODUCTS_SUCCESS:const o=[],n=t.products.reduce(((t,r)=>(o.push(r.id),t[r.id]={...e.data[r.id]||{},...r},t)),{}),s=Li(t.query);return{...e,products:{...e.products,[s]:{data:o}},data:{...e.data,...n}};case Zi.GET_PRODUCTS_TOTAL_COUNT_SUCCESS:const i=Fi(t.query);return{...e,productsCount:{...e.productsCount,[i]:t.totalCount}};case Zi.GET_PRODUCT_ERROR:return{...e,errors:{...e.errors,[t.productId]:t.error}};case Zi.GET_PRODUCTS_ERROR:case Zi.GET_PRODUCTS_TOTAL_COUNT_ERROR:case Zi.CREATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[Li(t.query)]:t.error},pending:{createProduct:!1}};case Zi.UPDATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`update/${t.id}`]:t.error}};case Zi.DUPLICATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`duplicate/${t.id}`]:t.error}};case Zi.DELETE_PRODUCT_START:return{...e,pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!0}}};case Zi.DELETE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`delete/${t.id}`]:t.error},pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!1}}};case Zi.DELETE_PRODUCT_SUCCESS:const a=e.data||{};return{...e,data:{...a,[t.id]:{...a[t.id]||{},...t.product,status:t.force?"deleted":"trash"}},pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!1}}};case Zi.SET_SUGGESTED_PRODUCTS:return{...e,suggestedProducts:{...e.suggestedProducts,[t.key]:{items:t.items||[]}}};default:return e}return e},actions:q,controls:ce,selectors:F,resolvers:j});const Aa=wi,Ia="wc/admin/orders",Ca="/wc/v3/orders",Pa="order";function Ua(e){return de(Pa,e)}function va(e){const{_fields:t,page:r,per_page:o,...n}=e;return Ua(n)}const Na=yr(((e,t,r=void 0)=>{const o=Ua(t),n=e.orders[o]?e.orders[o].data:void 0;if(!n)return r;if(t&&void 0!==t._fields){const r=t._fields;return n.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return n.map((t=>e.data[t]))}),((e,t)=>{const r=Ua(t),o=e.orders[r]?e.orders[r].data:[];return[e.orders[r],...o.map((t=>e.data[t]))]})),Da=(e,t,r=void 0)=>{const o=va(t);return e.ordersCount.hasOwnProperty(o)?e.ordersCount[o]:r},wa=(e,t)=>{const r=Ua(t);return e.errors[r]};var ba;!function(e){e.GET_ORDER_SUCCESS="GET_ORDER_SUCCESS",e.GET_ORDER_ERROR="GET_ORDER_ERROR",e.GET_ORDERS_SUCCESS="GET_ORDERS_SUCCESS",e.GET_ORDERS_ERROR="GET_ORDERS_ERROR",e.GET_ORDERS_TOTAL_COUNT_SUCCESS="GET_ORDERS_TOTAL_COUNT_SUCCESS",e.GET_ORDERS_TOTAL_COUNT_ERROR="GET_ORDERS_TOTAL_COUNT_ERROR"}(ba||(ba={}));const ka=ba;function Ga(e,t){return{type:ka.GET_ORDER_SUCCESS,id:e,order:t}}function Ma(e,t){return{type:ka.GET_ORDER_ERROR,query:e,error:t}}function La(e,t,r){return{type:ka.GET_ORDERS_SUCCESS,orders:t,query:e,totalCount:r}}function Fa(e,t){return{type:ka.GET_ORDERS_ERROR,query:e,error:t}}function qa(e,t){return{type:ka.GET_ORDERS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function ja(e,t){return{type:ka.GET_ORDERS_TOTAL_COUNT_ERROR,query:e,error:t}}function*xa(e){const t={...e};t&&t._fields&&!t._fields.includes("id")&&(t._fields=["id",...t._fields]);try{const{items:r,totalCount:o}=yield Te(Ca,t);return yield qa(e,o),yield La(e,r,o),r}catch(t){return yield Fa(e,t),t}}function*Qa(e){try{const t={...e,page:1,per_page:1},{totalCount:r}=yield Te(Ca,t);return yield qa(e,r),r}catch(t){return yield ja(e,t),t}}(0,te.registerStore)(Ia,{reducer:(e={orders:{},ordersCount:{},errors:{},data:{}},t)=>{if(t&&"type"in t)switch(t.type){case ka.GET_ORDER_SUCCESS:const r=e.data||{};return{...e,data:{...r,[t.id]:{...r[t.id]||{},...t.order}}};case ka.GET_ORDERS_SUCCESS:const o=[],n=t.orders.reduce(((t,r)=>(o.push(r.id),t[r.id]={...e.data[r.id]||{},...r},t)),{}),s=Ua(t.query);return{...e,orders:{...e.orders,[s]:{data:o}},data:{...e.data,...n}};case ka.GET_ORDERS_TOTAL_COUNT_SUCCESS:const i=va(t.query);return{...e,ordersCount:{...e.ordersCount,[i]:t.totalCount}};case ka.GET_ORDER_ERROR:case ka.GET_ORDERS_ERROR:case ka.GET_ORDERS_TOTAL_COUNT_ERROR:return{...e,errors:{...e.errors,[Ua(t.query)]:t.error}};default:return e}return e},actions:Q,controls:ce,selectors:x,resolvers:K});const Ka=Ia,Va="wc/admin/products/attributes";var $a;!function(e){e.CREATE_ITEM="CREATE_ITEM",e.DELETE_ITEM="DELETE_ITEM",e.GET_ITEM="GET_ITEM",e.GET_ITEMS="GET_ITEMS",e.GET_ITEMS_TOTAL_COUNT="GET_ITEMS_TOTAL_COUNT",e.UPDATE_ITEM="UPDATE_ITEM"}($a||($a={}));const Ha=$a,Ya=(e,t,r)=>{var o;let n=e;if(null===(o=n.match(/{(.*?)}/g))||void 0===o||o.forEach(((e,t)=>{n=n.replace(e,r[t].toString())})),new RegExp(/{|}/).test(n.toString()))throw new Error("Not all URL parameters were replaced");return(0,ne.addQueryArgs)(n,t)},Wa=(e,t=[])=>{const r="string"==typeof e||"number"==typeof e?e:e.id;return t.length?t.join("/")+"/"+r:r},Ja=(e,t=[],r={})=>{const o=[],n={},s=t.length>0;return e.forEach((e=>{const i=s?Wa(e.id,t):e.id;o.push(i),n[i]={...r[i]||{},...e}})),{objItems:n,ids:o}},Ba=(e,t=[])=>"string"==typeof e||"number"==typeof e?{id:e,key:e}:{id:e.id,key:Wa(e,t)},za=(e,t,r=[])=>(...o)=>(r.forEach(((e,t)=>{void 0===o[t+1]&&(o[t+1]=e)})),e(...o,t)),Za=e=>{var t;const r=[];return null===(t=e.match(/{(.*?)}/g))||void 0===t||t.forEach((e=>{const t=e.substr(1,e.length-2);r.push(t)})),r},Xa=(e,t)=>{if("object"!=typeof t)return[];const r=[];return Za(e).forEach((e=>{t.hasOwnProperty(e)&&r.push(t[e])})),r},ec=(e,t)=>{const[r,...o]=e;if(!r||!((e,t)=>{if("string"==typeof e||"number"==typeof e)return!0;const r=["id",...Za(t)];return!(!e||"object"!=typeof e||!e.hasOwnProperty("id")||JSON.stringify(r.sort())!==JSON.stringify(Object.keys(e).sort()))})(r,t))return e;const n=Xa(t,r),{key:s}=Ba(r,n);return[s,...o]},tc=(e,t)=>{const r={...e};return Za(t).forEach((e=>{delete r[e]})),r},rc=de,oc=(e,t)=>{switch(e){case`create${t}`:return Ha.CREATE_ITEM;case`delete${t}`:return Ha.DELETE_ITEM;case`update${t}`:return Ha.UPDATE_ITEM}return e},nc=(e,t)=>{const r=rc(Ha.CREATE_ITEM,t);return e.errors[r]},sc=(e,t,r)=>{const o=Xa(r,t),{key:n}=Ba(t,o),s=rc(Ha.DELETE_ITEM,n);return e.errors[s]},ic=(e,t,r)=>{const o=Xa(r,t),{key:n}=Ba(t,o);return e.data[n]},ac=(e,t,r)=>{const o=Xa(r,t),{key:n}=Ba(t,o),s=rc(Ha.GET_ITEM,n);return e.errors[s]},cc=yr(((e,t)=>{const r=rc(Ha.GET_ITEMS,t||{}),o=e.items[r]?e.items[r].data:void 0;if(!o)return null;if(t&&void 0!==t._fields){const r=t._fields;return o.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return o.map((t=>e.data[t])).filter((e=>void 0!==e))}),((e,t)=>{const r=rc(Ha.GET_ITEMS,t||{}),o=e.items[r]?e.items[r].data:void 0;return[e.items[r],...(o||[]).map((t=>e.data[t]))]})),uc=(e,t,r=void 0)=>{const o=pe(Ha.GET_ITEMS,t||{});return e.itemsCount.hasOwnProperty(o)?e.itemsCount[o]:r},lc=(e,t)=>{const r=rc(Ha.GET_ITEMS,t||{});return e.errors[r]},Ec=(e,t,r)=>{const{key:o}=Ba(t,r),n=rc(Ha.UPDATE_ITEM,o);return e.errors[n]},dc={};var pc;!function(e){e.CREATE_ITEM_ERROR="CREATE_ITEM_ERROR",e.CREATE_ITEM_REQUEST="CREATE_ITEM_REQUEST",e.CREATE_ITEM_SUCCESS="CREATE_ITEM_SUCCESS",e.DELETE_ITEM_ERROR="DELETE_ITEM_ERROR",e.DELETE_ITEM_REQUEST="DELETE_ITEM_REQUEST",e.DELETE_ITEM_SUCCESS="DELETE_ITEM_SUCCESS",e.GET_ITEM_ERROR="GET_ITEM_ERROR",e.GET_ITEM_SUCCESS="GET_ITEM_SUCCESS",e.GET_ITEMS_ERROR="GET_ITEMS_ERROR",e.GET_ITEMS_SUCCESS="GET_ITEMS_SUCCESS",e.UPDATE_ITEM_ERROR="UPDATE_ITEM_ERROR",e.UPDATE_ITEM_REQUEST="UPDATE_ITEM_REQUEST",e.UPDATE_ITEM_SUCCESS="UPDATE_ITEM_SUCCESS",e.GET_ITEMS_TOTAL_COUNT_SUCCESS="GET_ITEMS_TOTAL_COUNT_SUCCESS",e.GET_ITEMS_TOTAL_COUNT_ERROR="GET_ITEMS_TOTAL_COUNT_ERROR"}(pc||(pc={}));const Tc=pc;function Sc(e,t){return{type:Tc.GET_ITEMS_ERROR,query:e,error:t,errorType:Ha.GET_ITEMS}}function _c(e,t,r){return{type:Tc.GET_ITEMS_SUCCESS,items:t,query:e,urlParameters:r}}function yc(e,t){return{type:Tc.GET_ITEMS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function fc(e,t){return{type:Tc.GET_ITEMS_TOTAL_COUNT_ERROR,query:e,error:t,errorType:Ha.GET_ITEMS_TOTAL_COUNT}}const Rc=({storeName:e,resourceName:t,namespace:r,pluralResourceName:o,storeConfig:n={}})=>{const s=(({namespace:e,resourceName:t})=>({[`create${t}`]:function*(t,r){yield function(e){return{type:Tc.CREATE_ITEM_REQUEST,query:e}}(t);const o=Xa(e,t);try{const n=yield(0,re.apiFetch)({path:Ya(e,tc(t,e),o),method:"POST"}),{key:s}=Ba(n.id,o);return yield function(e,t,r,o){return{type:Tc.CREATE_ITEM_SUCCESS,key:e,item:t,query:r,options:o}}(s,n,t,r),n}catch(e){throw yield function(e,t){return{type:Tc.CREATE_ITEM_ERROR,query:e,error:t,errorType:Ha.CREATE_ITEM}}(t,e),e}},[`delete${t}`]:function*(t,r=!0){const o=Xa(e,t),{id:n,key:s}=Ba(t,o);yield function(e,t){return{type:Tc.DELETE_ITEM_REQUEST,key:e,force:t}}(s,r);try{const t=yield(0,re.apiFetch)({path:Ya(`${e}/${n}`,{force:r},o),method:"DELETE"});return yield function(e,t,r){return{type:Tc.DELETE_ITEM_SUCCESS,key:e,force:t,item:r}}(s,r,t),t}catch(e){throw yield function(e,t,r){return{type:Tc.DELETE_ITEM_ERROR,key:e,error:t,errorType:Ha.DELETE_ITEM,force:r}}(s,e,r),e}},[`update${t}`]:function*(t,r){const o=Xa(e,t),{id:n,key:s}=Ba(t,o);yield function(e,t){return{type:Tc.UPDATE_ITEM_REQUEST,key:e,query:t}}(s,r);try{const t=yield(0,re.apiFetch)({path:Ya(`${e}/${n}`,{},o),method:"PUT",data:r});return yield function(e,t,r){return{type:Tc.UPDATE_ITEM_SUCCESS,key:e,item:t,query:r}}(s,t,r),t}catch(e){throw yield function(e,t,r){return{type:Tc.UPDATE_ITEM_ERROR,key:e,error:t,errorType:Ha.UPDATE_ITEM,query:r}}(s,e,r),e}}}))({resourceName:t,namespace:r}),i=(({storeName:e,resourceName:t,pluralResourceName:r,namespace:o})=>({[`get${t}`]:function*(e){const t=Xa(o,e),{id:r,key:n}=Ba(e,t);try{const e=yield(0,re.apiFetch)({path:Ya(`${o}/${r}`,{},t),method:"GET"});return yield function(e,t){return{type:Tc.GET_ITEM_SUCCESS,key:e,item:t}}(n,e),e}catch(e){throw yield function(e,t){return{type:Tc.GET_ITEM_ERROR,key:e,error:t,errorType:Ha.GET_ITEM}}(n,e),e}},[`get${r}`]:function*(n){const s=Xa(o,n||{}),i=tc(n||{},o);yield te.controls.dispatch(e,"startResolution",`get${r}TotalCount`,[n]),i&&i._fields&&!i._fields.includes("id")&&(i._fields=["id",...i._fields]);try{const a=Ya(o,n||{},s),{items:c,totalCount:u}=yield Te(a,i);yield yc(n,u),yield te.controls.dispatch(e,"finishResolution",`get${r}TotalCount`,[n]),yield _c(n,c,s);for(const r of c)r.id&&(yield te.controls.dispatch(e,"finishResolution",`get${t}`,[r.id]));return c}catch(e){throw yield fc(n,e),yield Sc(n,e),e}},[`get${r}TotalCount`]:function*(t){if(yield te.controls.select(e,"hasStartedResolution",`get${r}`,[t]))return;const n={...t||{},page:1,per_page:1},s=Xa(o,n),i=tc(n,o);i&&i._fields&&!i._fields.includes("id")&&(i._fields=["id",...i._fields]);try{const e=Ya(o,{},s),{totalCount:r}=yield Te(e,n);return yield yc(t,r),r}catch(e){return yield fc(t,e),e}}}))({storeName:e,resourceName:t,pluralResourceName:o,namespace:r}),a=(({resourceName:e,pluralResourceName:t,namespace:r})=>({[`get${e}`]:za(ic,r),[`get${e}Error`]:za(ac,r),[`get${t}`]:za(cc,r,[dc]),[`get${t}TotalCount`]:za(uc,r,[dc,void 0]),[`get${t}Error`]:za(lc,r),[`get${e}CreateError`]:za(nc,r),[`get${e}DeleteError`]:za(sc,r),[`get${e}UpdateError`]:za(Ec,r),hasFinishedRequest:(t,o,n=[])=>{const s=ec(n,r),i=oc(o,e),a=rc(i,...s);if(o)return t.requesting.hasOwnProperty(a)&&!t.requesting[a]},isRequesting:(t,o,n=[])=>{const s=ec(n,r),i=oc(o,e),a=rc(i,...s);return t.requesting[a]}}))({resourceName:t,pluralResourceName:o,namespace:r}),{reducer:c,actions:u={},selectors:l={},resolvers:E={},controls:d={}}=n,p=(T=c,(e={items:{},data:{},itemsCount:{},errors:{},requesting:{}},t)=>{var r,o,n;const s=e.data||{};if(t&&"type"in t)switch(t.type){case pc.CREATE_ITEM_ERROR:const i=rc(t.errorType,t.query||{});return{...e,errors:{...e.errors,[i]:t.error},requesting:{...e.requesting,[i]:!1}};case pc.GET_ITEMS_TOTAL_COUNT_ERROR:case pc.GET_ITEMS_ERROR:return{...e,errors:{...e.errors,[rc(t.errorType,t.query||{})]:t.error}};case pc.GET_ITEMS_TOTAL_COUNT_SUCCESS:return{...e,itemsCount:{...e.itemsCount,[pe(Ha.GET_ITEMS,t.query||{})]:t.totalCount}};case pc.CREATE_ITEM_SUCCESS:{const{options:i={}}=t,{objItems:a,ids:c}=Ja([t.item],i.optimisticUrlParameters,s),u={...s,...a},l=rc(Ha.CREATE_ITEM,c[0],t.query),E=rc(Ha.GET_ITEMS,i.optimisticQueryUpdate),d=pe(Ha.GET_ITEMS,(null==i?void 0:i.optimisticQueryUpdate)||{});let p=e.items,T=[...(null===(r=p[E])||void 0===r?void 0:r.data)||[],...c],S=e.itemsCount;if(null==i?void 0:i.optimisticQueryUpdate){if(null===(o=i.optimisticQueryUpdate)||void 0===o?void 0:o.order_by){const e=null===(n=i.optimisticQueryUpdate)||void 0===n?void 0:n.order_by;let t=Object.values(function(e,t){return t.reduce(((t,r)=>(e[r]&&(t[r]=e[r]),t)),{})}(u,T));t=t.sort(((t,r)=>String(t[e]).toLowerCase().localeCompare(String(r[e]).toLowerCase())));const{ids:r}=Ja(t,i.optimisticUrlParameters);T=r}p={...p,[E]:{data:T}},S={...e.itemsCount,[d]:T.length}}return{...e,items:p,itemsCount:S,data:u,requesting:{...e.requesting,[l]:!1}}}case pc.GET_ITEM_SUCCESS:return{...e,data:{...s,[t.key]:{...s[t.key]||{},...t.item}}};case pc.UPDATE_ITEM_SUCCESS:const a=rc(Ha.UPDATE_ITEM,t.key,t.query);return{...e,data:{...s,[t.key]:{...s[t.key]||{},...t.item}},requesting:{...e.requesting,[a]:!1}};case pc.DELETE_ITEM_SUCCESS:const c=rc(Ha.DELETE_ITEM,t.key,t.force),u=Object.keys(e.data).reduce(((r,o)=>o!==t.key.toString()?(r[o]=e.data[o],r):(t.force||(r[o]=t.item),r)),{});return{...e,data:u,requesting:{...e.requesting,[c]:!1}};case pc.DELETE_ITEM_ERROR:const l=rc(t.errorType,t.key,t.force);return{...e,errors:{...e.errors,[l]:t.error},requesting:{...e.requesting,[l]:!1}};case pc.GET_ITEM_ERROR:return{...e,errors:{...e.errors,[rc(t.errorType,t.key)]:t.error}};case pc.UPDATE_ITEM_ERROR:const E=rc(t.errorType,t.key,t.query);return{...e,errors:{...e.errors,[E]:t.error},requesting:{...e.requesting,[E]:!1}};case pc.GET_ITEMS_SUCCESS:const{objItems:d,ids:p}=Ja(t.items,t.urlParameters,s),T=rc(Ha.GET_ITEMS,t.query||{});return{...e,items:{...e.items,[T]:{data:p}},data:{...e.data,...d}};case pc.CREATE_ITEM_REQUEST:return{...e,requesting:{...e.requesting,[rc(Ha.CREATE_ITEM,t.query)]:!0}};case pc.DELETE_ITEM_REQUEST:return{...e,requesting:{...e.requesting,[rc(Ha.DELETE_ITEM,t.key,t.force)]:!0}};case pc.UPDATE_ITEM_REQUEST:return{...e,requesting:{...e.requesting,[rc(Ha.UPDATE_ITEM,t.key,t.query)]:!0}}}return T?T(e,t):e});var T;(0,te.registerStore)(e,{reducer:p,actions:{...s,...u},selectors:{...a,...l},resolvers:{...i,...E},controls:{...ce,...d}})};Rc({storeName:Va,resourceName:"ProductAttribute",pluralResourceName:"ProductAttributes",namespace:"/wc/v3/products/attributes"});const gc=Va,mc="experimental/wc/admin/products/shipping-classes";Rc({storeName:mc,resourceName:"ProductShippingClass",pluralResourceName:"ProductShippingClasses",namespace:"/wc/v3/products/shipping_classes"});const Oc=mc,hc="experimental/wc/admin/shipping/zones";Rc({storeName:hc,resourceName:"ShippingZone",pluralResourceName:"ShippingZones",namespace:"/wc/v3/shipping/zones"});const Ac=hc,Ic="wc/admin/products/tags";Rc({storeName:Ic,resourceName:"ProductTag",pluralResourceName:"ProductTags",namespace:"/wc/v3/products/tags"});const Cc=Ic,Pc="experimental/wc/admin/products/categories";Rc({storeName:Pc,resourceName:"ProductCategory",pluralResourceName:"ProductCategories",namespace:"/wc/v3/products/categories"});const Uc=Pc,vc="wc/admin/products/attributes/terms";Rc({storeName:vc,resourceName:"ProductAttributeTerm",pluralResourceName:"ProductAttributeTerms",namespace:"/wc/v3/products/attributes/{attribute_id}/terms"});const Nc=vc,Dc="wc/admin/products/variations",wc="/wc/v3/products/{product_id}/variations";var bc;!function(e){e.GENERATE_VARIATIONS_REQUEST="GENERATE_VARIATIONS_REQUEST",e.GENERATE_VARIATIONS_SUCCESS="GENERATE_VARIATIONS_SUCCESS",e.GENERATE_VARIATIONS_ERROR="GENERATE_VARIATIONS_ERROR",e.BATCH_UPDATE_VARIATIONS_ERROR="BATCH_UPDATE_VARIATIONS_ERROR"}(bc||(bc={}));const kc=bc;var Gc;!function(e){e.GENERATE_VARIATIONS="GENERATE_VARIATIONS"}(Gc||(Gc={}));const Mc=Gc;function Lc(e,t){return{type:kc.GENERATE_VARIATIONS_ERROR,key:e,error:t,errorType:Mc.GENERATE_VARIATIONS}}function Fc(e){return{type:kc.GENERATE_VARIATIONS_REQUEST,key:e}}function qc(e){return{type:kc.GENERATE_VARIATIONS_SUCCESS,key:e}}const jc=function*(e,t,r,o=!0){const n=Xa(wc,e),{key:s}=Ba(e,n);if(yield Fc(s),o)try{yield te.controls.dispatch("core","saveEntityRecord","postType","product",{id:n[0],...t})}catch(e){throw yield Lc(s,e),e}try{const e=yield(0,re.apiFetch)({path:Ya(`${wc}/generate`,{},n),method:"POST",data:r});return yield qc(s),e}catch(e){throw yield Lc(s,e),e}};function xc(e,t){return{type:kc.BATCH_UPDATE_VARIATIONS_ERROR,key:e,error:t,errorType:"BATCH_UPDATE_VARIATIONS"}}function*Qc(e,t){const r=Xa(wc,e);try{return yield(0,re.apiFetch)({path:Ya(`${wc}/batch`,{},r),method:"POST",data:t})}catch(t){const{key:o}=Ba(e,r);throw yield xc(o,t),t}}const Kc=(e,t)=>{const r=Xa(wc,t),{key:o}=Ba(t,r),n=rc(Mc.GENERATE_VARIATIONS,o);return e.requesting[n]},Vc=(e,t)=>{const r=Xa(wc,t),{key:o}=Ba(t,r),n=rc(Mc.GENERATE_VARIATIONS,o);return e.errors[n]};Rc({storeName:Dc,resourceName:"ProductVariation",pluralResourceName:"ProductVariations",namespace:wc,storeConfig:{reducer:(e={items:{},data:{},itemsCount:{},errors:{},requesting:{}},t)=>{if(t&&"type"in t)switch(t.type){case bc.GENERATE_VARIATIONS_REQUEST:return{...e,requesting:{...e.requesting,[rc(Mc.GENERATE_VARIATIONS,t.key)]:!0}};case bc.GENERATE_VARIATIONS_SUCCESS:return{...e,requesting:{...e.requesting,[rc(Mc.GENERATE_VARIATIONS,t.key)]:!1},errors:{...e.errors,[rc(Mc.GENERATE_VARIATIONS,t.key)]:void 0}};case bc.GENERATE_VARIATIONS_ERROR:return{...e,errors:{...e.errors,[rc(t.errorType,t.key)]:t.error},requesting:{...e.requesting,[rc(Mc.GENERATE_VARIATIONS,t.key)]:!1}};default:return e}return e},actions:V,selectors:$}});const $c=Dc,Hc="experimental/wc/admin/product-form",Yc=e=>e.fields,Wc=(e,t)=>e.fields.find((e=>e.id===t)),Jc=e=>{const{errors:t,...r}=e;return r};var Bc;!function(e){e.GET_FIELDS_ERROR="GET_FIELDS_ERROR",e.GET_FIELDS_SUCCESS="GET_FIELDS_SUCCESS",e.GET_PRODUCT_FORM_ERROR="GET_PRODUCT_FORM_ERROR",e.GET_PRODUCT_FORM_SUCCESS="GET_PRODUCT_FORM_SUCCESS"}(Bc||(Bc={}));const zc=Bc;function Zc(e){return{type:zc.GET_FIELDS_SUCCESS,fields:e}}function Xc(e){return{type:zc.GET_FIELDS_ERROR,error:e}}function eu(e){return{type:zc.GET_PRODUCT_FORM_SUCCESS,fields:e.fields,sections:e.sections,subsections:e.subsections,tabs:e.tabs}}function tu(e){return{type:zc.GET_PRODUCT_FORM_ERROR,error:e}}function*ru(){try{const e=ve+"/product-form/fields";return Zc(yield(0,re.apiFetch)({path:e,method:"GET"}))}catch(e){return Xc(e)}}function*ou(){try{const e=ve+"/product-form";return eu(yield(0,re.apiFetch)({path:e,method:"GET"}))}catch(e){return tu(e)}}(0,te.registerStore)(Hc,{reducer:(e={errors:{},fields:[],sections:[],subsections:[],tabs:[]},t)=>{switch(t.type){case zc.GET_FIELDS_SUCCESS:e={...e,fields:t.fields};break;case zc.GET_FIELDS_ERROR:e={...e,errors:{...e.errors,fields:t.error}};break;case zc.GET_PRODUCT_FORM_SUCCESS:e={...e,fields:t.fields,sections:t.sections,subsections:t.subsections,tabs:t.tabs};break;case zc.GET_PRODUCT_FORM_ERROR:e={...e,errors:{...e.errors,fields:t.error,sections:t.error,subsections:t.error}}}return e},actions:Y,controls:re.controls,selectors:H,resolvers:W});const nu=Hc,su="experimental/wc/admin/tax-classes",iu="/wc/v3/taxes/classes";function*au(e){const t=Xa(iu,e||{}),r=tc(e||{},iu);try{const o=Ya(iu,e||{},t),{items:n}=yield Te(o,r);return yield yc(e,n.length),yield _c(e,n.map((e=>{var t;return{...e,id:null!==(t=e.id)&&void 0!==t?t:e.slug}})),t),n}catch(t){throw yield fc(e,t),yield Sc(e,t),t}}Rc({storeName:su,resourceName:"TaxClass",pluralResourceName:"TaxClasses",namespace:iu,storeConfig:{resolvers:J}});const cu=su,uu=window.wp.compose,lu=window.wp.element,Eu=(e,t)=>(0,uu.createHigherOrderComponent)((r=>o=>{const n=(0,lu.useRef)(t),{startResolution:s,finishResolution:i,updateSettingsForGroup:a,clearIsDirty:c}=(0,te.useDispatch)(oe),{isResolvingGroup:u,hasFinishedResolutionGroup:l}=(0,te.useSelect)((t=>{const{isResolving:r,hasFinishedResolution:o}=t(oe);return{isResolvingGroup:r("getSettings",[e]),hasFinishedResolutionGroup:o("getSettings",[e])}}),[]);return(0,lu.useEffect)((()=>{n.current&&(u||l||(s("getSettings",[e]),a(e,n.current),c(e),i("getSettings",[e])))}),[u,l,i,a,s,c]),(0,lu.createElement)(r,{...o})}),"withSettingsHydration"),du=e=>{let t=!1;return(0,uu.createHigherOrderComponent)((r=>o=>{const n=(0,lu.useRef)(e),{isResolvingGroup:s,hasFinishedResolutionGroup:i}=(0,te.useSelect)((e=>{const{isResolving:t,hasFinishedResolution:r}=e(pr);return{isResolvingGroup:t("getProfileItems",[]),hasFinishedResolutionGroup:r("getProfileItems",[])}})),{startResolution:a,finishResolution:c,setProfileItems:u}=(0,te.useDispatch)(pr);return(0,lu.useEffect)((()=>{if(!n.current)return;const{profileItems:e}=n.current;e&&(!e||t||s||i||(a("getProfileItems",[]),u(e,!0),c("getProfileItems",[]),t=!0))}),[c,u,a,s,i]),(0,lu.createElement)(r,{...o})}),"withOnboardingHydration")},pu=e=>(0,uu.createHigherOrderComponent)((t=>r=>{const o=(0,te.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(ue);return!r("getCurrentUser")&&!o("getCurrentUser")})),{startResolution:n,finishResolution:s,receiveCurrentUser:i}=(0,te.useDispatch)(ue);return o&&(n("getCurrentUser",[]),i(e),s("getCurrentUser",[])),(0,lu.createElement)(t,{...r})}),"withCurrentUserHydration"),Tu=e=>(0,uu.createHigherOrderComponent)((t=>r=>{const o=(0,te.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(ds);return!r("getMenuItems")&&!o("getMenuItems")})),{startResolution:n,finishResolution:s,setMenuItems:i}=(0,te.useDispatch)(ds);return(0,lu.useEffect)((()=>{o&&(n("getMenuItems",[]),i(e.menuItems),s("getMenuItems",[]))}),[o]),(0,lu.createElement)(t,{...r})}),"withNavigationHydration"),Su=e=>(0,uu.createHigherOrderComponent)((t=>r=>{const o=(0,te.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(et);return!r("getActivePlugins",[])&&!o("getActivePlugins",[])}),[]),{startResolution:n,finishResolution:s,updateActivePlugins:i,updateInstalledPlugins:a,updateIsJetpackConnected:c}=(0,te.useDispatch)(et);return(0,lu.useEffect)((()=>{o&&(n("getActivePlugins",[]),n("getInstalledPlugins",[]),n("isJetpackConnected",[]),i(e.activePlugins,!0),a(e.installedPlugins,!0),c(!(!e.jetpackStatus||!e.jetpackStatus.isActive)),s("getActivePlugins",[]),s("getInstalledPlugins",[]),s("isJetpackConnected",[]))}),[o]),(0,lu.createElement)(t,{...r})}),"withPluginsHydration"),_u=e=>{const t=(0,te.useSelect)((t=>{const{isResolving:r,hasFinishedResolution:o}=t(qt);return e?Object.fromEntries(Object.keys(e).map((e=>[e,!r("getOption",[e])&&!o("getOption",[e])]))):{}}),[]),{startResolution:r,finishResolution:o,receiveOptions:n}=(0,te.useDispatch)(qt);(0,lu.useEffect)((()=>{Object.entries(t).forEach((([t,s])=>{s&&(r("getOption",[t]),n({[t]:e[t]}),o("getOption",[t]))}))}),[t])},yu=e=>(0,uu.createHigherOrderComponent)((t=>r=>(_u(e),(0,lu.createElement)(t,{...r}))),"withOptionsHydration"),fu=(e,t=[])=>{const{requestedSettings:r,settingsError:o,isRequesting:n,isDirty:s}=(0,te.useSelect)((r=>{const{getLastSettingsErrorForGroup:o,getSettingsForGroup:n,getIsDirty:s,isUpdateSettingsRequesting:i}=r(oe);return{requestedSettings:n(e,t),settingsError:Boolean(o(e)),isRequesting:i(e),isDirty:s(e,t)}}),[e,...t.sort()]),{persistSettingsForGroup:i,updateAndPersistSettingsForGroup:a,updateSettingsForGroup:c}=(0,te.useDispatch)(oe),u=(0,lu.useCallback)(((t,r)=>{c(e,{[t]:r})}),[e]);return{settingsError:o,isRequesting:n,isDirty:s,...r,persistSettings:(0,lu.useCallback)((()=>{i(e)}),[e]),updateAndPersistSettings:(0,lu.useCallback)(((t,r)=>{a(e,{[t]:r})}),[e]),updateSettings:u}},Ru=e=>{const t=e.woocommerce_meta||{};return(0,Ce.mapValues)(t,(e=>{if(!e||0===e.length)return"";try{return JSON.parse(e)}catch(t){return e}}))},gu=()=>{const e=(0,te.useDispatch)(ue),{addEntities:t,receiveCurrentUser:r,saveEntityRecord:o}=e;let{saveUser:n}=e;const s=(0,te.useSelect)((e=>{const{getCurrentUser:t,getEntity:r,getEntityRecord:o,getLastEntitySaveError:n,hasStartedResolution:s,hasFinishedResolution:i}=e(ue);return{isRequesting:s("getCurrentUser")&&!i("getCurrentUser"),user:t(),getCurrentUser:t,getEntity:r,getEntityRecord:o,getLastEntitySaveError:n}})),i=s.user?Ru(s.user):{};return{isRequesting:s.isRequesting,...i,updateUserPreferences:e=>{"function"!=typeof n&&(n=async e=>(Boolean(s.getEntity("root","user"))||await t([{name:"user",kind:"root",baseURL:"/wp/v2/users",plural:"users"}]),await o("root","user",e),s.getEntityRecord("root","user",e.id)));const i=s.getCurrentUser();return async function(e,t,r,o,n){const s=(0,Ce.mapValues)(n,(e=>"string"==typeof e?e:JSON.stringify(e)));if(0===Object.keys(s).length)return{error:new Error("Invalid woocommerce_meta data for update."),updatedUser:void 0};e({...t,woocommerce_meta:{...t.woocommerce_meta,...s}});const i=await r({id:t.id,woocommerce_meta:s});return void 0===i?{error:o("root","user",t.id),updatedUser:i}:{updatedUser:{...i,woocommerce_meta:Ru(i)}}}(r,i,n,s.getLastEntitySaveError,e)}}},mu=()=>{const e=(0,te.useSelect)((e=>{const{getCurrentUser:t,hasStartedResolution:r,hasFinishedResolution:o}=e(ue);return{isRequesting:r("getCurrentUser")&&!o("getCurrentUser"),user:t(),getCurrentUser:t}}));return{currentUserCan:t=>!(!e.user||!e.user.is_super_admin)||!(!e.user||!e.user.capabilities[t]),user:e.user,isRequesting:e.isRequesting}},Ou=e=>e.filter((e=>!e.isDismissed)),hu=window.moment;var Au=r.n(hu);function Iu(e){const{endpoint:t,query:r,limitBy:o,filters:n=[],advancedFilters:s={}}=e;return r.search?(o||[t]).reduce(((e,t)=>(e[t]=r[t],e)),{}):n.map((e=>function(e,t,r){const o=r[e.param];if(!o)return{};if("advanced"===o){const e=(0,ys.getActiveFiltersFromQuery)(r,t.filters);if(0===e.length)return{};const o=(0,ys.getQueryFromActiveFilters)(e.map((e=>function(e,t){const r=e.filters[t.key];if("Date"!==(0,Ce.get)(r,["input","component"]))return t;const{rule:o,value:n}=t,s={after:"start",before:"end"};if(Array.isArray(n)){const[e,r]=n;return Object.assign({},t,{value:[(0,Ls.appendTimestamp)(Au()(e),s.after),(0,Ls.appendTimestamp)(Au()(r),s.before)]})}return Object.assign({},t,{value:(0,Ls.appendTimestamp)(Au()(n),s[o])})}(t,e))),{},t.filters);return{match:r.match||"all",...o}}const n=(0,Ce.find)((0,ys.flattenFilters)(e.filters),{value:o});if(!n)return{};if(n.settings&&n.settings.param){const{param:e}=n.settings;return r[e]?{[e]:r[e]}:{}}return{[e.param]:o}}(e,s,r))).reduce(((e,t)=>Object.assign(e,t)),{})}const Cu=["stock","customers"];function Pu(e){const{endpoint:t,dataType:r,query:o,fields:n,defaultDateRange:s}=e,i=(0,Ls.getCurrentDates)(o,s),a=(0,Ls.getIntervalForQuery)(o,s),c=Iu(e),u=i[r].before;return(0,Ce.includes)(Cu,t)?{...c,fields:n}:{order:"asc",interval:a,per_page:De,after:(0,Ls.appendTimestamp)(i[r].after,"start"),before:(0,Ls.appendTimestamp)(u,"end"),segmentby:o.segmentby,fields:n,...c}}function Uu(e){const{endpoint:t,select:r}=e,{getReportStats:o,getReportStatsError:n,isResolving:s}=r(Un),i={isRequesting:!1,isError:!1,totals:{primary:null,secondary:null}},a=Pu({...e,dataType:"primary"}),c=o(t,a);if(s("getReportStats",[t,a]))return{...i,isRequesting:!0};if(n(t,a))return{...i,isError:!0};const u=c&&c.data&&c.data.totals||null,l=Pu({...e,dataType:"secondary"}),E=o(t,l);if(s("getReportStats",[t,l]))return{...i,isRequesting:!0};if(n(t,l))return{...i,isError:!0};const d=E&&E.data&&E.data.totals||null;return{...i,totals:{primary:u,secondary:d}}}const vu={requesting:{isEmpty:!1,isError:!1,isRequesting:!0,data:{totals:{},intervals:[]}},error:{isEmpty:!1,isError:!0,isRequesting:!1,data:{totals:{},intervals:[]}},empty:{isEmpty:!0,isError:!1,isRequesting:!1,data:{totals:{},intervals:[]}}},Nu=[],Du=(0,Ce.memoize)(((e,t,r)=>({isEmpty:!1,isError:!1,isRequesting:!1,data:{totals:t,intervals:r}})),((e,t,r)=>[e,t.length,r.length].join(":")));function wu(e){const{endpoint:t}=e;let r=e.selector;e.select&&!e.selector&&(kr()("option.select",{version:"1.7.0",hint:"You can pass the report selectors through option.selector now."}),r=e.select(Un));const{getReportStats:o,getReportStatsError:n,isResolving:s}=r,i=Pu(e),a=o(t,i);if(s("getReportStats",[t,i]))return vu.requesting;if(n(t,i))return vu.error;if(function(e,t){return!(e&&e.data&&e.data.totals&&!(0,Ce.isNull)(e.data.totals)&&((0,Ce.includes)(Cu,t)||e.data.intervals&&0!==e.data.intervals.length))}(a,t))return vu.empty;const c=a&&a.data&&a.data.totals||null;let u=a&&a.data&&a.data.intervals||Nu;if(a.totalResults>De){let e=!0,r=!1;const c=[],l=Math.ceil(a.totalResults/De);let E=1;for(let a=2;a<=l;a++){const u={...i,page:a},d=o(t,u);if(!s("getReportStats",[t,u])){if(n(t,u)){r=!0,e=!1;break}if(c.push(d),E++,E===l){e=!1;break}}}if(e)return vu.requesting;if(r)return vu.error;(0,Ce.forEach)(c,(function(e){e.data&&e.data.intervals&&Array.isArray(e.data.intervals)&&(u=u.concat(e.data.intervals))}))}return Du(de(t,i),c,u)}function bu(e,t){switch(e){case"currency":return t;case"percent":return".0%";case"number":default:return",";case"average":return",.2r"}}function ku(e){const{query:t,tableQuery:r={}}=e,o=Iu(e),n=(0,Ls.getCurrentDates)(t,e.defaultDateRange),s=(0,Ce.includes)(Cu,e.endpoint);return{orderby:t.orderby||"date",order:t.order||"desc",after:s?void 0:(0,Ls.appendTimestamp)(n.primary.after,"start"),before:s?void 0:(0,Ls.appendTimestamp)(n.primary.before,"end"),page:t.paged||"1",per_page:t.per_page||Fe.pageSize,...o,...r}}function Gu(e){const{endpoint:t}=e;let r=e.selector;e.select&&!e.selector&&(kr()("option.select",{version:"1.7.0",hint:"You can pass the report selectors through option.selector now."}),r=e.select(Un));const{getReportItems:o,getReportItemsError:n,hasFinishedResolution:s}=r,i=ku(e),a={query:i,isRequesting:!1,isError:!1,items:{data:[],totalResults:0}},c=o(t,i);return s("getReportItems",[t,i])?n(t,i)?{...a,isError:!0}:{...a,items:c}:{...a,isRequesting:!0}}const Mu="wc/admin/export";var Lu=r(5681),Fu=r.n(Lu);const qu=e=>Fu()(de("export",e)),ju=(e,t,r)=>Boolean(e.requesting[t]&&e.requesting[t][qu(r)]),xu=(e,t,r)=>e.exportIds[t]&&e.exportIds[t][qu(r)],Qu=(e,t,r)=>e.errors[t]&&e.errors[t][qu(r)],Ku={START_EXPORT:"START_EXPORT",SET_EXPORT_ID:"SET_EXPORT_ID",SET_ERROR:"SET_ERROR",SET_IS_REQUESTING:"SET_IS_REQUESTING"};function Vu(e,t,r){return{type:Ku.SET_EXPORT_ID,exportType:e,exportArgs:t,exportId:r}}function $u(e,t,r){return{type:Ku.SET_IS_REQUESTING,selector:e,selectorArgs:t,isRequesting:r}}function Hu(e,t,r){return{type:Ku.SET_ERROR,selector:e,selectorArgs:t,error:r}}function*Yu(e,t){yield $u("startExport",{type:e,args:t},!0);try{const r=yield ae({path:`${Ue}/reports/${e}/export`,method:"POST",data:{report_args:t,email:!0}});yield $u("startExport",{type:e,args:t},!1);const{export_id:o,message:n}=r.data;if(!o)throw new Error(n);return yield Vu(e,t,o),r.data}catch(r){throw r instanceof Error?yield Hu("startExport",{type:e,args:t},r.message):console.error(`Unexpected Error: ${JSON.stringify(r)}`),yield $u("startExport",{type:e,args:t},!1),r}}(0,te.registerStore)(Mu,{reducer:(e={errors:{},requesting:{},exportMeta:{},exportIds:{}},t)=>{switch(t.type){case Ku.SET_IS_REQUESTING:return{...e,requesting:{...e.requesting,[t.selector]:{...e.requesting[t.selector],[qu(t.selectorArgs)]:t.isRequesting}}};case Ku.SET_EXPORT_ID:const{exportType:r,exportArgs:o,exportId:n}=t;return{...e,exportMeta:{...e.exportMeta,[n]:{exportType:r,exportArgs:o}},exportIds:{...e.exportIds,[r]:{...e.exportIds[r],[qu({type:r,args:o})]:n}}};case Ku.SET_ERROR:return{...e,errors:{...e.errors,[t.selector]:{...e.errors[t.selector],[qu(t.selectorArgs)]:t.error}}};default:return e}},actions:z,controls:ce,selectors:B});const Wu=Mu,Ju="wc/admin/import",Bu=e=>{const{activeImport:t,lastImportStartTimestamp:r}=e;return{activeImport:t,lastImportStartTimestamp:r}||{}},zu=e=>{const{period:t,skipPrevious:r}=e;return{period:t,skipPrevious:r}||{}},Zu=(e,t)=>{const r=JSON.stringify(t);return e.importStatus[r]||{}},Xu=(e,t)=>{const{importTotals:r,lastImportStartTimestamp:o}=e;return{...r[JSON.stringify(t)],lastImportStartTimestamp:o}||{}},el=(e,t)=>{const r=JSON.stringify(t);return e.errors[r]||!1},tl={SET_IMPORT_DATE:"SET_IMPORT_DATE",SET_IMPORT_ERROR:"SET_IMPORT_ERROR",SET_IMPORT_PERIOD:"SET_IMPORT_PERIOD",SET_IMPORT_STARTED:"SET_IMPORT_STARTED",SET_IMPORT_STATUS:"SET_IMPORT_STATUS",SET_IMPORT_TOTALS:"SET_IMPORT_TOTALS",SET_SKIP_IMPORTED:"SET_SKIP_IMPORTED"};function rl(e){return{type:tl.SET_IMPORT_STARTED,activeImport:e}}function ol(e,t){return t?{type:tl.SET_IMPORT_DATE,date:e}:{type:tl.SET_IMPORT_PERIOD,date:e}}function nl(e){return{type:tl.SET_SKIP_IMPORTED,skipPrevious:e}}function sl(e,t){return{type:tl.SET_IMPORT_STATUS,importStatus:t,query:e}}function il(e,t){return{type:tl.SET_IMPORT_TOTALS,importTotals:t,query:e}}function al(e,t){return{type:tl.SET_IMPORT_ERROR,error:t,query:e}}function*cl(e,t=!1){yield rl(t);try{return yield(0,re.apiFetch)({path:e,method:"POST"})}catch(t){throw yield al(e,t),t}}function*ul(e){try{const t=(0,ne.addQueryArgs)(`${Ue}/reports/import/status`,"object"==typeof e?(0,Ce.omit)(e,["timestamp"]):{}),r=yield(0,re.apiFetch)({path:t});yield sl(e,r)}catch(t){yield al(e,t)}}function*ll(e){try{const t=(0,ne.addQueryArgs)(`${Ue}/reports/import/totals`,e),r=yield(0,re.apiFetch)({path:t});yield il(e,r)}catch(t){yield al(e,t)}}(0,te.registerStore)(Ju,{reducer:(e={activeImport:!1,importStatus:{},importTotals:{},errors:{},lastImportStartTimestamp:0,period:{date:Au()().format((0,Ie.__)("MM/DD/YYYY","woocommerce")),label:"all"},skipPrevious:!0},t)=>{switch(t.type){case tl.SET_IMPORT_STARTED:const{activeImport:r}=t;e={...e,activeImport:r,lastImportStartTimestamp:r?Date.now():e.lastImportStartTimestamp};break;case tl.SET_IMPORT_PERIOD:e={...e,period:{...e.period,label:t.date},activeImport:!1};break;case tl.SET_IMPORT_DATE:e={...e,period:{date:t.date,label:"custom"},activeImport:!1};break;case tl.SET_SKIP_IMPORTED:e={...e,skipPrevious:t.skipPrevious,activeImport:!1};break;case tl.SET_IMPORT_STATUS:const{query:o,importStatus:n}=t;e={...e,importStatus:{...e.importStatus,[JSON.stringify(o)]:n},errors:{...e.errors,[JSON.stringify(o)]:!1}};break;case tl.SET_IMPORT_TOTALS:e={...e,importTotals:{...e.importTotals,[JSON.stringify(t.query)]:t.importTotals}};break;case tl.SET_IMPORT_ERROR:e={...e,errors:{...e.errors,[JSON.stringify(t.query)]:t.error}}}return e},actions:X,controls:re.controls,selectors:Z,resolvers:ee});const El=Ju,dl=["average_rating","backordered","backorders_allowed","date_created","date_created_gmt","date_modified","date_modified_gmt","generated_slug","id","on_sale","permalink","permalink_template","price","price_html","purchasable","rating_count","related_ids","shipping_class_id","shipping_required","shipping_taxable","total_sales","variations"]})(),(window.wc=window.wc||{}).data=o})();
"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[0,6732],{86053:(e,t,o)=>{o.d(t,{U:()=>w,e:()=>_});var n=o(69307),r=o(7829),c=o(10130),a=o(76292),s=o.n(a),i=o(7862),l=o.n(i),m=o(86020),d=o(55609),u=o(92819);class p extends n.Component{render(){const{className:e,hasAction:t,hasDate:o,hasSubtitle:c,lines:a}=this.props,s=(0,r.Z)("woocommerce-activity-card is-loading",e);return(0,n.createElement)("div",{className:s,"aria-hidden":!0},(0,n.createElement)("span",{className:"woocommerce-activity-card__icon"},(0,n.createElement)("span",{className:"is-placeholder"})),(0,n.createElement)("div",{className:"woocommerce-activity-card__header"},(0,n.createElement)("div",{className:"woocommerce-activity-card__title is-placeholder"}),c&&(0,n.createElement)("div",{className:"woocommerce-activity-card__subtitle is-placeholder"}),o&&(0,n.createElement)("div",{className:"woocommerce-activity-card__date"},(0,n.createElement)("span",{className:"is-placeholder"}))),(0,n.createElement)("div",{className:"woocommerce-activity-card__body"},(0,u.range)(a).map((e=>(0,n.createElement)("span",{className:"is-placeholder",key:e})))),t&&(0,n.createElement)("div",{className:"woocommerce-activity-card__actions"},(0,n.createElement)("span",{className:"is-placeholder"})))}}p.propTypes={className:l().string,hasAction:l().bool,hasDate:l().bool,hasSubtitle:l().bool,lines:l().number},p.defaultProps={hasAction:!1,hasDate:!1,hasSubtitle:!1,lines:1};const _=p;class w extends n.Component{getCard(){const{actions:e,className:t,children:o,date:c,icon:a,subtitle:i,title:l,unread:d}=this.props,u=(0,r.Z)("woocommerce-activity-card",t),p=Array.isArray(e)?e:[e],_=/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(c)?s().utc(c).fromNow():c;return(0,n.createElement)("section",{className:u},d&&(0,n.createElement)("span",{className:"woocommerce-activity-card__unread"}),a&&(0,n.createElement)("span",{className:"woocommerce-activity-card__icon","aria-hidden":!0},a),l&&(0,n.createElement)("header",{className:"woocommerce-activity-card__header"},(0,n.createElement)(m.H,{className:"woocommerce-activity-card__title"},l),i&&(0,n.createElement)("div",{className:"woocommerce-activity-card__subtitle"},i),_&&(0,n.createElement)("span",{className:"woocommerce-activity-card__date"},_)),o&&(0,n.createElement)(m.Section,{className:"woocommerce-activity-card__body"},o),e&&(0,n.createElement)("footer",{className:"woocommerce-activity-card__actions"},p.map(((e,t)=>(0,n.cloneElement)(e,{key:t})))))}render(){const{onClick:e}=this.props;return e?(0,n.createElement)(d.Button,{className:"woocommerce-activity-card__button",onClick:e},this.getCard()):this.getCard()}}w.propTypes={actions:l().oneOfType([l().arrayOf(l().element),l().element]),onClick:l().func,className:l().string,children:l().node,date:l().string,icon:l().node,subtitle:l().node,title:l().oneOfType([l().string,l().node]),unread:l().bool},w.defaultProps={icon:(0,n.createElement)(c.Z,{size:48}),unread:!1}},90353:(e,t,o)=>{o.d(t,{Z:()=>m});var n=o(69307),r=o(7829),c=o(7862),a=o.n(c),s=o(14812),i=o(86020);class l extends n.Component{render(){const{className:e,menu:t,subtitle:o,title:c,unreadMessages:a}=this.props,i=(0,r.Z)({"woocommerce-layout__inbox-panel-header":o,"woocommerce-layout__activity-panel-header":!o},e),l=a||0;return(0,n.createElement)("div",{className:i},(0,n.createElement)("div",{className:"woocommerce-layout__inbox-title"},(0,n.createElement)(s.Text,{size:16,weight:600,color:"#23282d"},c),(0,n.createElement)(s.Text,{variant:"button",weight:"600",size:"14",lineHeight:"20px"},l>0&&(0,n.createElement)("span",{className:"woocommerce-layout__inbox-badge"},a))),(0,n.createElement)("div",{className:"woocommerce-layout__inbox-subtitle"},o&&(0,n.createElement)(s.Text,{variant:"body.small",size:"14",lineHeight:"20px"},o)),t&&(0,n.createElement)("div",{className:"woocommerce-layout__activity-panel-header-menu"},t))}}l.propTypes={className:a().string,unreadMessages:a().number,title:a().string.isRequired,subtitle:a().string,menu:a().shape({type:a().oneOf([i.EllipsisMenu])})};const m=l},43631:(e,t,o)=>{o.r(t),o.d(t,{HelpPanel:()=>y,SETUP_TASK_HELP_ITEMS_FILTER:()=>E,default:()=>v});var n=o(69307),r=o(65736),c=o(14812),a=o(9818),s=o(92694),i=o(23374),l=o(89015),m=o(83619),d=o(92819),u=o(86020),p=o(67221),_=o(2031),w=o(14599),h=o(90353),g=o(24082);const E="woocommerce_admin_setup_task_help_items";function k(e,t){const{taskName:o}=e;t&&e.recordEvent("help_panel_click",{task_name:o||"homescreen",link:t.currentTarget.href})}const y=({taskName:e,recordEvent:t=w.recordEvent,...o})=>{(0,n.useEffect)((()=>{t("help_panel_open",{task_name:e||"homescreen"})}),[e,t]);const a=function(e){const t=function(e){const{taskName:t}=e;switch(t){case"products":return[{title:(0,r.__)("Adding and Managing Products","woocommerce"),link:"https://woocommerce.com/document/managing-products/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Import products using the CSV Importer and Exporter","woocommerce"),link:"https://woocommerce.com/document/product-csv-importer-exporter/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Migrate products using Cart2Cart","woocommerce"),link:"https://woocommerce.com/products/cart2cart/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Learn more about setting up products","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/setup-products/?utm_source=help_panel&utm_medium=product"}];case"appearance":return[{title:(0,r.__)("Showcase your products and tailor your shopping experience using Blocks","woocommerce"),link:"https://woocommerce.com/document/woocommerce-blocks/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Manage Store Notice, Catalog View and Product Images","woocommerce"),link:"https://woocommerce.com/document/woocommerce-customizer/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("How to choose and change a theme","woocommerce"),link:"https://woocommerce.com/document/choose-change-theme/?utm_source=help_panel&utm_medium=product"}];case"shipping":return function({activePlugins:e,countryCode:t}){const o="US"===t&&!e.includes("woocommerce-services")&&!e.includes("woocommerce-shipping")&&!e.includes("woocommerce-tax");return[{title:(0,r.__)("Setting up Shipping Zones","woocommerce"),link:"https://woocommerce.com/document/setting-up-shipping-zones/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Core Shipping Options","woocommerce"),link:"https://woocommerce.com/documentation/plugins/woocommerce/getting-started/shipping/core-shipping-options/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Product Shipping Classes","woocommerce"),link:"https://woocommerce.com/document/product-shipping-classes/?utm_source=help_panel&utm_medium=product"},o&&{title:(0,r.__)("WooCommerce Shipping setup and configuration","woocommerce"),link:"https://woocommerce.com/document/woocommerce-shipping-and-tax/?utm_source=help_panel&utm_medium=product#section-3"},{title:(0,r.__)("Learn more about configuring your shipping settings","woocommerce"),link:"https://woocommerce.com/document/plugins/woocommerce/getting-started/shipping/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"tax":return function(e){const{countryCode:t,taskLists:o}=e,n=o.reduce(((e,t)=>[...e,...t.tasks]),[]).find((e=>"tax"===e.id));if(!n)return;const{additionalData:c}=n,{woocommerceTaxCountries:a=[],taxJarActivated:s,woocommerceTaxActivated:i,woocommerceShippingActivated:l}=c,m=!s&&a.includes(t)&&!i&&!l;return[{title:(0,r.__)("Setting up Taxes in WooCommerce","woocommerce"),link:"https://woocommerce.com/document/setting-up-taxes-in-woocommerce/?utm_source=help_panel&utm_medium=product"},m&&{title:(0,r.__)("Automated Tax calculation using WooCommerce Tax","woocommerce"),link:"https://woocommerce.com/document/woocommerce-services/?utm_source=help_panel&utm_medium=product#section-10"}].filter(Boolean)}(e);case"payments":return function(e){const{paymentGatewaySuggestions:t}=e;return[{title:(0,r.__)("Which Payment Option is Right for Me?","woocommerce"),link:"https://woocommerce.com/document/premium-payment-gateway-extensions/?utm_source=help_panel&utm_medium=product"},t.woocommerce_payments&&{title:(0,r.__)("WooPayments Start Up Guide","woocommerce"),link:"https://woocommerce.com/document/payments/?utm_source=help_panel&utm_medium=product"},t.woocommerce_payments&&{title:(0,r.__)("WooPayments FAQs","woocommerce"),link:"https://woocommerce.com/documentation/woocommerce-payments/woocommerce-payments-faqs/?utm_source=help_panel&utm_medium=product"},t.stripe&&{title:(0,r.__)("Stripe Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/stripe/?utm_source=help_panel&utm_medium=product"},t["ppcp-gateway"]&&{title:(0,r.__)("PayPal Checkout Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/2-0/woocommerce-paypal-payments/?utm_medium=product#section-3"},t.square_credit_card&&{title:(0,r.__)("Square - Get started","woocommerce"),link:"https://woocommerce.com/document/woocommerce-square/?utm_source=help_panel&utm_medium=product"},t.kco&&{title:(0,r.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-checkout/?utm_source=help_panel&utm_medium=product"},t.klarna_payments&&{title:(0,r.__)("Klarna - Introduction","woocommerce"),link:"https://woocommerce.com/document/klarna-payments/?utm_source=help_panel&utm_medium=product"},t.payfast&&{title:(0,r.__)("Payfast Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/payfast-payment-gateway/?utm_source=help_panel&utm_medium=product"},t.eway&&{title:(0,r.__)("Eway Setup and Configuration","woocommerce"),link:"https://woocommerce.com/document/eway/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Direct Bank Transfer (BACS)","woocommerce"),link:"https://woocommerce.com/document/bacs/?utm_source=help_panel&utm_medium=product"},{title:(0,r.__)("Cash on Delivery","woocommerce"),link:"https://woocommerce.com/document/cash-on-delivery/?utm_source=help_panel&utm_medium=product"}].filter(Boolean)}(e);case"marketing":return function(e){const{activePlugins:t}=e;return[t.includes("mailpoet")&&{title:(0,r.__)("Get started with Mailpoet","woocommerce"),link:"https://kb.mailpoet.com/category/114-getting-started"},t.includes("google-listings-and-ads")&&{title:(0,r.__)("Set up Google for WooCommerce","woocommerce"),link:"https://woocommerce.com/document/google-listings-and-ads/?utm_medium=product#get-started"},t.includes("pinterest-for-woocommerce")&&{title:(0,r.__)("Set up Pinterest for WooCommerce","woocommerce"),link:"https://woocommerce.com/products/pinterest-for-woocommerce/"},t.includes("mailchimp-for-woocommerce")&&{title:(0,r.__)("Connect Mailchimp for WooCommerce","woocommerce"),link:"https://mailchimp.com/help/connect-or-disconnect-mailchimp-for-woocommerce/"},t.includes("creative-mail-by-constant-contact")&&{title:(0,r.__)("Set up Creative Mail for WooCommerce","woocommerce"),link:"https://app.creativemail.com/kb/help/WooCommerce"}].filter(Boolean)}(e);default:return[{title:(0,r.__)("Get Support","woocommerce"),link:"https://woocommerce.com/my-account/create-a-ticket/?utm_medium=product"},{title:(0,r.__)("Home Screen","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product"},{title:(0,r.__)("Inbox","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-4"},{title:(0,r.__)("Stats Overview","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-5"},{title:(0,r.__)("Store Management","woocommerce"),link:"https://woocommerce.com/document/home-screen/?utm_medium=product#section-10"},{title:(0,r.__)("Store Setup Checklist","woocommerce"),link:"https://woocommerce.com/document/woocommerce-setup-wizard?utm_medium=product#store-setup-checklist"}]}}(e),o={title:(0,r.__)("WooCommerce Docs","woocommerce"),link:"https://woocommerce.com/documentation/?utm_source=help_panel&utm_medium=product"};t.push(o);const a=(0,s.applyFilters)(E,t,e.taskName,e);let u=Array.isArray(a)?a.filter((e=>e instanceof Object&&e.title&&e.link)):[];u.length||(u=[o]);const p=(0,d.partial)(k,e);return u.map((e=>{var t,o;return{title:(0,n.createElement)(c.Text,{as:"div",variant:"button",weight:"600",size:"14",lineHeight:"20px"},e.title),before:(0,n.createElement)(i.Z,{icon:l.Z}),after:(0,n.createElement)(i.Z,{icon:m.Z}),linkType:null!==(t=e.linkType)&&void 0!==t?t:"external",target:null!==(o=e.target)&&void 0!==o?o:"_blank",href:e.link,onClick:p}}))}({taskName:e,recordEvent:t,...o});return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(h.Z,{title:(0,r.__)("Documentation","woocommerce")}),(0,n.createElement)(u.Section,null,(0,n.createElement)(u.List,{items:a,className:"woocommerce-quick-links__list"})))},v=(0,_.qC)((0,a.withSelect)((e=>{const{getSettings:t}=e(p.SETTINGS_STORE_NAME),{getActivePlugins:o}=e(p.PLUGINS_STORE_NAME),{general:n={}}=t("general"),r=o(),c=e(p.ONBOARDING_STORE_NAME).getPaymentGatewaySuggestions().reduce(((e,t)=>{const{id:o}=t;return e[o]=!0,e}),{}),a=e(p.ONBOARDING_STORE_NAME).getTaskLists();return{activePlugins:r,countryCode:(0,g.so)(n.woocommerce_default_country),paymentGatewaySuggestions:c,taskLists:a}})))(y)},78855:(e,t,o)=>{o.d(t,{A:()=>u,Z:()=>d});var n=o(76292),r=o.n(n),c=o(92819),a=o(81921),s=o(67221),i=o(10431),l=o(81595),m=o(74617);const d=({indicator:e,primaryData:t,secondaryData:o,currency:n,formatAmount:r,persistedQuery:a})=>{const s=(0,c.find)(t.data,(t=>t.stat===e.stat)),d=(0,c.find)(o.data,(t=>t.stat===e.stat));if(!s||!d)return{};const u=s._links&&s._links.report[0]&&s._links.report[0].href||"",p=function(e,t,o){return e?"/jetpack"===e?(0,m.getAdminLink)("admin.php?page=jetpack#/dashboard"):(0,i.getNewPath)(t,e,{chart:o.chart}):""}(u,a,s),_="/jetpack"===u?"wp-admin":"wc-admin",w="currency"===s.format,h=(0,l.calculateDelta)(s.value,d.value);return{primaryValue:w?r(s.value):(0,l.formatValue)(n,s.format,s.value),secondaryValue:w?r(d.value):(0,l.formatValue)(n,d.format,d.value),delta:h,reportUrl:p,reportUrlType:_}},u=(e,t,o,n)=>{const{getReportItems:c,getReportItemsError:i,isResolving:l}=e(s.REPORTS_STORE_NAME),{woocommerce_default_date_range:m}=e(s.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings"),d=(0,a.getCurrentDates)(o,m),u=d.primary.before,p=d.secondary.before,_=t.map((e=>e.stat)).join(","),w=(0,s.getFilterQuery)({filters:n,query:o}),h={...w,after:(0,a.appendTimestamp)(d.primary.after,"start"),before:(0,a.appendTimestamp)(u,u.isSame(r()(),"day")?"now":"end"),stats:_},g={...w,after:(0,a.appendTimestamp)(d.secondary.after,"start"),before:(0,a.appendTimestamp)(p,p.isSame(r()(),"day")?"now":"end"),stats:_};return{primaryData:c("performance-indicators",h),primaryError:i("performance-indicators",h)||null,primaryRequesting:l("getReportItems",["performance-indicators",h]),secondaryData:c("performance-indicators",g),secondaryError:i("performance-indicators",g)||null,secondaryRequesting:l("getReportItems",["performance-indicators",g]),defaultDateRange:m}}},53332:(e,t,o)=>{o.d(t,{ZP:()=>ct,tv:()=>nt});var n=o(69307),r=o(94333),c=o(9818),a=o(7829),s=o(7862),i=o.n(s),l=o(67221),m=o(65736),d=o(90353),u=o(88533),p=o(86020),_=o(55609),w=o(14599),h=o(92819),g=o(53644),E=o(22629),k=o(76357),y=o(10431),v=o(74617),f=o(17844),C=o(86053),b=o(17062);function S(e){(0,w.recordEvent)(`activity_panel_orders_${e}`,{})}function N({unreadOrdersCount:e,orderStatuses:t}){const o=(0,n.useMemo)((()=>({page:1,per_page:5,status:t,_fields:["id","number","currency","status","total","customer","line_items","customer_id","date_created_gmt"]})),[t]),r=(0,n.useContext)(f.CurrencyContext),a=r.getCurrencyConfig(),{currencySymbols:s={}}=(0,b.O3)("onboarding",{}),{orders:i=[],isRequesting:d,isError:u,customerItems:_}=(0,c.useSelect)((n=>{const{getOrders:r,hasFinishedResolution:c,getOrdersError:a}=n(l.ORDERS_STORE_NAME),{getItems:s}=n(l.ITEMS_STORE_NAME);if(!t.length&&0===e)return{isRequesting:!1};const i=r(o,null),m=c("getOrders",[o]);if(m||null===e||null===i)return{isError:Boolean(a(o)),isRequesting:!0,orderStatuses:t};const d=s("customers",{users:i.map((e=>e.customer_id)).filter((e=>0!==e)),_fields:["id","name","country","user_id"]});return{orders:i,isError:Boolean(a(i)),isRequesting:m,orderStatuses:t,customerItems:d}}));if(u){if(!t.length&&window.wcAdminFeatures.analytics)return(0,n.createElement)(p.EmptyContent,{title:(0,m.__)("You currently don’t have any actionable statuses. To display orders here, select orders that require further review in settings.","woocommerce"),actionLabel:(0,m.__)("Settings","woocommerce"),actionURL:(0,v.getAdminLink)("admin.php?page=wc-admin&path=/analytics/settings")});throw new Error("Failed to load orders, raise error to trigger ErrorBoundary")}const w=_?Array.from(_,(([,e])=>e)):[];return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(p.Section,null,d?(0,n.createElement)(C.e,{className:"woocommerce-order-activity-card",hasAction:!0,hasDate:!0,lines:1}):function(e,t,o){if(0===e.length)return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(C.U,{className:"woocommerce-empty-activity-card",title:"",icon:""},(0,n.createElement)("span",{className:"woocommerce-order-empty__success-icon",role:"img","aria-labelledby":"woocommerce-order-empty-message"},"🎉"),(0,n.createElement)(p.H,{id:"woocommerce-order-empty-message"},(0,m.__)("You’ve fulfilled all your orders","woocommerce"))),(0,n.createElement)(p.Link,{href:"edit.php?post_type=shop_order",onClick:()=>S("orders_manage"),className:"woocommerce-layout__activity-panel-outbound-link woocommerce-layout__activity-panel-empty",type:"wp-admin"},(0,m.__)("Manage all orders","woocommerce")));const r=e=>{const{name:t}=e||{};return t?`{{customerLink}}${t}{{/customerLink}}`:""},c=e=>{const{id:o,number:c,customer_id:a}=e,s=t.find((e=>e.user_id===a))||{};let i=null;return s&&s.id&&(i=window.wcAdminFeatures.analytics?(0,y.getNewPath)({},"/analytics/customers",{filter:"single_customer",customers:s.id}):(0,v.getAdminLink)("user-edit.php?user_id="+s.id)),(0,n.createElement)(n.Fragment,null,(0,k.Z)({mixedString:(0,m.sprintf)((0,m.__)("{{orderLink}}Order #%(orderNumber)s{{/orderLink}} %(customerString)s","woocommerce"),{orderNumber:c,customerString:r(s)}),components:{orderLink:(0,n.createElement)(p.Link,{href:(0,v.getAdminLink)("post.php?action=edit&post="+o),onClick:()=>S("order_number"),type:"wp-admin"}),destinationFlag:s&&s.country?(0,n.createElement)(p.Flag,{code:s&&s.country,round:!1}):null,customerLink:i?(0,n.createElement)(p.Link,{href:i,onClick:()=>S("customer_name"),type:"wc-admin"}):(0,n.createElement)("span",null)}}))},a=[];return e.forEach((e=>{const{date_created_gmt:t,line_items:r,id:s}=e,i=r?r.length:0;a.push((0,n.createElement)(C.U,{key:s,className:"woocommerce-order-activity-card",title:c(e),date:t,onClick:({target:e})=>{S("orders_begin_fulfillment"),e.href||(window.location.href=(0,v.getAdminLink)(`post.php?action=edit&post=${s}`))},subtitle:(0,n.createElement)("div",null,(0,n.createElement)("span",null,(0,m.sprintf)((0,m._n)("%d product","%d products",i,"woocommerce"),i)),(0,n.createElement)("span",null,o(e.total,e.currency)))},(0,n.createElement)(p.OrderStatus,{order:e,orderStatusMap:(0,b.O3)("orderStatuses",{})})))})),(0,n.createElement)(n.Fragment,null,a,(0,n.createElement)(p.Link,{href:"edit.php?post_type=shop_order",className:"woocommerce-layout__activity-panel-outbound-link",onClick:()=>S("orders_manage"),type:"wp-admin"},(0,m.__)("Manage all orders","woocommerce")))}(i,w,((e,t)=>{if(!t)return null;if(a&&a.code===t)return r.formatAmount(e);const o=s[t];return o?(0,f.CurrencyFactory)({...a,symbol:(0,E.decodeEntities)(o),code:t}).formatAmount(e):`${t}${e}`}))))}N.propTypes={unreadOrdersCount:i().number,orderStatuses:i().array};const T=N;var R=o(39630),A=o(76292),L=o.n(A);class P extends n.Component{constructor(e){super(e),this.state={quantity:e.product.stock_quantity,editing:!1,edited:!1},this.beginEdit=this.beginEdit.bind(this),this.cancelEdit=this.cancelEdit.bind(this),this.onQuantityChange=this.onQuantityChange.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.onSubmit=this.onSubmit.bind(this)}recordStockEvent(e,t={}){(0,w.recordEvent)(`activity_panel_stock_${e}`,t)}beginEdit(){const{product:e}=this.props;this.setState({editing:!0,quantity:e.stock_quantity},(()=>{this.quantityInput&&this.quantityInput.focus()})),this.recordStockEvent("update_stock")}cancelEdit(){const{product:e}=this.props;this.setState({editing:!1,quantity:e.stock_quantity}),this.recordStockEvent("cancel")}handleKeyDown(e){e.keyCode===R.ESCAPE&&this.cancelEdit()}onQuantityChange(e){this.setState({quantity:e.target.value})}async onSubmit(){const{product:e,updateProductStock:t,createNotice:o}=this.props,n=parseInt(this.state.quantity,10);e.stock_quantity!==n?(this.setState({editing:!1,edited:!0}),await t(e,n)?o("success",(0,m.sprintf)((0,m.__)("%s stock updated","woocommerce"),e.name),{actions:[{label:(0,m.__)("Undo","woocommerce"),onClick:()=>{t(e,e.stock_quantity),this.recordStockEvent("undo")}}]}):o("error",(0,m.sprintf)((0,m.__)("%s stock could not be updated","woocommerce"),e.name)),this.recordStockEvent("save",{quantity:n})):this.setState({editing:!1})}getActions(){const{editing:e}=this.state;return e?[(0,n.createElement)(_.Button,{key:"save",type:"submit",isPrimary:!0},(0,m.__)("Save","woocommerce")),(0,n.createElement)(_.Button,{key:"cancel",type:"reset"},(0,m.__)("Cancel","woocommerce"))]:[(0,n.createElement)(_.Button,{key:"update",isSecondary:!0,onClick:this.beginEdit},(0,m.__)("Update stock","woocommerce"))]}getBody(){const{product:e}=this.props,{editing:t,quantity:o}=this.state;return t?(0,n.createElement)(n.Fragment,null,(0,n.createElement)(_.BaseControl,{className:"woocommerce-stock-activity-card__edit-quantity"},(0,n.createElement)("input",{className:"components-text-control__input",type:"number",value:o,onKeyDown:this.handleKeyDown,onChange:this.onQuantityChange,ref:e=>{this.quantityInput=e}})),(0,n.createElement)("span",null,(0,m.__)("in stock","woocommerce"))):(0,n.createElement)("span",{className:(0,a.Z)("woocommerce-stock-activity-card__stock-quantity",{"out-of-stock":e.stock_quantity<1})},(0,m.sprintf)((0,m.__)("%d in stock","woocommerce"),e.stock_quantity))}render(){const{product:e}=this.props,{edited:t,editing:o}=this.state,r=(0,b.O3)("notifyLowStockAmount",0),c=Number.isFinite(e.low_stock_amount)?e.low_stock_amount:r,s=e.stock_quantity<=c,i=e.last_order_date?(0,m.sprintf)((0,m.__)("Last ordered %s","woocommerce"),L().utc(e.last_order_date).fromNow()):null;if(!s&&!t)return null;const l=(0,n.createElement)(p.Link,{href:"post.php?action=edit&post="+(e.parent_id||e.id),onClick:()=>this.recordStockEvent("product_name"),type:"wp-admin"},e.name);let d=null;"variation"===e.type&&(d=Object.values(e.attributes).map((e=>e.option)).join(", "));const u=(0,h.get)(e,["images",0])||(0,h.get)(e,["image"]),_=(0,a.Z)("woocommerce-stock-activity-card__image-overlay__product",{"is-placeholder":!u||!u.src}),w=(0,n.createElement)("div",{className:"woocommerce-stock-activity-card__image-overlay"},(0,n.createElement)("div",{className:_},(0,n.createElement)(p.ProductImage,{product:e}))),g=(0,a.Z)("woocommerce-stock-activity-card",{"is-dimmed":!o&&!s}),E=(0,n.createElement)(C.U,{className:g,title:l,subtitle:d,icon:w,date:i,actions:this.getActions()},this.getBody());return o?(0,n.createElement)("form",{onReset:this.cancelEdit,onSubmit:this.onSubmit},E):E}}const M={page:1,per_page:5,status:"publish",_fields:["attributes","id","images","last_order_date","low_stock_amount","name","parent_id","stock_quantity","type"]};class x extends n.Component{constructor(e){super(e),this.updateStock=this.updateStock.bind(this)}async updateStock(e,t){const{invalidateResolution:o,updateProductStock:n}=this.props,r=await n(e,t);return r&&(o("getItems",["products/low-in-stock",M]),o("getItemsTotalCount",["products/count-low-in-stock",g.Ox,null])),r}renderProducts(){const{products:e,createNotice:t}=this.props;return e.map((e=>(0,n.createElement)(P,{key:e.id,product:e,updateProductStock:this.updateStock,createNotice:t})))}render(){const{lowStockProductsCount:e,isError:t,isRequesting:o,products:r}=this.props;if(t)throw new Error("Failed to load low stock products, Raise error to trigger ErrorBoundary");if(o||!r.length){const t=Math.min(5,null!=e?e:1),o=Array.from(new Array(t)).map(((e,t)=>(0,n.createElement)(C.e,{key:t,className:"woocommerce-stock-activity-card",hasAction:!0,lines:1})));return(0,n.createElement)(p.Section,null,o)}return(0,n.createElement)(p.Section,null,this.renderProducts())}}x.propTypes={lowStockProductsCount:i().number,products:i().array.isRequired,isError:i().bool,isRequesting:i().bool},x.defaultProps={products:[],isError:!1,isRequesting:!1};const I=(0,r.compose)((0,c.withSelect)((e=>{const{getItems:t,getItemsError:o,isResolving:n}=e(l.ITEMS_STORE_NAME);return{products:Array.from(t("products/low-in-stock",M).values()),isError:Boolean(o("products/low-in-stock",M)),isRequesting:n("getItems",["products/low-in-stock",M])}})),(0,c.withDispatch)((e=>{const{invalidateResolution:t,updateProductStock:o}=e(l.ITEMS_STORE_NAME),{createNotice:n}=e("core/notices");return{createNotice:n,invalidateResolution:t,updateProductStock:o}})))(x);var O=o(28870),U=o(41043);const D=()=>(0,n.createElement)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,n.createElement)("mask",{id:"mask0",style:"mask-type:alpha",maskUnits:"userSpaceOnUse",x:"1",y:"1",width:"14",height:"14"},(0,n.createElement)("path",{d:"M7.99992 1.33301C4.31992 1.33301 1.33325 4.31967 1.33325 7.99967C1.33325 11.6797 4.31992 14.6663 7.99992 14.6663C11.6799 14.6663 14.6666 11.6797 14.6666 7.99967C14.6666 4.31967 11.6799 1.33301 7.99992 1.33301ZM7.99992 13.333C5.05992 13.333 2.66659 10.9397 2.66659 7.99967C2.66659 5.05967 5.05992 2.66634 7.99992 2.66634C10.9399 2.66634 13.3333 5.05967 13.3333 7.99967C13.3333 10.9397 10.9399 13.333 7.99992 13.333ZM6.66658 9.44634L11.0599 5.05301L11.9999 5.99967L6.66658 11.333L3.99992 8.66634L4.93992 7.72634L6.66658 9.44634Z",fill:"white"})),(0,n.createElement)("g",{mask:"url(#mask0)"},(0,n.createElement)("rect",{width:"16",height:"16",fill:"#4AB866"})));var Z=o(346),q=o(37873);const F={page:1,per_page:q.X6,status:"hold",_embed:1};class B extends n.Component{recordReviewEvent(e,t){(0,w.recordEvent)(`reviews_${e}`,t||{})}deleteReview(e){const{deleteReview:t,createNotice:o,updateReview:n,clearReviewsCache:r}=this.props;e&&t(e).then((()=>{r(),o("success",(0,m.__)("Review successfully deleted.","woocommerce"),{actions:[{label:(0,m.__)("Undo","woocommerce"),onClick:()=>{n(e,{status:"untrash"},{_embed:1}).then((()=>r()))}}]})})).catch((()=>{o("error",(0,m.__)("Review could not be deleted.","woocommerce"))}))}updateReviewStatus(e,t,o){const{createNotice:n,updateReview:r,clearReviewsCache:c}=this.props;e&&r(e,{status:t}).then((()=>{c(),n("success",(0,m.__)("Review successfully updated.","woocommerce"),{actions:[{label:(0,m.__)("Undo","woocommerce"),onClick:()=>{r(e,{status:o},{_embed:1}).then((()=>c()))}}]})})).catch((()=>{n("error",(0,m.__)("Review could not be updated.","woocommerce"))}))}renderReview(e){const t=e&&e._embedded&&e._embedded.up&&e._embedded.up[0]||null;if(e.isUpdating)return(0,n.createElement)(C.e,{key:e.id,className:"woocommerce-review-activity-card",hasAction:!0,hasDate:!0,lines:1});if((0,h.isNull)(t)||e.status!==F.status)return null;const o=(0,k.Z)({mixedString:(0,m.sprintf)((0,m.__)("{{authorLink}}%1$s{{/authorLink}}{{verifiedCustomerIcon/}} reviewed {{productLink}}%2$s{{/productLink}}","woocommerce"),e.reviewer,t.name),components:{productLink:(0,n.createElement)(p.Link,{href:t.permalink,onClick:()=>this.recordReviewEvent("product"),type:"external"}),authorLink:(0,n.createElement)(p.Link,{href:(0,v.getAdminLink)("admin.php?page=wc-admin&path=%2Fcustomers&search="+e.reviewer),onClick:()=>this.recordReviewEvent("customer"),type:"external"}),verifiedCustomerIcon:e.verified?(0,n.createElement)("span",{className:"woocommerce-review-activity-card__verified"},(0,n.createElement)(_.Tooltip,{text:(0,m.__)("Verified owner","woocommerce")},(0,n.createElement)("span",null,(0,n.createElement)(D,null)))):null}}),r=(0,n.createElement)(n.Fragment,null,(0,n.createElement)(p.ReviewRating,{review:e,icon:U.Z,outlineIcon:O.Z,size:13})),c=(0,h.get)(t,["images",0])||(0,h.get)(t,["image"]),s=(0,a.Z)("woocommerce-review-activity-card__image-overlay__product",{"is-placeholder":!c||!c.src}),i=(0,n.createElement)("div",{className:"woocommerce-review-activity-card__image-overlay"},(0,n.createElement)("div",{className:s},(0,n.createElement)(p.ProductImage,{product:t,width:33,height:33}))),l={date:e.date_created_gmt,status:e.status},d=[(0,n.createElement)(_.Button,{key:"approve-action",isSecondary:!0,onClick:()=>{this.recordReviewEvent("approve",l),this.updateReviewStatus(e.id,"approved",e.status)}},(0,m.__)("Approve","woocommerce")),(0,n.createElement)(_.Button,{key:"spam-action",isTertiary:!0,onClick:()=>{this.recordReviewEvent("mark_as_spam",l),this.updateReviewStatus(e.id,"spam",e.status)}},(0,m.__)("Mark as spam","woocommerce")),(0,n.createElement)(_.Button,{key:"delete-action",isDestructive:!0,isTertiary:!0,onClick:()=>{this.recordReviewEvent("delete",l),this.deleteReview(e.id)}},(0,m.__)("Delete","woocommerce"))];return(0,n.createElement)(C.U,{className:"woocommerce-review-activity-card",key:e.id,title:o,subtitle:r,date:e.date_created_gmt,icon:i,actions:d},(0,n.createElement)("span",{dangerouslySetInnerHTML:(0,Z.ZP)(e.review)}))}renderReviews(e){const t=e.map((e=>this.renderReview(e,this.props)));return 0===t.filter(Boolean).length?(0,n.createElement)(n.Fragment,null):(0,n.createElement)(n.Fragment,null,t,(0,n.createElement)(p.Link,{href:(0,v.getAdminLink)("edit.php?post_type=product&page=product-reviews"),onClick:()=>this.recordReviewEvent("reviews_manage"),className:"woocommerce-layout__activity-panel-outbound-link woocommerce-layout__activity-panel-empty",type:"wp-admin"},(0,m.__)("Manage all reviews","woocommerce")))}render(){const{isRequesting:e,isError:t,reviews:o}=this.props;if(t)throw new Error("Failed to load reviews, Raise error to trigger ErrorBoundary");return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(p.Section,null,e||!o.length?(0,n.createElement)(C.e,{className:"woocommerce-review-activity-card",hasAction:!0,hasDate:!0,lines:1}):(0,n.createElement)(n.Fragment,null,this.renderReviews(o))))}}B.propTypes={reviews:i().array.isRequired,isError:i().bool,isRequesting:i().bool},B.defaultProps={reviews:[],isError:!1,isRequesting:!1},B.contextType=f.CurrencyContext;const H=(0,r.compose)([(0,c.withSelect)(((e,t)=>{const{hasUnapprovedReviews:o}=t,{getReviews:n,getReviewsError:r,isResolving:c}=e(l.REVIEWS_STORE_NAME);let a=[],s=!1,i=!1;return o&&(a=n(F),s=Boolean(r(F)),i=c("getReviews",[F])),{reviews:a,isError:s,isRequesting:i}})),(0,c.withDispatch)(((e,t)=>{const{deleteReview:o,updateReview:n,invalidateResolution:r}=e(l.REVIEWS_STORE_NAME),{createNotice:c}=e("core/notices");return{deleteReview:o,createNotice:c,updateReview:n,clearReviewsCache:()=>{r("getReviews",[F]),t.reviews&&t.reviews.length<2&&r("getReviewsTotalCount",[q.wY])}}}))])(B);var W=o(53736);const j={_fields:["id"]},z={status:"publish",_fields:["id"]},G=()=>{const e=(0,c.useSelect)((e=>{const{getOrdersTotalCount:t,hasFinishedResolution:o}=e(l.ORDERS_STORE_NAME),{getProductsTotalCount:n,hasFinishedResolution:r}=e(l.PRODUCTS_STORE_NAME),c=t(j,0),a=(0,g.sg)(e),s=(0,b.O3)("reviewsEnabled","no"),i=(0,g.xX)(e,a),m=(0,b.O3)("manageStock","no"),d=(0,g.ae)(e),u=(0,q.Vh)(e),p=n(z,0),_=!o("getOrdersTotalCount",[j,0])||!r("getProductsTotalCount",[z,0]),w=e(l.ONBOARDING_STORE_NAME).getTaskList("setup");return{loadingOrderAndProductCount:_,lowStockProductsCount:d,unapprovedReviewsCount:u,unreadOrdersCount:i,manageStock:m,isTaskListHidden:w?.isHidden,publishedProductCount:p,reviewsEnabled:s,totalOrderCount:c,orderStatuses:a}})),t=e.loadingOrderAndProductCount?[]:function({lowStockProductsCount:e,unapprovedReviewsCount:t,unreadOrdersCount:o,manageStock:r,isTaskListHidden:c,orderStatuses:a,publishedProductCount:s,reviewsEnabled:i,totalOrderCount:l}){return c?[l>0&&{className:"woocommerce-homescreen-card",count:o,collapsible:!0,id:"orders-panel",initialOpen:!1,panel:(0,n.createElement)(p.__experimentalErrorBoundary,{errorMessage:(0,n.createElement)(n.Fragment,null,(0,m.__)("There was an error getting your orders.","woocommerce"),(0,n.createElement)("br",null),(0,m.__)("Please try again.","woocommerce"))},(0,n.createElement)(T,{unreadOrdersCount:o,orderStatuses:a})),title:(0,m.__)("Orders","woocommerce")},l>0&&s>0&&"yes"===r&&{className:"woocommerce-homescreen-card",count:e,id:"stock-panel",initialOpen:!1,collapsible:0!==e,panel:(0,n.createElement)(p.__experimentalErrorBoundary,{errorMessage:(0,n.createElement)(n.Fragment,null,(0,m.__)("There was an error getting your low stock products.","woocommerce"),(0,n.createElement)("br",null),(0,m.__)("Please try again.","woocommerce"))},(0,n.createElement)(I,{lowStockProductsCount:e})),title:(0,m.__)("Stock","woocommerce")},s>0&&t>0&&"yes"===i&&{className:"woocommerce-homescreen-card",id:"reviews-panel",count:t,initialOpen:!1,collapsible:0!==t,panel:(0,n.createElement)(p.__experimentalErrorBoundary,{errorMessage:(0,n.createElement)(n.Fragment,null,(0,m.__)("There was an error getting your reviews.","woocommerce"),(0,n.createElement)("br",null),(0,m.__)("Please try again.","woocommerce"))},(0,n.createElement)(H,{hasUnapprovedReviews:t>0})),title:(0,m.__)("Reviews","woocommerce")}].filter(Boolean):[]}(e);if((0,n.useEffect)((()=>{if(void 0!==e.isTaskListHidden){const o=t.reduce(((e,t)=>(e[(0,h.snakeCase)(t.id)]=!0,e)),{task_list:e.isTaskListHidden});(0,w.recordEvent)("activity_panel_visible_panels",o)}}),[e.isTaskListHidden]),0===t.length)return null;const o=e=>{const{opened_panel:t}=(0,W.DP)(window.location.search);return e===t};return(0,n.createElement)(_.Panel,{className:"woocommerce-activity-panel"},t.map((e=>{const{className:t,count:r,id:c,initialOpen:a,panel:s,title:i,collapsible:l}=e;return l?(0,n.createElement)(_.PanelBody,{title:[(0,n.createElement)(_.__experimentalText,{key:i,variant:"title.small",size:"20",lineHeight:"28px"},i),null!==r&&(0,n.createElement)(p.Badge,{key:`${i}-badge`,count:r})],key:c,className:t,initialOpen:o(c)||a,collapsible:l,disabled:!l,onToggle:e=>{e&&(0,w.recordEvent)("activity_panel_open",{tab:c})}},(0,n.createElement)(_.PanelRow,null,s)):(0,n.createElement)("div",{className:"components-panel__body",key:c},(0,n.createElement)("h2",{className:"components-panel__body-title"},(0,n.createElement)(_.Button,{className:"components-panel__body-toggle","aria-expanded":!1,disabled:!0},(0,n.createElement)(_.__experimentalText,{variant:"title.small",size:"20",lineHeight:"28px"},i),null!==r&&(0,n.createElement)(p.Badge,{count:r}))))})))},V=({children:e,shouldStick:t=!1})=>{const[o,r]=(0,n.useState)(!1),c=(0,n.useRef)(null),a=(0,n.useRef)(null),s=(0,n.useCallback)((()=>{if(!c.current)return;const{bottom:e,top:t}=c.current.getBoundingClientRect();null===a.current&&(a.current=t);const o=e<window.innerHeight;t===a.current&&r(o)}),[]);return(0,n.useLayoutEffect)((()=>{if(t)return s(),window.addEventListener("resize",s),window.addEventListener("scroll",s),()=>{window.removeEventListener("resize",s),window.removeEventListener("scroll",s)}}),[s,t]),(0,n.createElement)("div",{className:"woocommerce-homescreen-column",ref:c,style:{position:t&&o?"sticky":"static"}},e)};var $=o(63724),J=o(14812),Y=o(92694);const K=(0,Y.applyFilters)("woocommerce_admin_homepage_default_stats",["revenue/total_sales","revenue/net_revenue","orders/orders_count","products/items_sold","jetpack/stats/visitors","jetpack/stats/views"]),Q=["revenue/net_revenue","products/items_sold"];var X=o(78855);const ee=(0,c.withSelect)(((e,{stats:t,query:o})=>(0,X.A)(e,t,o)))((({stats:e,primaryData:t,secondaryData:o,primaryRequesting:r,secondaryRequesting:c,primaryError:s,secondaryError:i,query:l})=>{const{formatAmount:d,getCurrencyConfig:u}=(0,n.useContext)(f.CurrencyContext);if(s||i)return null;const _=(0,y.getPersistedQuery)(l),h=u();return(0,n.createElement)("ul",{className:(0,a.Z)("woocommerce-stats-overview__stats",{"is-even":e.length%2==0})},e.map((e=>{if(r||c)return(0,n.createElement)(p.SummaryNumberPlaceholder,{key:e.stat});const{primaryValue:a,secondaryValue:s,delta:i,reportUrl:l,reportUrlType:u}=(0,X.Z)({indicator:e,primaryData:t,secondaryData:o,currency:h,formatAmount:d,persistedQuery:_});return(0,n.createElement)(p.SummaryNumber,{isHomescreen:!0,key:e.stat,href:l,hrefType:u,label:e.label,value:a,prevLabel:(0,m.__)("Previous period:","woocommerce"),prevValue:s,delta:i,onLinkClickCallback:()=>{(0,w.recordEvent)("statsoverview_indicators_click",{key:e.stat})}})})))}));var te=o(23418);(0,m.__)("Facebook for WooCommerce","woocommerce"),(0,m.__)("Jetpack","woocommerce"),(0,m.__)("Klarna Checkout for WooCommerce","woocommerce"),(0,m.__)("Klarna Payments for WooCommerce","woocommerce"),(0,m.__)("Mailchimp for WooCommerce","woocommerce"),(0,m.__)("Creative Mail for WooCommerce","woocommerce"),(0,m.__)("WooCommerce PayPal","woocommerce"),(0,m.__)("WooCommerce Stripe","woocommerce"),(0,m.__)("WooCommerce Payfast","woocommerce"),(0,m.__)("WooPayments","woocommerce"),(0,m.__)("WooCommerce Shipping & Tax","woocommerce"),(0,m.__)("WooCommerce Shipping & Tax","woocommerce"),(0,m.__)("WooCommerce Shipping & Tax","woocommerce"),(0,m.__)("WooCommerce ShipStation Gateway","woocommerce"),(0,m.__)("Mercado Pago payments for WooCommerce","woocommerce"),(0,m.__)("Google for WooCommerce","woocommerce"),(0,m.__)("Razorpay","woocommerce"),(0,m.__)("MailPoet","woocommerce"),(0,m.__)("Pinterest for WooCommerce","woocommerce"),(0,m.__)("TikTok for WooCommerce","woocommerce"),(0,m.__)("Omnichannel for WooCommerce","woocommerce");const oe=c.controls&&c.controls.dispatch?c.controls.dispatch:te.dispatch;c.controls&&c.controls.resolveSelect?c.controls.resolveSelect:te.select;const ne=e=>oe("core/notices","createNotice","error",e),re=({onClickInstall:e,onClickDismiss:t,isBusy:o,jetpackInstallState:r})=>(0,n.createElement)("article",{className:"woocommerce-stats-overview__install-jetpack-promo"},(0,n.createElement)("div",{className:"woocommerce-stats-overview__install-jetpack-promo__content"},(0,n.createElement)(p.H,null,(0,m.__)("Get traffic stats with Jetpack","woocommerce")),(0,n.createElement)("p",null,(0,m.__)("Keep an eye on your views and visitors metrics with Jetpack. Requires Jetpack plugin and a WordPress.com account.","woocommerce"))),(0,n.createElement)("footer",null,(0,n.createElement)(_.Button,{isSecondary:!0,onClick:()=>{(0,w.recordEvent)("statsoverview_install_jetpack"),e()},disabled:o,isBusy:o},(e=>({unavailable:(0,m.__)("Get Jetpack","woocommerce"),installed:(0,m.__)("Activate Jetpack","woocommerce"),activated:(0,m.__)("Connect Jetpack","woocommerce")}[e]||""))(r)),(0,n.createElement)(_.Button,{isTertiary:!0,onClick:()=>{(0,w.recordEvent)("statsoverview_dismiss_install_jetpack"),t()},disabled:o,isBusy:o},(0,m.__)("No thanks","woocommerce")))),ce=()=>{const{currentUserCan:e}=(0,l.useUser)(),{updateUserPreferences:t,...o}=(0,l.useUserPreferences)(),{canUserInstallPlugins:r,jetpackInstallState:a,isBusy:s}=(0,c.useSelect)((t=>{const{getPluginInstallState:o,isPluginsRequesting:n}=t(l.PLUGINS_STORE_NAME),r=o("jetpack");return{isBusy:n("getJetpackConnectUrl")||n("installPlugins")||n("activatePlugins"),jetpackInstallState:r,canUserInstallPlugins:e("install_plugins")}})),{installJetpackAndConnect:i}=(0,c.useDispatch)(l.PLUGINS_STORE_NAME);return r?(0,n.createElement)(re,{jetpackInstallState:a,isBusy:s,onClickInstall:()=>{i(ne,v.getAdminLink)},onClickDismiss:()=>{const e=o.homepage_stats||{};e.installJetpackDismissed=!0,t({homepage_stats:e})}}):null},{performanceIndicators:ae=[]}=(0,b.O3)("dataEndpoints",{performanceIndicators:[]}),se=ae.filter((e=>K.includes(e.stat))),ie=()=>(0,n.createElement)(J.Text,{variant:"title.small",size:"20",lineHeight:"28px"},(0,m.__)("Stats overview","woocommerce")),le=()=>{const{updateUserPreferences:e,...t}=(0,l.useUserPreferences)(),o=(0,h.get)(t,["homepage_stats","hiddenStats"],Q),r=(0,c.useSelect)((e=>e(l.PLUGINS_STORE_NAME).isJetpackConnected()),[]),a=(t.homepage_stats||{}).installJetpackDismissed,s=se.filter((e=>!o.includes(e.stat)));return(0,n.createElement)(_.Card,{size:"large",className:"woocommerce-stats-overview woocommerce-homescreen-card"},(0,n.createElement)(_.CardHeader,{size:"medium"},(0,n.createElement)(ie,null),(0,n.createElement)(p.EllipsisMenu,{label:(0,m.__)("Choose which values to display","woocommerce"),renderContent:()=>(0,n.createElement)(n.Fragment,null,(0,n.createElement)(p.MenuTitle,null,(0,m.__)("Display stats:","woocommerce")),se.map((t=>{const r=!o.includes(t.stat);return(0,n.createElement)(p.MenuItem,{checked:r,isCheckbox:!0,isClickable:!0,key:t.stat,onInvoke:()=>(t=>{const n=(0,h.xor)(o,[t]);e({homepage_stats:{hiddenStats:n}}),(0,w.recordEvent)("statsoverview_indicators_toggle",{indicator_name:t,status:n.includes(t)?"off":"on"})})(t.stat)},t.label)})))})),(0,n.createElement)(_.TabPanel,{className:"woocommerce-stats-overview__tabs",onSelect:e=>{(0,w.recordEvent)("statsoverview_date_picker_update",{period:e})},tabs:[{title:(0,m.__)("Today","woocommerce"),name:"today"},{title:(0,m.__)("Week to date","woocommerce"),name:"week"},{title:(0,m.__)("Month to date","woocommerce"),name:"month"}]},(e=>(0,n.createElement)(n.Fragment,null,!r&&!a&&(0,n.createElement)(ce,null),(0,n.createElement)(ee,{query:{period:e.name,compare:"previous_period"},stats:s})))),(0,n.createElement)(_.CardFooter,null,(0,n.createElement)(p.Link,{className:"woocommerce-stats-overview__more-btn",href:(0,y.getNewPath)({},"/analytics/overview"),type:"wc-admin",onClick:()=>{(0,w.recordEvent)("statsoverview_indicators_click",{key:"view_detailed_stats"})}},(0,m.__)("View detailed stats","woocommerce"))))};var me=o(26184),de=o(34200),ue=o(87983),pe=o(58358),_e=o(92486),we=o(49704),he=o(25918),ge=o(6520);const Ee=({title:e,children:t})=>(0,n.createElement)("div",{className:"woocommerce-quick-links__category"},(0,n.createElement)("h3",{className:"woocommerce-quick-links__category-header"},e),t);var ke=o(23374),ye=o(12532);const ve=({icon:e,title:t,href:o,linkType:r,onClick:c})=>{const a="external"===r;return(0,n.createElement)("div",{className:"woocommerce-quick-links__item"},(0,n.createElement)(p.Link,{onClick:c,href:o,type:r,target:a?"_blank":null,className:"woocommerce-quick-links__item-link"},(0,n.createElement)(ke.Z,{className:"woocommerce-quick-links__item-link__icon",icon:e}),(0,n.createElement)(J.Text,{className:"woocommerce-quick-links__item-link__text",as:"div",variant:"button",weight:"600",size:"14",lineHeight:"20px"},t),a&&(0,n.createElement)(ke.Z,{icon:ye.Z})))};function fe({path:e,tab:t=null,type:o,href:n=null}){return{"wc-admin":{href:`admin.php?page=wc-admin&path=%2F${e}`,linkType:"wc-admin"},"wp-admin":{href:e,linkType:"wp-admin"},"wc-settings":{href:`admin.php?page=wc-settings&tab=${t}`,linkType:"wp-admin"}}[o]||{href:n,linkType:"external"}}const Ce=()=>{const e=(0,b.O3)("shopUrl"),t=(0,Y.applyFilters)("woocommerce_admin_homescreen_quicklinks",[]).reduce(((e,{icon:t,href:o,title:n})=>(new URL(o,window.location.href).origin===window.location.origin&&e.push({icon:t,link:{href:o,linkType:"wp-admin"},title:n,listItemTag:"quick-links-extension-link"}),e)),[]),o=function(e){return[{title:(0,m.__)("Marketing & Merchandising","woocommerce"),items:[{title:(0,m.__)("Marketing","woocommerce"),link:fe({type:"wc-admin",path:"marketing"}),icon:me.Z,listItemTag:"marketing"},{title:(0,m.__)("Add products","woocommerce"),link:fe({type:"wp-admin",path:"post-new.php?post_type=product"}),icon:de.Z,listItemTag:"add-products"},{title:(0,m.__)("Personalize my store","woocommerce"),link:fe({type:"wp-admin",path:"customize.php"}),icon:ue.Z,listItemTag:"personalize-store"},{title:(0,m.__)("View my store","woocommerce"),link:fe({type:"external",href:e}),icon:pe.Z,listItemTag:"view-store"}]},{title:(0,m.__)("Settings","woocommerce"),items:[{title:(0,m.__)("Store details","woocommerce"),link:fe({type:"wc-settings",tab:"general"}),icon:_e.Z,listItemTag:"edit-store-details"},{title:(0,m.__)("Payments","woocommerce"),link:fe({type:"wc-settings",tab:"checkout"}),icon:we.Z,listItemTag:"payment-settings"},{title:(0,m.__)("Tax","woocommerce"),link:fe({type:"wc-settings",tab:"tax"}),icon:he.Z,listItemTag:"tax-settings"},{title:(0,m.__)("Shipping","woocommerce"),link:fe({type:"wc-settings",tab:"shipping"}),icon:ge.Z,listItemTag:"shipping-settings"}]}]}(e),r={title:(0,m.__)("Extensions","woocommerce"),items:t},c=t.length?[...o,r]:o;return(0,n.createElement)(_.Card,{size:"medium"},(0,n.createElement)(_.CardHeader,{size:"medium"},(0,n.createElement)(J.Text,{variant:"title.small",size:"20",lineHeight:"28px"},(0,m.__)("Store management","woocommerce"))),(0,n.createElement)(_.CardBody,{size:"custom",className:"woocommerce-store-management-links__card-body"},c.map((e=>(0,n.createElement)(Ee,{key:e.title,title:e.title},e.items.map((({icon:e,listItemTag:t,title:o,link:{href:r,linkType:c}})=>(0,n.createElement)(ve,{icon:e,key:`${o}_${t}_${r}`,title:o,linkType:c,href:r,onClick:()=>{(0,w.recordEvent)("home_quick_links_click",{task_name:t})}}))))))))};var be=o(8658),Se=o(33773),Ne=o(98817);const Te=o.p+"93973815f7cd64d5f512.png",Re=({body:e})=>(0,n.createElement)("div",{className:"mobile-app-modal-layout"},(0,n.createElement)("div",{className:"mobile-app-modal-content"},e),(0,n.createElement)("div",{className:"mobile-app-modal-illustration"},(0,n.createElement)("img",{src:Te,alt:(0,m.__)("Screen captures of the WooCommerce mobile app","woocommerce")}))),Ae="user-cannot-install",Le="not-owner-of-connection",Pe="full-connection",Me="initializing",xe=()=>{const{currentUserCan:e}=(0,l.useUser)(),{canUserInstallPlugins:t,jetpackInstallState:o,jetpackConnectionData:r}=(0,c.useSelect)((t=>{const{getPluginInstallState:o,getJetpackConnectionData:n}=t(l.PLUGINS_STORE_NAME),r=o("jetpack");return{jetpackConnectionData:n(),jetpackInstallState:r,canUserInstallPlugins:e("install_plugins")}})),{installJetpackAndConnect:a}=(0,c.useDispatch)(l.PLUGINS_STORE_NAME),[s,i]=(0,n.useState)(Me),m=(0,n.useCallback)((()=>{const e=window.location.href;a(ne,(()=>e+"&jetpackState=returning")),i("installing")}),[a]);return(0,n.useEffect)((()=>{if(t)switch(o){case"installed":i("not-activated");break;case"unavailable":i("not-installed");break;case"activated":r&&!r?.connectionOwner?i("userless-connection"):r&&!r?.currentUser?.isMaster?i(Le):r&&r?.currentUser?.isConnected&&r?.currentUser?.isMaster&&i(Pe)}else i(Ae)}),[t,o,r]),{state:s,installHandler:m,jetpackConnectionData:r}};var Ie=o(86989),Oe=o.n(Ie);const Ue="fetching",De="success",Ze="error",qe=({onClickHandler:e,isFetching:t})=>(0,n.createElement)(_.Button,{className:"send-magic-link-button",onClick:e},t&&(0,n.createElement)(p.Spinner,{className:"send-magic-link-spinner"}),(0,n.createElement)("div",{style:{visibility:t?"hidden":"visible"},className:"send-magic-link-button-contents"},(0,n.createElement)("div",{className:"send-magic-link-button-text"},(0,m.__)("✨️ Send the sign-in link","woocommerce")))),Fe=({returnToSendLinkPage:e})=>(0,n.createElement)("div",{className:"email-sent-modal-body"},(0,n.createElement)("div",{className:"email-sent-illustration"}),(0,n.createElement)("div",{className:"email-sent-title"},(0,n.createElement)("h1",null,(0,m.__)("Check your email!","woocommerce"))),(0,n.createElement)("div",{className:"email-sent-subheader-spacer"},(0,n.createElement)("div",{className:"email-sent-subheader"},(0,m.__)("We just sent you the magic link. Open it on your mobile device and follow the instructions.","woocommerce"))),(0,n.createElement)("div",{className:"email-sent-footer"},(0,n.createElement)("div",{className:"email-sent-footer-prompt"},(0,m.__)("DIDN’T GET IT?","woocommerce")),(0,n.createElement)("div",{className:"email-sent-footer-text"},(0,k.Z)({mixedString:(0,m.__)("Check your spam/junk email folder or {{ sendAnotherLink /}}.","woocommerce"),components:{sendAnotherLink:(0,n.createElement)(_.Button,{className:"email-sent-send-another-link",onClick:()=>{e()}},(0,m.__)("send another link","woocommerce"))}}))));o(99196);const Be=({children:e})=>(0,n.createElement)("div",{className:"jetpack-installation-content"},(0,n.createElement)("div",{className:"modal-layout-header"},(0,n.createElement)("div",{className:"woo-icon"}),(0,n.createElement)("div",{className:"modal-header"},(0,n.createElement)("h1",null,(0,m.__)("Manage orders and track sales in real-time with the free mobile app","woocommerce")))),(0,n.createElement)("div",{className:"modal-layout-body"},e),(0,n.createElement)("div",{className:"modal-layout-footer"},(0,n.createElement)("div",{className:"mobile-footer-icons"},(0,n.createElement)("div",{className:"apple-icon"}),(0,n.createElement)("div",{className:"android-icon"})),(0,n.createElement)("div",{className:"mobile-footer-blurb"},(0,m.__)("The WooCommerce Mobile App is available on iOS and Android","woocommerce"))));var He=o(4210);const We=()=>(0,n.createElement)("div",null,(0,n.createElement)(He.tv,{value:"https://woocommerce.com/mobile/?utm_source=wc_onboarding_mobile_task",size:140})),je=({loginUrl:e})=>(0,n.createElement)("div",null,e&&(0,n.createElement)("div",null,(0,n.createElement)(He.tv,{value:e,size:140}),(0,n.createElement)("p",null,(0,m.__)("The app version needs to be 15.7 or above to sign in with this link.","woocommerce"))),(0,n.createElement)("div",null,(0,k.Z)({mixedString:(0,m.__)("Any troubles signing in? Check out the {{link}}FAQ{{/link}}.","woocommerce"),components:{link:(0,n.createElement)(p.Link,{href:"https://woocommerce.com/document/android-ios-apps-login-help-faq/",target:"_blank",type:"external",onClick:()=>{(0,w.recordEvent)("onboarding_app_login_faq_click")}}),strong:(0,n.createElement)("strong",null)}}))),ze=({step:e,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationStepHandler:r,sendMagicLinkHandler:c,sendMagicLinkStatus:a})=>{const[s,i]=(0,n.useState)(void 0);return(0,n.useEffect)((()=>{if("first"===e)i([{key:"first",label:(0,m.__)("Install the mobile app","woocommerce"),description:(0,m.__)("Scan the code below to download or upgrade the app, or visit woo.com/mobile from your mobile device.","woocommerce"),content:(0,n.createElement)(n.Fragment,null,(0,n.createElement)(We,null),(0,n.createElement)(_.Button,{variant:"primary",className:"install-app-button",onClick:()=>{r()}},(0,m.__)("App is installed","woocommerce")))},{key:"second",label:(0,m.__)("Sign into the app","woocommerce"),description:"",content:(0,n.createElement)(n.Fragment,null)}]);else if("second"===e)if(t&&void 0!==o)i([{key:"first",label:(0,m.__)("App installed","woocommerce"),description:"",content:(0,n.createElement)(n.Fragment,null)},{key:"second",label:"Sign into the app",description:(0,m.sprintf)((0,m.__)("We’ll send a magic link to %s. Open it on your smartphone or tablet to sign into your store instantly.","woocommerce"),o),content:(0,n.createElement)(qe,{isFetching:a===Ue,onClickHandler:c})}]);else{const e=(0,b.O3)("siteUrl"),t=(0,b.O3)("currentUserData").username,o=`woocommerce://app-login?siteUrl=${encodeURIComponent(e)}&username=${encodeURIComponent(t)}`,r=o?(0,m.__)("Scan the QR code below and enter the wp-admin password in the app.","woocommerce"):(0,m.__)("Follow the instructions in the app to sign in.","woocommerce");i([{key:"first",label:(0,m.__)("App installed","woocommerce"),description:"",content:(0,n.createElement)(n.Fragment,null)},{key:"second",label:"Sign into the app",description:r,content:(0,n.createElement)(je,{loginUrl:o})}])}}),[e,t,o,r,c,a]),(0,n.createElement)("div",{className:"login-stepper-wrapper"},s&&(0,n.createElement)(p.Stepper,{isVertical:!0,currentStep:e,steps:s}))},Ge=({appInstalledClicked:e,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationHandler:r,sendMagicLinkHandler:c,sendMagicLinkStatus:a})=>(0,n.createElement)(Be,null,(0,n.createElement)("div",{className:"modal-subheader"},(0,n.createElement)("h3",null,(0,m.__)("Run your store from anywhere in two easy steps.","woocommerce"))),(0,n.createElement)(ze,{step:e?"second":"first",isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationStepHandler:r,sendMagicLinkHandler:c,sendMagicLinkStatus:a}));var Ve=o(43631);const $e=()=>{const[e,t]=(0,n.useState)(!1),[o,r]=(0,n.useState)(!1),{state:a,jetpackConnectionData:s}=xe(),{updateOptions:i}=(0,c.useDispatch)(l.OPTIONS_STORE_NAME),[d,u]=(0,n.useState)(),[p]=(0,Se.lr)();(0,n.useEffect)((()=>{p.get("mobileAppModal")?t(!0):t(!1),"returning"===p.get("jetpackState")&&r(!0)}),[p]);const[h,g]=(0,n.useState)(!1),[E,k]=(0,n.useState)(!1),[v,f]=(0,n.useState)(!1),{requestState:C,fetchMagicLinkApiCall:b}=(()=>{const[e,t]=(0,n.useState)("initializing"),{createNotice:o}=(0,c.useDispatch)("core/notices");return{requestState:e,fetchMagicLinkApiCall:(0,n.useCallback)((()=>{t(Ue),Oe()({path:`${l.WC_ADMIN_NAMESPACE}/mobile-app/send-magic-link`}).then((e=>{"success"===e.code?t(De):(t(Ze),o("error",(0,m.__)("Sorry, an unknown error occurred.","woocommerce")))})).catch((e=>{t(Ze),(0,w.recordEvent)("magic_prompt_send_magic_link_error",{error:e.message,code:e.code}),"error_sending_mobile_magic_link"===e.code?o("error",(0,m.__)("We couldn’t send the link. Try again in a few seconds.","woocommerce")):"invalid_user_permission_view_admin"===e.code?o("error",(0,m.__)("Sorry, your account doesn’t have sufficient permission.","woocommerce")):"jetpack_not_connected"===e.code?o("error",e.message):o("error","We couldn’t send the link. Try again in a few seconds.")}))}),[o])}})(),S=(0,n.useCallback)((()=>{g(!0),(0,w.recordEvent)("onboarding_app_install_click")}),[]),N=(0,n.useCallback)((()=>{b(),(0,w.recordEvent)("magic_prompt_send_signin_link_click")}),[b]);return(0,n.useEffect)((()=>{C===De&&k(!0)}),[C]),(0,n.useEffect)((()=>{if(E)u((0,n.createElement)(Fe,{returnToSendLinkPage:()=>{k(!1),f(!0),(0,w.recordEvent)("magic_prompt_retry_send_signin_link")}}));else{var e;const t=null!==(e=a===Pe&&void 0!==s?.currentUser?.wpcomUser?.email)&&void 0!==e&&e,o=s?.currentUser?.wpcomUser?.email;u((0,n.createElement)(Ge,{appInstalledClicked:h,isJetpackPluginInstalled:t,wordpressAccountEmailAddress:o,completeInstallationHandler:S,sendMagicLinkHandler:N,sendMagicLinkStatus:C}))}}),[h,N,E,o,s?.currentUser?.wpcomUser?.email,a,v,C]),(0,n.createElement)(n.Fragment,null,e&&(0,n.createElement)(_.Guide,{onFinish:()=>{i({woocommerce_admin_dismissed_mobile_app_modal:"yes"}),(0,y.updateQueryString)({jetpackState:void 0,mobileAppModal:void 0},void 0,Object.fromEntries(p.entries()))},className:"woocommerce__mobile-app-welcome-modal",pages:[{content:(0,n.createElement)(Re,{body:d})}]}))},Je="wc/admin/mobile-app-help-entry-callback";(0,Ne.registerPlugin)("woocommerce-mobile-app-modal",{render:()=>{const{state:e}=xe(),t=(0,n.useCallback)((t=>e===Me||e===Ae||e===Le?t:[...t,{title:(0,m.__)("Get the WooCommerce app","woocommerce"),link:(0,v.getAdminLink)("./admin.php?page=wc-admin&mobileAppModal=true"),linkType:"wc-admin"}]),[e]);return(0,n.useEffect)((()=>{(0,Y.removeFilter)(Ve.SETUP_TASK_HELP_ITEMS_FILTER,Je),(0,Y.addFilter)(Ve.SETUP_TASK_HELP_ITEMS_FILTER,Je,t,10)}),[t]),null},scope:"woocommerce-admin"});const Ye="woocommerce_homescreen_experimental_header_banner_item",Ke=({children:e,order:t=1})=>(0,n.createElement)(_.Fill,{name:Ye},(o=>(0,p.createOrderedChildren)(e,t,o)));Ke.Slot=({fillProps:e})=>(0,n.createElement)(_.Slot,{name:Ye,fillProps:e},p.sortFillsByOrder);const Qe=({className:e})=>{const t=(0,J.useSlot)(Ye);return Boolean(t?.fills?.length)?(0,n.createElement)("div",{className:(0,a.Z)("woocommerce-homescreen__header",e)},(0,n.createElement)(Ke.Slot,null)):null},Xe="experimental_woocommerce_wcpay_feature",et=({children:e,order:t=1})=>(0,n.createElement)(_.Fill,{name:Xe},(o=>(0,p.createOrderedChildren)(e,t,o)));et.Slot=({fillProps:e})=>(0,n.createElement)(_.Slot,{name:Xe,fillProps:e},p.sortFillsByOrder);const tt=({className:e})=>{const t=(0,J.useSlot)(Xe);return Boolean(t?.fills?.length)?(0,n.createElement)("div",{className:(0,a.Z)("woocommerce-homescreen__header",e)},(0,n.createElement)(et.Slot,null)):null},ot=(0,n.lazy)((()=>Promise.resolve().then(o.bind(o,8658)).then((e=>({default:e.TaskLists}))))),nt=(e,t,o,n)=>{const r=o||n||window.wcAdminFeatures.analytics;return"two_columns"===(e||t)&&r},rt=({defaultHomescreenLayout:e,query:t,taskListComplete:o,hasTaskList:r,showingProgressHeader:c,isLoadingTaskLists:s,isTaskListHidden:i})=>{var p;const _=(0,l.useUserPreferences)(),w=o||i,h=o||i,g=Object.keys(t).length>0&&!t.task,E=(0,be.useActiveSetupTasklist)(),k=nt(_.homepage_layout,e,o,i),y=(0,n.useRef)(!0),v=(0,n.useCallback)((()=>{y.current=window.innerWidth>=782}),[]);(0,n.useLayoutEffect)((()=>(v(),window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)})),[v]);const f=y.current&&k,C=null!==(p=t.mobileAppModal)&&void 0!==p&&p,b=()=>(0,n.createElement)(n.Suspense,{fallback:(0,n.createElement)(be.TasksPlaceholder,{query:t})},E&&g&&(0,n.createElement)(n.Fragment,null,(0,n.createElement)(be.ProgressTitle,{taskListId:E})),(0,n.createElement)(ot,{query:t}));return(0,n.createElement)(n.Fragment,null,g&&(0,n.createElement)(Qe,{className:(0,a.Z)("woocommerce-homescreen",{"woocommerce-homescreen-column":!k})}),(0,n.createElement)("div",{className:(0,a.Z)("woocommerce-homescreen",{"two-columns":k})},g?(0,n.createElement)(n.Fragment,null,(0,n.createElement)(V,{shouldStick:f},!s&&!c&&(0,n.createElement)(d.Z,{className:"your-store-today",title:(0,m.__)("Your store today","woocommerce"),subtitle:(0,m.__)("To do’s, tips, and insights for your business","woocommerce")}),h&&(0,n.createElement)(tt,null),i&&(0,n.createElement)(G,null),r&&b(),(0,n.createElement)(u.Z,{format:"promo-card"}),(0,n.createElement)($.Z,null)),(0,n.createElement)(V,{shouldStick:f},window.wcAdminFeatures.analytics&&(0,n.createElement)(le,null),w&&(0,n.createElement)(Ce,null))):b(),C&&(0,n.createElement)($e,null)))};rt.propTypes={taskListComplete:i().bool,hasTaskList:i().bool,query:i().object.isRequired,shouldShowWelcomeModal:i().bool,shouldShowWelcomeFromCalypsoModal:i().bool};const ct=(0,r.compose)((0,c.withSelect)((e=>{const{isNotesRequesting:t}=e(l.NOTES_STORE_NAME),{getOption:o}=e(l.OPTIONS_STORE_NAME),{getTaskList:n,getTaskLists:r,hasFinishedResolution:c}=e(l.ONBOARDING_STORE_NAME),a=r(),s=!c("getTaskLists");return{defaultHomescreenLayout:o("woocommerce_default_homepage_layout")||"single_column",isBatchUpdating:t("batchUpdateNotes"),isLoadingTaskLists:s,isTaskListHidden:n("setup")?.isHidden,hasTaskList:(0,b.O3)("visibleTaskListIds",[]).length>0,showingProgressHeader:!!a.find((e=>e.isVisible&&e.displayProgressHeader)),taskListComplete:n("setup")?.isComplete}})))(rt)},63724:(e,t,o)=>{o.d(t,{Z:()=>b});var n=o(69307),r=o(65736),c=o(86020),a=o(55609),s=o(67221),i=o(9818),l=o(14599),m=o(86892),d=o(83165),u=o(14812),p=o(76292),_=o.n(p),w=o(86053),h=o(86357),g=o(53736);const E=({onClose:e})=>{const{createNotice:t}=(0,i.useDispatch)("core/notices"),{batchUpdateNotes:o,removeAllNotes:c}=(0,i.useDispatch)(s.NOTES_STORE_NAME);return(0,n.createElement)(n.Fragment,null,(0,n.createElement)(a.Modal,{title:(0,r.__)("Dismiss all messages","woocommerce"),className:"woocommerce-inbox-dismiss-all-modal",onRequestClose:e},(0,n.createElement)("div",{className:"woocommerce-inbox-dismiss-all-modal__wrapper"},(0,n.createElement)("div",{className:"woocommerce-usage-modal__message"},(0,r.__)("Are you sure? Inbox messages will be dismissed forever.","woocommerce")),(0,n.createElement)("div",{className:"woocommerce-usage-modal__actions"},(0,n.createElement)(a.Button,{onClick:e},(0,r.__)("Cancel","woocommerce")),(0,n.createElement)(a.Button,{isPrimary:!0,onClick:()=>{(async()=>{(0,l.recordEvent)("wcadmin_inbox_action_dismissall",{});try{const e=await c({status:"unactioned"});t("success",(0,r.__)("All messages dismissed","woocommerce"),{actions:[{label:(0,r.__)("Undo","woocommerce"),onClick:()=>{o(e.map((e=>e.id)),{is_deleted:0})}}]})}catch(o){t("error",(0,r.__)("Messages could not be dismissed","woocommerce")),e()}})(),e()}},(0,r.__)("Yes, dismiss all","woocommerce"))))))},k={page:1,per_page:5,status:"unactioned",type:s.QUERY_DEFAULTS.noteTypes,orderby:"date",order:"desc",_fields:["id","name","title","content","type","status","actions","date_created","date_created_gmt","layout","image","is_deleted","is_read","locale"]},y=["en_US","en_AU","en_CA","en_GB","en_ZA"],v=_()("2022-01-11","YYYY-MM-DD").valueOf(),f=(e,t)=>{(0,l.recordEvent)("inbox_action_click",{note_name:e.name,note_title:e.title,note_content_inner_link:t})};let C=!1;const b=({showHeader:e=!0})=>{const[t,o]=(0,n.useState)(k.per_page),[p,b]=(0,n.useState)(!1),[S,N]=(0,n.useState)([]),[T,R]=(0,n.useState)({}),{createNotice:A}=(0,i.useDispatch)("core/notices"),{removeNote:L,updateNote:P,triggerNoteAction:M,invalidateResolutionForStoreSelector:x}=(0,i.useDispatch)(s.NOTES_STORE_NAME),I=(0,g.GG)(),O=(0,n.useMemo)((()=>({...k,per_page:t})),[t]),{isError:U,notes:D,notesHaveResolved:Z,isBatchUpdating:q,unreadNotesCount:F}=(0,i.useSelect)((e=>{const{getNotes:t,getNotesError:o,isNotesRequesting:n,hasFinishedResolution:r}=e(s.NOTES_STORE_NAME);return{notes:t(O),unreadNotesCount:t({...k,is_read:!1,per_page:-1}).length,isError:Boolean(o("getNotes",[O])),isBatchUpdating:n("batchUpdateNotes"),notesHaveResolved:!n("batchUpdateNotes")&&r("getNotes",[O])}}));(0,n.useEffect)((()=>{Z&&D.length<t&&b(!0),Z&&D.length&&N(D.map((e=>{const t=_()(e.date_created_gmt,"YYYY-MM-DD").valueOf();return y.includes(e.locale)&&t>=v?{...e,content:(0,h.r7)(e.content,320)}:e})))}),[D,Z]);const[B,H]=(0,n.useState)(!1);if(U){const e=(0,r.__)("There was an error getting your inbox. Please try again.","woocommerce"),t=(0,r.__)("Reload","woocommerce"),o=()=>{window.location.reload()};return(0,n.createElement)(c.EmptyContent,{title:e,actionLabel:t,actionURL:null,actionCallback:o})}return Z&&!S.length?null:(0,n.createElement)(n.Fragment,null,B&&(0,n.createElement)(E,{onClose:()=>{H(!1)}}),(0,n.createElement)("div",{className:"woocommerce-homepage-notes-wrapper"},!Z&&!S.length&&(0,n.createElement)(c.Section,null,(0,n.createElement)(u.InboxNotePlaceholder,{className:"banner message-is-unread"})),(0,n.createElement)(c.Section,null,Boolean(S.length)&&(({hasNotes:e,isBatchUpdating:t,notes:o,onDismiss:s,onNoteActionClick:i,onNoteVisible:p,setShowDismissAllModal:_,showHeader:h=!0,loadMoreNotes:g,allNotesFetched:E,notesHaveResolved:y,unreadNotesCount:v})=>{if(t)return;if(!e)return(0,n.createElement)(w.U,{className:"woocommerce-empty-activity-card",title:(0,r.__)("Your inbox is empty","woocommerce"),icon:!1},(0,r.__)("As things begin to happen in your store your inbox will start to fill up. You’ll see things like achievements, new feature announcements, extension recommendations and more!","woocommerce"));C||((0,l.recordEvent)("inbox_panel_view",{total:o.length}),C=!0);const b=Object.keys(o).map((e=>o[e]));return(0,n.createElement)(a.Card,{size:"large"},h&&(0,n.createElement)(a.CardHeader,{size:"medium"},(0,n.createElement)("div",{className:"woocommerce-inbox-card__header"},(0,n.createElement)(u.Text,{size:"20",lineHeight:"28px",variant:"title.small"},(0,r.__)("Inbox","woocommerce")),(0,n.createElement)(c.Badge,{count:v})),(0,n.createElement)(c.EllipsisMenu,{label:(0,r.__)("Inbox Notes Options","woocommerce"),renderContent:({onToggle:e})=>(0,n.createElement)("div",{className:"woocommerce-inbox-card__section-controls"},(0,n.createElement)(a.Button,{onClick:()=>{_(!0),e()}},(0,r.__)("Dismiss all","woocommerce")))})),(0,n.createElement)(m.Z,{role:"menu"},b.map((e=>{const{id:t,is_deleted:o}=e;return o?null:(0,n.createElement)(d.Z,{key:t,timeout:500,classNames:"woocommerce-inbox-message"},(0,n.createElement)(u.InboxNoteCard,{key:t,note:e,onDismiss:s,onNoteActionClick:i,onBodyLinkClick:f,onNoteVisible:p}))}))),E?null:y?(0,n.createElement)(a.CardFooter,{className:"woocommerce-inbox-card__footer",size:"medium"},(0,n.createElement)(a.Button,{isPrimary:!0,onClick:()=>{g()}},b.length>k.per_page?(0,r.__)("Show more","woocommerce"):(0,r.__)("Show older","woocommerce"))):(0,n.createElement)(u.InboxNotePlaceholder,{className:"banner message-is-unread"}))})({loadMoreNotes:()=>{(0,l.recordEvent)("inbox_action_load_more",{quantity_shown:S.length}),o(t+10)},hasNotes:(0,h.kS)(S),isBatchUpdating:q,notes:S,onDismiss:async e=>{(0,l.recordEvent)("inbox_action_dismiss",{note_name:e.name,note_title:e.title,note_name_dismiss_all:!1,note_name_dismiss_confirmation:!0,screen:I});const t=e.id;try{await L(t),x("getNotes"),A("success",(0,r.__)("Message dismissed","woocommerce"),{actions:[{label:(0,r.__)("Undo","woocommerce"),onClick:async()=>{await P(t,{is_deleted:0}),x("getNotes")}}]})}catch(e){A("error",(0,r._n)("Message could not be dismissed","Messages could not be dismissed",1,"woocommerce"))}},onNoteActionClick:(e,t)=>{M(e.id,t.id)},onNoteVisible:e=>{T[e.id]||e.is_read||(R({...T,[e.id]:!0}),setTimeout((()=>{P(e.id,{is_read:!0})}),3e3)),(0,l.recordEvent)("inbox_note_view",{note_content:e.content,note_name:e.name,note_title:e.title,note_type:e.type,screen:I})},setShowDismissAllModal:H,showHeader:e,allNotesFetched:p,notesHaveResolved:Z,unreadNotesCount:F}))))}},1968:(e,t,o)=>{o.d(t,{Z:()=>u});var n=o(69307),r=o(7829),c=o(84560),a=o(47642),s=o(25918),i=o(23374),l=o(28601),m=o(346);const d={info:c.Z,check:a.Z,percent:s.Z};function u(e){const{id:t,description:o,children:c,icon:a,isDismissible:s=!0,variant:u="info",onClose:p,onLoad:_}=e,[w,h]=(0,n.useState)("true"!==localStorage.getItem(`wc-marketplaceNoticeClosed-${t}`));if((0,n.useEffect)((()=>{w&&"function"==typeof _&&_()}),[w]),!w)return null;const g=(0,r.Z)("woocommerce-marketplace__notice",`woocommerce-marketplace__notice--${u}`,{"is-dismissible":s}),E=d[a||"info"],k=(0,r.Z)("woocommerce-marketplace__notice-icon",`woocommerce-marketplace__notice-icon--${u}`);return(0,n.createElement)("div",{className:g},a&&(0,n.createElement)("span",{className:k},(0,n.createElement)(i.Z,{icon:E})),(0,n.createElement)("div",{className:"woocommerce-marketplace__notice-content"},(0,n.createElement)("p",{className:"woocommerce-marketplace__notice-description",dangerouslySetInnerHTML:(0,m.ZP)(o)}),c&&(0,n.createElement)("div",{className:"woocommerce-marketplace__notice-children"},c)),s&&(0,n.createElement)("button",{className:"woocommerce-marketplace__notice-close","aria-label":"Close",onClick:()=>{h(!1),localStorage.setItem(`wc-marketplaceNoticeClosed-${t}`,"true"),"function"==typeof p&&p()}},(0,n.createElement)(i.Z,{icon:l.Z})))}},55968:(e,t,o)=>{o.d(t,{Z:()=>r});var n=o(69307);function r(){return(0,n.createElement)("svg",{width:"72",height:"60",viewBox:"0 0 72 60",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,n.createElement)("g",{clipPath:"url(#clip0_4074_10418)"},(0,n.createElement)("path",{d:"M68.5301 33.3144C68.0263 32.1006 66.3348 32.344 65.8443 31.1636C65.3538 29.9832 66.7251 28.9562 66.2213 27.7458C65.7175 26.5354 64.0259 26.7755 63.5355 25.5951C63.045 24.4147 64.4163 23.3877 63.9125 22.1773C63.4087 20.9669 61.7171 21.207 61.2267 20.0266C60.7362 18.8462 62.1075 17.8192 61.6037 16.6088C61.0999 15.395 59.4083 15.6385 58.9179 14.4581C58.4274 13.2777 59.7987 12.2507 59.2949 11.0403C58.7911 9.82652 57.0995 10.0699 56.6091 8.88955C56.1186 7.70915 57.4899 6.68214 56.9861 5.47174C56.4823 4.26134 54.7907 4.50142 54.3003 3.32102C53.8465 2.22733 55.0476 1.11696 54.8274 -0.00341797L0 22.5941C0.5038 23.8079 2.19537 23.5644 2.68582 24.7448C3.17627 25.9252 1.805 26.9522 2.3088 28.1626C2.8126 29.373 4.50417 29.133 4.99462 30.3134C5.48508 31.4937 4.11381 32.5208 4.61761 33.7312C5.12141 34.9416 6.81297 34.7015 7.30343 35.8819C7.79388 37.0623 6.42261 38.0893 6.92641 39.2997C7.43021 40.5134 9.12178 40.27 9.61223 41.4504C10.1027 42.6308 8.73142 43.6578 9.23522 44.8682C9.73902 46.0786 11.4306 45.8385 11.921 47.0189C12.4115 48.1993 11.0402 49.2263 11.544 50.4367C12.0478 51.6471 13.7394 51.4071 14.2298 52.5874C14.6836 53.6811 13.4825 54.7915 13.7027 55.9119L28.1928 49.9232L68.5368 33.3177L68.5301 33.3144Z",fill:"#674399"}),(0,n.createElement)("path",{d:"M13.696 55.912L28.1861 49.9234L52.3851 39.9634H7.46021C8.17086 40.4802 9.23852 40.5569 9.60886 41.4539C10.0993 42.6343 8.72805 43.6613 9.23185 44.8717C9.73565 46.0821 11.4272 45.842 11.9177 47.0224C12.4081 48.2028 11.0368 49.2298 11.5406 50.4402C12.0444 51.6506 13.736 51.4105 14.2265 52.5909C14.6802 53.6846 13.4791 54.795 13.6993 55.9154L13.696 55.912Z",fill:"#3C2861"}),(0,n.createElement)("path",{d:"M63.8523 41.9907C63.8523 37.4925 67.499 33.848 71.9998 33.848V23.988H17.873V33.848C22.3739 33.848 26.0206 37.4925 26.0206 41.9907C26.0206 46.4889 22.3739 50.1334 17.873 50.1334V59.9934H71.9998V50.1334C67.499 50.1334 63.8523 46.4889 63.8523 41.9907Z",fill:"#BEA0F2"}),(0,n.createElement)("path",{d:"M35.2527 37.676C35.2527 35.2051 37.0143 33.2878 39.6968 33.2878C42.3793 33.2878 44.1643 35.2051 44.1643 37.676C44.1643 40.1468 42.4026 42.0107 39.6968 42.0107C36.991 42.0107 35.2527 40.1201 35.2527 37.676ZM41.7954 37.676C41.7954 36.2288 40.9046 35.3385 39.6935 35.3385C38.4823 35.3385 37.6182 36.2288 37.6182 37.676C37.6182 39.1231 38.509 39.9601 39.6935 39.9601C40.8779 39.9601 41.7954 39.0664 41.7954 37.676ZM37.9852 51.0704L49.1789 33.5513H51.1774L39.9537 51.0704H37.9819H37.9852ZM44.8983 47.0524C44.8983 44.5849 46.6566 42.641 49.3391 42.641C52.0215 42.641 53.8065 44.5849 53.8065 47.0524C53.8065 49.5199 52.0182 51.3872 49.3391 51.3872C46.6599 51.3872 44.8983 49.4966 44.8983 47.0524ZM51.441 47.0524C51.441 45.6053 50.5468 44.715 49.3357 44.715C48.1246 44.715 47.2605 45.6053 47.2605 47.0524C47.2605 48.4996 48.1279 49.3365 49.3357 49.3365C50.5435 49.3365 51.441 48.4696 51.441 47.0524Z",fill:"#674399"})),(0,n.createElement)("defs",null,(0,n.createElement)("clipPath",{id:"clip0_4074_10418"},(0,n.createElement)("rect",{width:"72",height:"60",fill:"white"}))))}},24844:(e,t,o)=>{o.d(t,{Z:()=>l});var n=o(69307),r=o(55609),c=o(65736),a=o(14599),s=o(346);const i={percent:o(55968).Z},l=({promotion:e})=>{var t,o;const l=window.location.pathname+window.location.search,m=()=>JSON.parse(localStorage.getItem("wc-marketplaceDismissedPromos")||"[]"),[d,u]=(0,n.useState)(!m().includes(l));if((0,n.useEffect)((()=>{d&&(0,a.recordEvent)("marketplace_promotion_viewed",{path:l,format:"promo-card"})}),[d]),!d)return null;const p="promo-card"+(e.style?` ${e.style}`:""),_=(0,n.createElement)("div",{className:"promo-content"},(0,n.createElement)("h2",{className:"promo-title"},e.title?.en_US),(0,n.createElement)("div",{className:"promo-text",dangerouslySetInnerHTML:(0,s.ZP)(e.content?.en_US)})),w=(0,n.createElement)("div",{className:"promo-links"},(0,n.createElement)(r.Button,{className:"promo-cta",href:null!==(t=e.cta_link)&&void 0!==t?t:"",onClick:()=>((0,a.recordEvent)("marketplace_promotion_actioned",{path:l,target_uri:e.cta_link,format:"promo-card"}),!0)},null!==(o=e.cta_label?.en_US)&&void 0!==o?o:""),(0,n.createElement)(r.Button,{className:"promo-cta-link",onClick:()=>{u(!1),localStorage.setItem("wc-marketplaceDismissedPromos",JSON.stringify(m().concat(l))),(0,a.recordEvent)("marketplace_promotion_dismissed",{path:l,format:"promo-card"})}},(0,c.__)("Dismiss","woocommerce")));function h(){if(e.icon&&Object.hasOwn(i,e.icon)){const t=i[e.icon];return t?(0,n.createElement)("div",{className:"promo-image"},(0,n.createElement)(t)):null}return null}return(0,n.createElement)("div",{className:p},"has-background"===e?.style?(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"promo-content-links"},_,w),h()):(0,n.createElement)(n.Fragment,null,(0,n.createElement)("div",{className:"promo-content-image"},_,h()),w))}},88533:(e,t,o)=>{o.d(t,{Z:()=>i});var n=o(69307),r=o(14599),c=o(17062),a=o(1968),s=o(24844);const i=({format:e})=>{var t;if(!window?.wcMarketplace?.promotions||!Array.isArray(window?.wcMarketplace?.promotions))return null;const o=(null!==(t=window?.wcMarketplace?.promotions)&&void 0!==t?t:[]).filter((t=>t.format===e)),i=new URLSearchParams(window.location.search),l=i.get("page"),m=Date.now(),d=decodeURIComponent(i.get("path")||""),u=i.get("tab"),p=window.location.pathname+window.location.search,_=()=>{(0,r.recordEvent)("marketplace_promotion_viewed",{path:p,format:e})},w=()=>{(0,r.recordEvent)("marketplace_promotion_dismissed",{path:p,format:e})};return(0,n.createElement)(n.Fragment,null,o.map(((e,t)=>{if(!e.pages)return null;if(!e.pages.some((e=>{if(e.pathname)return e.pathname===p;if(!e.path)return!1;const t=e=>e.startsWith("/")?e:`/${e}`,o=t(e.path),n=t(d);return e.page===l&&o===n&&(e.tab?u:!u)})))return null;const o=new Date(e.date_from_gmt).getTime(),r=new Date(e.date_to_gmt).getTime();return m<o||m>r?null:"promo-card"===e.format?(0,n.createElement)(s.Z,{key:t,promotion:e}):"notice"===e.format&&e?.content?(0,n.createElement)(a.Z,{key:t,id:null!==(i=e.menu_item_id)&&void 0!==i?i:`promotion-${t}`,description:e.content[c.MV.userLocale]||e.content.en_US,variant:e.style?e.style:"info",icon:e?.icon||"",isDismissible:e.is_dismissible||!1,onLoad:_,onClose:w}):null;var i})))}}}]);
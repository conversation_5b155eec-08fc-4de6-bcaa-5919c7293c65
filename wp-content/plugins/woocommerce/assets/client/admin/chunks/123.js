/*! For license information please see 123.js.LICENSE.txt */
"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[123],{87983:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.<PERSON>,{d:"M4 20h8v-1.5H4V20zM18.9 3.5c-.6-.6-1.5-.6-2.1 0l-7.2 7.2c-.4-.1-.7 0-1.1.1-.5.2-1.5.7-1.9 2.2-.4 1.7-.8 2.2-1.1 2.7-.1.1-.2.3-.3.4l-.6 1.1H6c2 0 3.4-.4 4.7-1.4.8-.6 1.2-1.4 1.3-2.3 0-.3 0-.5-.1-.7L19 5.7c.5-.6.5-1.6-.1-2.2zM9.7 14.7c-.7.5-1.5.8-2.4 1 .2-.5.5-1.2.8-2.3.2-.6.4-1 .8-1.1.5-.1 1 .1 1.3.3.2.2.3.5.2.8 0 .3-.1.9-.7 1.3z"}))},58358:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 4L4 7.9V20h16V7.9L12 4zm6.5 14.5H14V13h-4v5.5H5.5V8.8L12 5.7l6.5 3.1v9.7z"}))},84560:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"}))},26184:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M6.863 13.644L5 13.25h-.5a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5H5L18 6.5h2V16h-2l-3.854-.815.026.008a3.75 3.75 0 01-7.31-1.549zm1.477.313a2.251 2.251 0 004.356.921l-4.356-.921zm-2.84-3.28L18.157 8h.343v6.5h-.343L5.5 11.823v-1.146z",clipRule:"evenodd"}))},49704:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M5.5 9.5v-2h13v2h-13zm0 3v4h13v-4h-13zM4 7a1 1 0 011-1h14a1 1 0 011 1v10a1 1 0 01-1 1H5a1 1 0 01-1-1V7z",clipRule:"evenodd"}))},92486:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M20.1 5.1L16.9 2 6.2 12.7l-1.3 4.4 4.5-1.3L20.1 5.1zM4 20.8h8v-1.5H4v1.5z"}))},25918:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"}))},6520:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(69307),o=n(70444);const s=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M3 6.75C3 5.784 3.784 5 4.75 5H15V7.313l.05.027 5.056 2.73.394.212v3.468a1.75 1.75 0 01-1.75 1.75h-.012a2.5 2.5 0 11-4.975 0H9.737a2.5 2.5 0 11-4.975 0H3V6.75zM13.5 14V6.5H4.75a.25.25 0 00-.25.25V14h.965a2.493 2.493 0 011.785-.75c.7 0 1.332.287 1.785.75H13.5zm4.535 0h.715a.25.25 0 00.25-.25v-2.573l-4-2.16v4.568a2.487 2.487 0 011.25-.335c.7 0 1.332.287 1.785.75zM6.282 15.5a1.002 1.002 0 00.968 1.25 1 1 0 10-.968-1.25zm9 0a1 1 0 101.937.498 1 1 0 00-1.938-.498z"}))},41043:(e,t,n)=>{t.Z=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,a=(e.icon,e.className),l=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],0<=t.indexOf(n)||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,s),h=["gridicon","gridicons-star-outline",a,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",i({className:h,height:n,width:n,onClick:r},l,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.041 3.374.139-2.647 2.092-.784.62.27.962.911 3.249-2.814-1.871-.83-.553-.83.552-2.814 1.871.911-3.249.27-.962-.784-.62-2.648-2.092 3.374-.139.997-.041.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.891 18.18 21l-2.002-7.141L22 9.257l-7.418-.305L12 2z"})))};var r,o=(r=n(99196))&&r.__esModule?r:{default:r},s=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)}},28870:(e,t,n)=>{t.Z=function(e){var t=e.size,n=void 0===t?24:t,r=e.onClick,a=(e.icon,e.className),l=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],0<=t.indexOf(n)||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,s),h=["gridicon","gridicons-star",a,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return o.default.createElement("svg",i({className:h,height:n,width:n,onClick:r},l,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),o.default.createElement("g",null,o.default.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.891 5.82 21l2.002-7.141L2 9.257l7.418-.304z"})))};var r,o=(r=n(99196))&&r.__esModule?r:{default:r},s=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)}},4210:(e,t,n)=>{n.d(t,{tv:()=>R});var r,o=n(99196),s=n.n(o),i=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,c=(e,t,n)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))l.call(t,n)&&c(e,n,t[n]);if(a)for(var n of a(t))h.call(t,n)&&c(e,n,t[n]);return e},d=(e,t)=>{var n={};for(var r in e)l.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&a)for(var r of a(e))t.indexOf(r)<0&&h.call(e,r)&&(n[r]=e[r]);return n};(e=>{const t=class{constructor(e,n,r,o){if(this.version=e,this.errorCorrectionLevel=n,this.modules=[],this.isFunction=[],e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version value out of range");if(o<-1||o>7)throw new RangeError("Mask value out of range");this.size=4*e+17;let i=[];for(let e=0;e<this.size;e++)i.push(!1);for(let e=0;e<this.size;e++)this.modules.push(i.slice()),this.isFunction.push(i.slice());this.drawFunctionPatterns();const a=this.addEccAndInterleave(r);if(this.drawCodewords(a),-1==o){let e=1e9;for(let t=0;t<8;t++){this.applyMask(t),this.drawFormatBits(t);const n=this.getPenaltyScore();n<e&&(o=t,e=n),this.applyMask(t)}}s(0<=o&&o<=7),this.mask=o,this.applyMask(o),this.drawFormatBits(o),this.isFunction=[]}static encodeText(n,r){const o=e.QrSegment.makeSegments(n);return t.encodeSegments(o,r)}static encodeBinary(n,r){const o=e.QrSegment.makeBytes(n);return t.encodeSegments([o],r)}static encodeSegments(e,n,o=1,i=40,l=-1,h=!0){if(!(t.MIN_VERSION<=o&&o<=i&&i<=t.MAX_VERSION)||l<-1||l>7)throw new RangeError("Invalid value");let c,u;for(c=o;;c++){const r=8*t.getNumDataCodewords(c,n),o=a.getTotalBits(e,c);if(o<=r){u=o;break}if(c>=i)throw new RangeError("Data too long")}for(const e of[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH])h&&u<=8*t.getNumDataCodewords(c,e)&&(n=e);let d=[];for(const t of e){r(t.mode.modeBits,4,d),r(t.numChars,t.mode.numCharCountBits(c),d);for(const e of t.getData())d.push(e)}s(d.length==u);const f=8*t.getNumDataCodewords(c,n);s(d.length<=f),r(0,Math.min(4,f-d.length),d),r(0,(8-d.length%8)%8,d),s(d.length%8==0);for(let e=236;d.length<f;e^=253)r(e,8,d);let m=[];for(;8*m.length<d.length;)m.push(0);return d.forEach(((e,t)=>m[t>>>3]|=e<<7-(7&t))),new t(c,n,m,l)}getModule(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]}getModules(){return this.modules}drawFunctionPatterns(){for(let e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);const e=this.getAlignmentPatternPositions(),t=e.length;for(let n=0;n<t;n++)for(let r=0;r<t;r++)0==n&&0==r||0==n&&r==t-1||n==t-1&&0==r||this.drawAlignmentPattern(e[n],e[r]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(e){const t=this.errorCorrectionLevel.formatBits<<3|e;let n=t;for(let e=0;e<10;e++)n=n<<1^1335*(n>>>9);const r=21522^(t<<10|n);s(r>>>15==0);for(let e=0;e<=5;e++)this.setFunctionModule(8,e,o(r,e));this.setFunctionModule(8,7,o(r,6)),this.setFunctionModule(8,8,o(r,7)),this.setFunctionModule(7,8,o(r,8));for(let e=9;e<15;e++)this.setFunctionModule(14-e,8,o(r,e));for(let e=0;e<8;e++)this.setFunctionModule(this.size-1-e,8,o(r,e));for(let e=8;e<15;e++)this.setFunctionModule(8,this.size-15+e,o(r,e));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let e=this.version;for(let t=0;t<12;t++)e=e<<1^7973*(e>>>11);const t=this.version<<12|e;s(t>>>18==0);for(let e=0;e<18;e++){const n=o(t,e),r=this.size-11+e%3,s=Math.floor(e/3);this.setFunctionModule(r,s,n),this.setFunctionModule(s,r,n)}}drawFinderPattern(e,t){for(let n=-4;n<=4;n++)for(let r=-4;r<=4;r++){const o=Math.max(Math.abs(r),Math.abs(n)),s=e+r,i=t+n;0<=s&&s<this.size&&0<=i&&i<this.size&&this.setFunctionModule(s,i,2!=o&&4!=o)}}drawAlignmentPattern(e,t){for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)this.setFunctionModule(e+r,t+n,1!=Math.max(Math.abs(r),Math.abs(n)))}setFunctionModule(e,t,n){this.modules[t][e]=n,this.isFunction[t][e]=!0}addEccAndInterleave(e){const n=this.version,r=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(n,r))throw new RangeError("Invalid argument");const o=t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][n],i=t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][n],a=Math.floor(t.getNumRawDataModules(n)/8),l=o-a%o,h=Math.floor(a/o);let c=[];const u=t.reedSolomonComputeDivisor(i);for(let n=0,r=0;n<o;n++){let o=e.slice(r,r+h-i+(n<l?0:1));r+=o.length;const s=t.reedSolomonComputeRemainder(o,u);n<l&&o.push(0),c.push(o.concat(s))}let d=[];for(let e=0;e<c[0].length;e++)c.forEach(((t,n)=>{(e!=h-i||n>=l)&&d.push(t[e])}));return s(d.length==a),d}drawCodewords(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");let n=0;for(let t=this.size-1;t>=1;t-=2){6==t&&(t=5);for(let r=0;r<this.size;r++)for(let s=0;s<2;s++){const i=t-s,a=0==(t+1&2)?this.size-1-r:r;!this.isFunction[a][i]&&n<8*e.length&&(this.modules[a][i]=o(e[n>>>3],7-(7&n)),n++)}}s(n==8*e.length)}applyMask(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(let t=0;t<this.size;t++)for(let n=0;n<this.size;n++){let r;switch(e){case 0:r=(n+t)%2==0;break;case 1:r=t%2==0;break;case 2:r=n%3==0;break;case 3:r=(n+t)%3==0;break;case 4:r=(Math.floor(n/3)+Math.floor(t/2))%2==0;break;case 5:r=n*t%2+n*t%3==0;break;case 6:r=(n*t%2+n*t%3)%2==0;break;case 7:r=((n+t)%2+n*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][n]&&r&&(this.modules[t][n]=!this.modules[t][n])}}getPenaltyScore(){let e=0;for(let n=0;n<this.size;n++){let r=!1,o=0,s=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[n][i]==r?(o++,5==o?e+=t.PENALTY_N1:o>5&&e++):(this.finderPenaltyAddHistory(o,s),r||(e+=this.finderPenaltyCountPatterns(s)*t.PENALTY_N3),r=this.modules[n][i],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,s)*t.PENALTY_N3}for(let n=0;n<this.size;n++){let r=!1,o=0,s=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[i][n]==r?(o++,5==o?e+=t.PENALTY_N1:o>5&&e++):(this.finderPenaltyAddHistory(o,s),r||(e+=this.finderPenaltyCountPatterns(s)*t.PENALTY_N3),r=this.modules[i][n],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,s)*t.PENALTY_N3}for(let n=0;n<this.size-1;n++)for(let r=0;r<this.size-1;r++){const o=this.modules[n][r];o==this.modules[n][r+1]&&o==this.modules[n+1][r]&&o==this.modules[n+1][r+1]&&(e+=t.PENALTY_N2)}let n=0;for(const e of this.modules)n=e.reduce(((e,t)=>e+(t?1:0)),n);const r=this.size*this.size,o=Math.ceil(Math.abs(20*n-10*r)/r)-1;return s(0<=o&&o<=9),e+=o*t.PENALTY_N4,s(0<=e&&e<=2568888),e}getAlignmentPatternPositions(){if(1==this.version)return[];{const e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2));let n=[6];for(let r=this.size-7;n.length<e;r-=t)n.splice(1,0,r);return n}}static getNumRawDataModules(e){if(e<t.MIN_VERSION||e>t.MAX_VERSION)throw new RangeError("Version number out of range");let n=(16*e+128)*e+64;if(e>=2){const t=Math.floor(e/7)+2;n-=(25*t-10)*t-55,e>=7&&(n-=36)}return s(208<=n&&n<=29648),n}static getNumDataCodewords(e,n){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][e]}static reedSolomonComputeDivisor(e){if(e<1||e>255)throw new RangeError("Degree out of range");let n=[];for(let t=0;t<e-1;t++)n.push(0);n.push(1);let r=1;for(let o=0;o<e;o++){for(let e=0;e<n.length;e++)n[e]=t.reedSolomonMultiply(n[e],r),e+1<n.length&&(n[e]^=n[e+1]);r=t.reedSolomonMultiply(r,2)}return n}static reedSolomonComputeRemainder(e,n){let r=n.map((e=>0));for(const o of e){const e=o^r.shift();r.push(0),n.forEach(((n,o)=>r[o]^=t.reedSolomonMultiply(n,e)))}return r}static reedSolomonMultiply(e,t){if(e>>>8!=0||t>>>8!=0)throw new RangeError("Byte out of range");let n=0;for(let r=7;r>=0;r--)n=n<<1^285*(n>>>7),n^=(t>>>r&1)*e;return s(n>>>8==0),n}finderPenaltyCountPatterns(e){const t=e[1];s(t<=3*this.size);const n=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(n&&e[0]>=4*t&&e[6]>=t?1:0)+(n&&e[6]>=4*t&&e[0]>=t?1:0)}finderPenaltyTerminateAndCount(e,t,n){return e&&(this.finderPenaltyAddHistory(t,n),t=0),t+=this.size,this.finderPenaltyAddHistory(t,n),this.finderPenaltyCountPatterns(n)}finderPenaltyAddHistory(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}};let n=t;function r(e,t,n){if(t<0||t>31||e>>>t!=0)throw new RangeError("Value out of range");for(let r=t-1;r>=0;r--)n.push(e>>>r&1)}function o(e,t){return 0!=(e>>>t&1)}function s(e){if(!e)throw new Error("Assertion error")}n.MIN_VERSION=1,n.MAX_VERSION=40,n.PENALTY_N1=3,n.PENALTY_N2=3,n.PENALTY_N3=40,n.PENALTY_N4=10,n.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],n.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],e.QrCode=n;const i=class{constructor(e,t,n){if(this.mode=e,this.numChars=t,this.bitData=n,t<0)throw new RangeError("Invalid argument");this.bitData=n.slice()}static makeBytes(e){let t=[];for(const n of e)r(n,8,t);return new i(i.Mode.BYTE,e.length,t)}static makeNumeric(e){if(!i.isNumeric(e))throw new RangeError("String contains non-numeric characters");let t=[];for(let n=0;n<e.length;){const o=Math.min(e.length-n,3);r(parseInt(e.substr(n,o),10),3*o+1,t),n+=o}return new i(i.Mode.NUMERIC,e.length,t)}static makeAlphanumeric(e){if(!i.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");let t,n=[];for(t=0;t+2<=e.length;t+=2){let o=45*i.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t));o+=i.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t+1)),r(o,11,n)}return t<e.length&&r(i.ALPHANUMERIC_CHARSET.indexOf(e.charAt(t)),6,n),new i(i.Mode.ALPHANUMERIC,e.length,n)}static makeSegments(e){return""==e?[]:i.isNumeric(e)?[i.makeNumeric(e)]:i.isAlphanumeric(e)?[i.makeAlphanumeric(e)]:[i.makeBytes(i.toUtf8ByteArray(e))]}static makeEci(e){let t=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<128)r(e,8,t);else if(e<16384)r(2,2,t),r(e,14,t);else{if(!(e<1e6))throw new RangeError("ECI assignment value out of range");r(6,3,t),r(e,21,t)}return new i(i.Mode.ECI,0,t)}static isNumeric(e){return i.NUMERIC_REGEX.test(e)}static isAlphanumeric(e){return i.ALPHANUMERIC_REGEX.test(e)}getData(){return this.bitData.slice()}static getTotalBits(e,t){let n=0;for(const r of e){const e=r.mode.numCharCountBits(t);if(r.numChars>=1<<e)return 1/0;n+=4+e+r.bitData.length}return n}static toUtf8ByteArray(e){e=encodeURI(e);let t=[];for(let n=0;n<e.length;n++)"%"!=e.charAt(n)?t.push(e.charCodeAt(n)):(t.push(parseInt(e.substr(n+1,2),16)),n+=2);return t}};let a=i;a.NUMERIC_REGEX=/^[0-9]*$/,a.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,a.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",e.QrSegment=a})(r||(r={})),(e=>{let t;(e=>{const t=class{constructor(e,t){this.ordinal=e,this.formatBits=t}};let n=t;n.LOW=new t(0,1),n.MEDIUM=new t(1,0),n.QUARTILE=new t(2,3),n.HIGH=new t(3,2),e.Ecc=n})(t=e.QrCode||(e.QrCode={}))})(r||(r={})),(e=>{let t;(e=>{const t=class{constructor(e,t){this.modeBits=e,this.numBitsCharCount=t}numCharCountBits(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}};let n=t;n.NUMERIC=new t(1,[10,12,14]),n.ALPHANUMERIC=new t(2,[9,11,13]),n.BYTE=new t(4,[8,16,16]),n.KANJI=new t(8,[8,10,12]),n.ECI=new t(7,[0,0,0]),e.Mode=n})(t=e.QrSegment||(e.QrSegment={}))})(r||(r={}));var f=r,m={L:f.QrCode.Ecc.LOW,M:f.QrCode.Ecc.MEDIUM,Q:f.QrCode.Ecc.QUARTILE,H:f.QrCode.Ecc.HIGH},g=128,w="L",E="#FFFFFF",v="#000000",M=!1,p=4,C=.1;function R(e){const t=e,{value:n,size:r=g,level:o=w,bgColor:i=E,fgColor:a=v,includeMargin:l=M,imageSettings:h}=t,c=d(t,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]);let R=f.QrCode.encodeText(n,m[o]).getModules();const N=l?p:0,P=R.length+2*N,y=function(e,t,n,r){if(null==r)return null;const o=n?p:0,s=e.length+2*o,i=Math.floor(t*C),a=s/t,l=(r.width||i)*a,h=(r.height||i)*a,c=null==r.x?e.length/2-l/2:r.x*a,u=null==r.y?e.length/2-h/2:r.y*a;let d=null;if(r.excavate){let e=Math.floor(c),t=Math.floor(u);d={x:e,y:t,w:Math.ceil(l+c-e),h:Math.ceil(h+u-t)}}return{x:c,y:u,h,w:l,excavation:d}}(R,r,l,h);let A=null;var z,O;null!=h&&null!=y&&(null!=y.excavation&&(z=R,O=y.excavation,R=z.slice().map(((e,t)=>t<O.y||t>=O.y+O.h?e:e.map(((e,t)=>(t<O.x||t>=O.x+O.w)&&e))))),A=s().createElement("image",{xlinkHref:h.src,height:y.h,width:y.w,x:y.x+N,y:y.y+N,preserveAspectRatio:"none"}));const b=function(e,t=0){const n=[];return e.forEach((function(e,r){let o=null;e.forEach((function(s,i){if(!s&&null!==o)return n.push(`M${o+t} ${r+t}h${i-o}v1H${o+t}z`),void(o=null);if(i!==e.length-1)s&&null===o&&(o=i);else{if(!s)return;null===o?n.push(`M${i+t},${r+t} h1v1H${i+t}z`):n.push(`M${o+t},${r+t} h${i+1-o}v1H${o+t}z`)}}))})),n.join("")}(R,N);return s().createElement("svg",u({height:r,width:r,viewBox:`0 0 ${P} ${P}`},c),s().createElement("path",{fill:i,d:`M0,0 h${P}v${P}H0z`,shapeRendering:"crispEdges"}),s().createElement("path",{fill:a,d:b,shapeRendering:"crispEdges"}),A)}!function(){try{(new Path2D).addPath(new Path2D)}catch(e){return!1}}()}}]);
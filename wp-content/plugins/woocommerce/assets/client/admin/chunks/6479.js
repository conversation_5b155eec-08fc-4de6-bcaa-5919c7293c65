"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6479],{96479:(e,t,a)=>{a.r(t),a.d(t,{ProductsApp:()=>Te});var i=a(69307),s=a(86802),n=a(12238),l=a(74776);const{lock:r,unlock:o}=(0,l.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site");var c=a(8850),u=a(50529),d=a(37798),m=a(65736),p=a(9818),_=a(87608),v=a.n(_),g=a(55609),f=a(86243),b=a(34121),y=a(36216),h=a(91676),E=a(96073),w=a(65343);const k="table",N="list",x="is",S={[k]:{layout:{primaryField:"name",styles:{name:{maxWidth:300}}}},grid:{layout:{mediaField:"featured-image",primaryField:"name"}},[N]:{layout:{primaryField:"name",mediaField:"featured-image"}}},T={type:k,search:"",filters:[],page:1,perPage:20,sort:{field:"date",direction:"desc"},fields:["name","sku","status","date"],layout:S[N].layout};function P({postType:e}){const t=(0,p.useSelect)((t=>{const{getPostType:a}=t(d.store),i=a(e);return null==i?void 0:i.labels}),[e]);return(0,i.useMemo)((()=>[{title:(null==t?void 0:t.all_items)||(0,m.__)("All items","woocommerce"),slug:"all",icon:f.Z,view:{...T}},{title:(0,m.__)("Published","woocommerce"),slug:"published",icon:b.Z,view:{...T,filters:[{field:"status",operator:x,value:"publish"}]}},{title:(0,m.__)("Scheduled","woocommerce"),slug:"future",icon:y.Z,view:{...T,filters:[{field:"status",operator:x,value:"future"}]}},{title:(0,m.__)("Drafts","woocommerce"),slug:"drafts",icon:h.Z,view:{...T,filters:[{field:"status",operator:x,value:"draft"}]}},{title:(0,m.__)("Private","woocommerce"),slug:"private",icon:E.Z,view:{...T,filters:[{field:"status",operator:x,value:"private"}]}},{title:(0,m.__)("Trash","woocommerce"),slug:"trash",icon:w.Z,view:{...T,type:k,layout:S[k].layout,filters:[{field:"status",operator:x,value:"trash"}]}}]),[t])}const A=[{value:"draft",label:(0,m.__)("Draft","woocommerce")},{value:"future",label:(0,m.__)("Scheduled","woocommerce")},{value:"private",label:(0,m.__)("Private","woocommerce")},{value:"publish",label:(0,m.__)("Published","woocommerce")},{value:"trash",label:(0,m.__)("Trash","woocommerce")}],C=[{id:"name",label:(0,m.__)("Name","woocommerce"),enableHiding:!1,type:"text",render:function({item:e}){return(0,i.createElement)(i.Fragment,null,e.name)}},{id:"sku",label:(0,m.__)("SKU","woocommerce"),enableHiding:!1,enableSorting:!1,render:({item:e})=>(0,i.createElement)(i.Fragment,null,e.sku)},{id:"date",label:(0,m.__)("Date","woocommerce"),render:({item:e})=>(0,i.createElement)("time",null,e.date_created)},{label:(0,m.__)("Status","woocommerce"),id:"status",getValue:({item:e})=>{var t,a;return null!==(a=null===(t=A.find((({value:t})=>t===e.status)))||void 0===t?void 0:t.label)&&void 0!==a?a:e.status},elements:A,filterBy:{operators:[x]},enableSorting:!1}];var L=a(78842);const{useHistory:I,useLocation:R}=o(s.privateApis),F=({postType:e})=>{const t=I(),a=R();return(0,i.useMemo)((()=>({id:"edit-product",label:(0,m.__)("Edit","woocommerce"),isPrimary:!0,icon:L.Z,supportsBulk:!0,isEligible:e=>"trash"!==e.status,callback(i){const s=i[0];t.push({...a.params,postId:s.id,postType:e,quickEdit:!0})}})),[t,a.params])},{NavigableRegion:H,usePostActions:Z}=o(n.privateApis),{useHistory:D,useLocation:U}=o(s.privateApis),V=25,B=[],M=(e,t)=>{var a;return null===(a=e.find((({slug:e})=>e===t)))||void 0===a?void 0:a.view};function z(e){return e.id.toString()}function j({subTitle:e,className:t,hideTitleFromUI:a=!1}){const s=D(),n=U(),{postId:l,quickEdit:r=!1,postType:o="product",isCustom:_,activeView:f="all"}=n.params,[b,y]=(0,i.useState)([l]),[h,E]=function(e){const{params:{activeView:t="all",isCustom:a="false",layout:s}}=U(),n=D(),l=P({postType:e}),[r,o]=(0,i.useState)((()=>{var e;const a=null!==(e=M(l,t))&&void 0!==e?e:{type:null!=s?s:N},i=null!=s?s:a.type;return{...a,type:i}})),c=(0,i.useCallback)((e=>{const{params:t}=n.getLocationWithParams();(e.type!==N||(null==t?void 0:t.layout))&&e.type!==(null==t?void 0:t.layout)&&n.push({...t,layout:e.type}),o(e)}),[n,a]);return(0,i.useEffect)((()=>{o((e=>({...e,type:null!=s?s:N})))}),[s]),(0,i.useEffect)((()=>{const e=M(l,t);if(e){const t=null!=s?s:e.type;o({...e,type:t})}}),[t,a,s,l]),[r,c,c]}(o),w=(0,i.useMemo)((()=>{var e,t,a,i;const s={};null===(e=h.filters)||void 0===e||e.forEach((e=>{"status"===e.field&&(s.status=Array.isArray(e.value)?e.value.join(","):e.value)}));const n="name"===(null===(t=h.sort)||void 0===t?void 0:t.field)?"title":null===(a=h.sort)||void 0===a?void 0:a.field;return{per_page:h.perPage,page:h.page,order:null===(i=h.sort)||void 0===i?void 0:i.direction,orderby:n,search:h.search,...s}}),[n.params,h]),k=(0,i.useCallback)((e=>{y(e),s.push({...n.params,postId:e.join(",")})}),[s,n.params,null==h?void 0:h.type]),{records:x,totalCount:T,isLoading:A}=(0,p.useSelect)((e=>{const{getProducts:t,getProductsTotalCount:a,isResolving:i}=e("wc/admin/products");return{records:t(w),totalCount:a(w),isLoading:i("getProducts",[w])}}),[w]),L=(0,i.useMemo)((()=>({totalItems:T,totalPages:Math.ceil(T/(h.perPage||V))})),[T,h.perPage]),{labels:I,canCreateRecord:R}=(0,p.useSelect)((e=>{const{getPostType:t,canUser:a}=e(d.store),i=t(o);return{labels:null==i?void 0:i.labels,canCreateRecord:a("create",{kind:"postType",name:o})}}),[o]),j=Z({postType:o,context:"list"}),W=F({postType:o}),q=(0,i.useMemo)((()=>[W,...j]),[j,W]),O=v()("edit-site-page",t);return(0,i.createElement)(H,{className:O,ariaLabel:(0,m.__)("Products","woocommerce")},(0,i.createElement)("div",{className:"edit-site-page-content"},!a&&(0,i.createElement)(g.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(g.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(g.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,m.__)("Products","woocommerce")),(0,i.createElement)(g.FlexItem,{className:"edit-site-page-header__actions"},(null==I?void 0:I.add_new_item)&&R&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)(g.Button,{variant:"primary",disabled:!0,__next40pxDefaultSize:!0},I.add_new_item)))),e&&(0,i.createElement)(g.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),(0,i.createElement)(c.Z,{key:f+_,paginationInfo:L,fields:C,data:x||B,isLoading:A,view:h,actions:q,onChangeView:E,onChangeSelection:k,getItemId:z,selection:b,defaultLayouts:S,header:(0,i.createElement)(g.Button,{size:"compact",isPressed:r,icon:u.Z,label:(0,m.__)("Toggle details panel","woocommerce"),onClick:()=>{s.push({...n.params,quickEdit:!r||void 0})}})})))}var W=a(23894),q=a(44731);const{NavigableRegion:O}=o(n.privateApis),G={type:"panel",fields:["name","status"]};function K({subTitle:e,actions:t,className:a,hideTitleFromUI:s=!0,postType:n,postId:l=""}){const r=v()("edit-product-page",a,{"is-empty":!l}),o=(0,i.useMemo)((()=>l.split(",")),[l]),{initialEdits:c}=(0,p.useSelect)((e=>({initialEdits:1===o.length?e("wc/admin/products").getProduct(o[0]):null})),[n,o]),[u,d]=(0,i.useState)({}),_=(0,i.useMemo)((()=>({...c,...u})),[c,u]),f=!(0,W.h)(_,C,G);return(0,i.createElement)(O,{className:r,ariaLabel:(0,m.__)("Product Edit","woocommerce")},(0,i.createElement)("div",{className:"edit-product-content"},!s&&(0,i.createElement)(g.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(g.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(g.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,m.__)("Product Edit","woocommerce")),(0,i.createElement)(g.FlexItem,{className:"edit-site-page-header__actions"},t)),e&&(0,i.createElement)(g.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),!l&&(0,i.createElement)("p",null,(0,m.__)("Select a product to edit","woocommerce")),l&&(0,i.createElement)(g.__experimentalVStack,{spacing:4,as:"form",onSubmit:async e=>{e.preventDefault(),(0,W.h)(_,C,G)&&d({})}},(0,i.createElement)(q.Z,{data:_,fields:C,form:G,onChange:d}),(0,i.createElement)(g.FlexItem,null,(0,i.createElement)(g.Button,{variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:f,__next40pxDefaultSize:!0},(0,m.__)("Update","woocommerce"))))))}var Q=a(96483),J=a(41649),X=a(8213),Y=a(38192),$=a(74621);const{useHistory:ee}=o(s.privateApis);function te({className:e,icon:t,withChevron:a=!1,suffix:s,uid:n,params:l,onClick:r,children:o,...c}){const u=ee();return(0,i.createElement)(g.__experimentalItem,{className:v()("edit-site-sidebar-navigation-item",{"with-suffix":!a&&s},e),onClick:function(e){r?r(e):l&&(e.preventDefault(),u.push(l))},id:n,...c},(0,i.createElement)(g.__experimentalHStack,{justify:"flex-start"},t&&(0,i.createElement)(X.Z,{style:{fill:"currentcolor"},icon:t,size:24}),(0,i.createElement)(g.FlexBlock,null,o),a&&(0,i.createElement)(X.Z,{icon:(0,m.isRTL)()?Y.Z:$.Z,className:"edit-site-sidebar-navigation-item__drilldown-indicator",size:24}),!a&&s))}const{useHistory:ae,useLocation:ie}=o(s.privateApis);function se({title:e,slug:t,customViewId:a,type:s,icon:n,isActive:l,isCustom:r,suffix:o}){var c;const{params:{postType:u,page:d}}=ie(),m=n||(null===(c=J.K.find((e=>e.type===s)))||void 0===c?void 0:c.icon);let p=r?a:t;"all"===p&&(p=void 0);const _=function(e,t,a=!1){const i=ae(),s=(0,Q.getQueryArgs)(window.location.href),n=(0,Q.removeQueryArgs)(window.location.href,...Object.keys(s));return{href:(0,Q.addQueryArgs)(n,e),onClick:function(s){null==s||s.preventDefault(),a?i.replace(e,t):i.push(e,t)}}}({page:d,postType:u,layout:s,activeView:p,isCustom:r?"true":void 0});return(0,i.createElement)(g.__experimentalHStack,{justify:"flex-start",className:v()("edit-site-sidebar-dataviews-dataview-item",{"is-selected":l})},(0,i.createElement)(te,{icon:m,..._,"aria-current":l?"true":void 0},e),o)}const{useLocation:ne}=o(s.privateApis);function le(){const{params:{postType:e="product",activeView:t="all",isCustom:a="false"}}=ne(),s=P({postType:e});if(!e)return null;const n="true"===a;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(g.__experimentalItemGroup,null,s.map((e=>(0,i.createElement)(se,{key:e.slug,slug:e.slug,title:e.title,icon:e.icon,type:e.view.type,isActive:!n&&e.slug===t,isCustom:!1})))))}var re=a(51619),oe=a(74059);function ce(e){return(0,i.createElement)(g.Button,{...e,className:v()("edit-site-sidebar-button",e.className)})}const{useHistory:ue,useLocation:de}=o(s.privateApis);function me({isRoot:e,title:t,actions:a,meta:s,content:n,footer:l,description:r,backPath:c}){var u;const{dashboardLink:d,dashboardLinkText:_}=(0,p.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site"));return{dashboardLink:t().__experimentalDashboardLink,dashboardLinkText:t().__experimentalDashboardLinkText}}),[]),f=de(),b=ue(),y=null!=c?c:null===(u=f.state)||void 0===u?void 0:u.backPath,h=(0,m.isRTL)()?re.Z:oe.Z;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(g.__experimentalVStack,{className:v()("edit-site-sidebar-navigation-screen__main",{"has-footer":!!l}),spacing:0,justify:"flex-start"},(0,i.createElement)(g.__experimentalHStack,{spacing:3,alignment:"flex-start",className:"edit-site-sidebar-navigation-screen__title-icon"},!e&&(0,i.createElement)(ce,{onClick:()=>{b.push(y)},icon:h,label:(0,m.__)("Back","woocommerce"),showTooltip:!1}),e&&(0,i.createElement)(ce,{icon:h,label:_||(0,m.__)("Go to the Dashboard","woocommerce"),href:d||"index.php"}),(0,i.createElement)(g.__experimentalHeading,{className:"edit-site-sidebar-navigation-screen__title",color:"#e0e0e0",level:1,size:20},t),a&&(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__actions"},a)),s&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__meta"},s)),(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__content"},r&&(0,i.createElement)("p",{className:"edit-site-sidebar-navigation-screen__description"},r),n)),l&&(0,i.createElement)("footer",{className:"edit-site-sidebar-navigation-screen__footer"},l))}const{useLocation:pe}=o(s.privateApis);var _e=a(94333);function ve({children:e}){const t=(0,i.useRef)(null);return(0,i.createElement)("div",{ref:t,className:"edit-site-sidebar__screen-wrapper"},e)}function ge({routeKey:e,children:t}){return(0,i.createElement)("div",{className:"edit-site-sidebar__content"},(0,i.createElement)(ve,{key:e},t))}var fe=a(22629),be=a(45199);const ye=function({className:e}){const{isRequestingSite:t,siteIconUrl:a}=(0,p.useSelect)((e=>{const{getEntityRecord:t}=e(d.store),a=t("root","__unstableBase",void 0);return{isRequestingSite:!a,siteIconUrl:null==a?void 0:a.site_icon_url}}),[]);if(t&&!a)return(0,i.createElement)("div",{className:"edit-site-site-icon__image"});const s=a?(0,i.createElement)("img",{className:"edit-site-site-icon__image",alt:(0,m.__)("Site Icon","woocommerce"),src:a}):(0,i.createElement)(g.Icon,{className:"edit-site-site-icon__icon",icon:be.Z,size:48});return(0,i.createElement)("div",{className:v()(e,"edit-site-site-icon")},s)},he=(0,i.memo)((0,i.forwardRef)((({isTransparent:e},t)=>{const{dashboardLink:a,homeUrl:s,siteTitle:n}=(0,p.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site")),{getSite:a,getUnstableBase:i}=e(d.store),s=a(),n=i();return{dashboardLink:t().__experimentalDashboardLink||"index.php",homeUrl:null==n?void 0:n.home,siteTitle:!(null==s?void 0:s.title)&&(null==s?void 0:s.url)?(0,Q.filterURLForDisplay)(null==s?void 0:s.url):null==s?void 0:s.title}}),[]);return(0,i.createElement)("div",{className:"edit-site-site-hub"},(0,i.createElement)(g.__experimentalHStack,{justify:"flex-start",spacing:"0"},(0,i.createElement)("div",{className:v()("edit-site-site-hub__view-mode-toggle-container",{"has-transparent-background":e})},(0,i.createElement)(g.Button,{ref:t,href:a,label:(0,m.__)("Go to the Dashboard","woocommerce"),className:"edit-site-layout__view-mode-toggle",style:{transform:"scale(0.5)",borderRadius:4}},(0,i.createElement)(ye,{className:"edit-site-layout__view-mode-toggle-icon"}))),(0,i.createElement)(g.__experimentalHStack,null,(0,i.createElement)("div",{className:"edit-site-site-hub__title"},(0,i.createElement)(g.Button,{variant:"link",href:s,target:"_blank"},n&&(0,fe.decodeEntities)(n),(0,i.createElement)(g.VisuallyHidden,{as:"span"},(0,m.__)("(opens in a new tab)","woocommerce")))))))}))),{NavigableRegion:Ee}=o(n.privateApis),we=.3;function ke({route:e}){const[t]=(0,_e.useResizeObserver)(),a=(0,i.useRef)(null),s=(0,_e.useViewportMatch)("medium","<"),l=(0,_e.useReducedMotion)(),{key:r,areas:o,widths:c}=e;return(0,i.createElement)(i.Fragment,null,t,(0,i.createElement)("div",{className:"edit-site-layout"},(0,i.createElement)("div",{className:"edit-site-layout__content"},(!s||!o.mobile)&&(0,i.createElement)(Ee,{ariaLabel:(0,m.__)("Navigation","woocommerce"),className:"edit-site-layout__sidebar-region"},(0,i.createElement)(g.__unstableAnimatePresence,null,(0,i.createElement)(g.__unstableMotion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{type:"tween",duration:l||s?0:we,ease:"easeOut"},className:"edit-site-layout__sidebar"},(0,i.createElement)(he,{ref:a,isTransparent:!1}),(0,i.createElement)(ge,{routeKey:r},o.sidebar)))),(0,i.createElement)(n.EditorSnackbars,null),!s&&o.content&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:null==c?void 0:c.content}},o.content),!s&&o.edit&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:null==c?void 0:c.edit}},o.edit))))}const{RouterProvider:Ne}=o(s.privateApis),{GlobalStylesProvider:xe}=o(n.privateApis);function Se(){const e=function(){const{params:e={}}=pe(),{postType:t="product",layout:a="table",canvas:s,quickEdit:n,postId:l}=e;if(["product"].includes(t)){const e="list"===a||!a;return{key:"products-list",areas:{sidebar:(0,i.createElement)(me,{title:"Products",isRoot:!0,content:(0,i.createElement)(le,null)}),content:(0,i.createElement)(j,null),preview:!1,mobile:(0,i.createElement)(j,{postType:t}),edit:n&&(0,i.createElement)(K,{postType:t,postId:l})},widths:{content:e?380:void 0,edit:n&&!e?380:void 0}}}return{key:"default",areas:{sidebar:()=>null,preview:!1,mobile:"edit"===s}}}();return(0,i.createElement)(ke,{route:e})}function Te(){return(0,i.createElement)(xe,null,(0,i.createElement)(n.UnsavedChangesWarning,null),(0,i.createElement)(Ne,null,(0,i.createElement)(Se,null)))}}}]);
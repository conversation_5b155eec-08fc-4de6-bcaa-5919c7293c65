"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4272],{93914:(e,t,o)=>{o.d(t,{Z:()=>l});var r=o(69307),c=o(65736),a=o(55609),n=o(14812);const l=({onCancel:e,onImport:t})=>(0,r.createElement)(a.<PERSON>,{className:"woocommerce-products-load-sample-product-confirm-modal",overlayClassName:"woocommerce-products-load-sample-product-confirm-modal-overlay",title:(0,c.__)("Load sample products","woocommerce"),onRequestClose:e},(0,r.createElement)(n.Text,{className:"woocommerce-confirmation-modal__message"},(0,c.__)("We’ll import images from WooCommerce.com to set up your sample products.","woocommerce")),(0,r.createElement)("div",{className:"woocommerce-confirmation-modal-actions"},(0,r.createElement)(a.Button,{isSecondary:!0,onClick:e},(0,c.__)("Cancel","woocommerce")),(0,r.createElement)(a.Button,{isPrimary:!0,onClick:t},(0,c.__)("Import sample products","woocommerce"))))},69359:(e,t,o)=>{o.d(t,{Z:()=>i});var r=o(69307),c=o(65736),a=o(55609),n=o(36840),l=o(14812);const i=()=>(0,r.createElement)(a.Modal,{className:"woocommerce-products-load-sample-product-modal",overlayClassName:"woocommerce-products-load-sample-product-modal-overlay",title:"",onRequestClose:()=>{}},(0,r.createElement)(n.Spinner,{color:"#007cba",size:48}),(0,r.createElement)(l.Text,{className:"woocommerce-load-sample-product-modal__title"},(0,c.__)("Loading sample products","woocommerce")),(0,r.createElement)(l.Text,{className:"woocommerce-load-sample-product-modal__description"},(0,c.__)("We are loading 9 sample products into your store","woocommerce")))},35192:(e,t,o)=>{o.d(t,{Z:()=>d});var r=o(65736),c=o(86989),a=o.n(c),n=o(67221),l=o(9818),i=o(69307),s=o(14599),m=o(69987);const d=({redirectUrlAfterSuccess:e})=>{const[t,o]=(0,i.useState)(!1),{createNotice:c}=(0,l.useDispatch)("core/notices"),{recordCompletionTime:d}=(0,m.Z)("products");return{loadSampleProduct:async()=>{(0,s.recordEvent)("tasklist_add_product",{method:"sample_product"}),d(),o(!0);try{if(await a()({path:`${n.WC_ADMIN_NAMESPACE}/onboarding/tasks/import_sample_products`,method:"POST"}),e)return void(window.location.href=e)}catch(e){const t=e instanceof Error&&e.message?e.message:(0,r.__)("There was an error importing the sample products","woocommerce");c("error",t)}o(!1)},isLoadingSampleProducts:t}}},59879:(e,t,o)=>{o.d(t,{RC:()=>w,xz:()=>u,x:()=>v,Ir:()=>E,Yc:()=>h,wW:()=>_,M5:()=>p,T:()=>k});var r=o(69307),c=o(65736),a=o(40031),n=o(910),l=o(71896),i=o(23374),s=o(83619),m=o(14599),d=o(74617);const p=Object.freeze([{key:"physical",title:(0,c.__)("Physical product","woocommerce"),content:(0,c.__)("A tangible item that gets delivered to customers.","woocommerce"),before:(0,r.createElement)(a.Z,null),after:(0,r.createElement)(i.Z,{icon:s.Z})},{key:"digital",title:(0,c.__)("Digital product","woocommerce"),content:(0,c.__)("A digital product like service, downloadable book, music or video.","woocommerce"),before:(0,r.createElement)(n.Z,null),after:(0,r.createElement)(i.Z,{icon:s.Z})},{key:"variable",title:(0,c.__)("Variable product","woocommerce"),content:(0,c.__)("A product with variations like color or size.","woocommerce"),before:(0,r.createElement)(l.Z,null),after:(0,r.createElement)(i.Z,{icon:s.Z})},{key:"grouped",title:(0,c.__)("Grouped product","woocommerce"),content:(0,c.__)("A collection of related products.","woocommerce"),before:(0,r.createElement)((()=>(0,r.createElement)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"mask0_1133_132667",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"2",y:"2",width:"21",height:"20"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.5 2.34497L10.84 7.99497V3.65497H2.84003V11.655H10.84V7.99497L16.5 13.655H12.84V21.655H20.84V13.655H16.5L22.16 7.99497L16.5 2.34497ZM19.33 8.00497L16.5 5.17497L13.67 8.00497L16.5 10.835L19.33 8.00497ZM8.84003 9.65497V5.65497H4.84003V9.65497H8.84003ZM18.84 15.655V19.655H14.84V15.655H18.84ZM8.84003 19.655V15.655H4.84003V19.655H8.84003ZM2.84003 13.655H10.84V21.655H2.84003V13.655Z",fill:"white"})),(0,r.createElement)("g",{mask:"url(#mask0_1133_132667)"},(0,r.createElement)("rect",{x:"0.5",width:"24",height:"24"})))),null),after:(0,r.createElement)(i.Z,{icon:s.Z})},{key:"external",title:(0,c.__)("External product","woocommerce"),content:(0,c.__)("Link a product to an external website.","woocommerce"),before:(0,r.createElement)((()=>(0,r.createElement)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"mask0_1133_132681",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"2",y:"7",width:"21",height:"10"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.5 15H7.5C5.85 15 4.5 13.65 4.5 12C4.5 10.35 5.85 9 7.5 9H11.5V7H7.5C4.74 7 2.5 9.24 2.5 12C2.5 14.76 4.74 17 7.5 17H11.5V15ZM17.5 7H13.5V9H17.5C19.15 9 20.5 10.35 20.5 12C20.5 13.65 19.15 15 17.5 15H13.5V17H17.5C20.26 17 22.5 14.76 22.5 12C22.5 9.24 20.26 7 17.5 7ZM16.5 11H8.5V13H16.5V11Z",fill:"white"})),(0,r.createElement)("g",{mask:"url(#mask0_1133_132681)"},(0,r.createElement)("rect",{x:"0.5",width:"24",height:"24"})))),null),after:(0,r.createElement)(i.Z,{icon:s.Z})}]),u=((0,c.__)("can’t decide?","woocommerce"),(0,c.__)("Load sample products and see what they look like in your store.","woocommerce"),(0,r.createElement)((()=>(0,r.createElement)("svg",{width:"25",height:"24",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("mask",{id:"mask0_1133_132689",style:{maskType:"alpha"},maskUnits:"userSpaceOnUse",x:"5",y:"2",width:"15",height:"20"},(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.5 2C8.64 2 5.5 5.14 5.5 9C5.5 11.38 6.69 13.47 8.5 14.74V17C8.5 17.55 8.95 18 9.5 18H15.5C16.05 18 16.5 17.55 16.5 17V14.74C18.31 13.47 19.5 11.38 19.5 9C19.5 5.14 16.36 2 12.5 2ZM9.5 21C9.5 21.55 9.95 22 10.5 22H14.5C15.05 22 15.5 21.55 15.5 21V20H9.5V21ZM14.5 13.7L15.35 13.1C16.7 12.16 17.5 10.63 17.5 9C17.5 6.24 15.26 4 12.5 4C9.74 4 7.5 6.24 7.5 9C7.5 10.63 8.3 12.16 9.65 13.1L10.5 13.7V16H14.5V13.7Z",fill:"white"})),(0,r.createElement)("g",{mask:"url(#mask0_1133_132689)"},(0,r.createElement)("rect",{x:"0.5",width:"24",height:"24",fill:"#757575"})))),null),(0,r.createElement)(i.Z,{icon:s.Z}),{key:"printful-advert",title:(0,r.createElement)("span",{className:"printful-sponsored__text"},(0,c.__)("Print-on-demand products","woocommerce")),content:(0,c.__)("Design and easily sell custom print products online with Printful.","woocommerce"),className:"woocommerce-products-list__item-advert",before:(0,r.createElement)("img",{className:"printful-sponsored__icon",alt:"Printful",src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQ+SURBVHgB7VbrS1tnGH9zsbqoNYkmCm7k4IiLF5zDynR4GZvzBoK2ODYZWnABtaTTmcHWZJu2H8SBa7VksjLWD1sn88NYaP+B0oqlFVGqeCcqqOA1XqImmqS/U4gcj++xKaX0y/mB+J7zPO9z+T2XHEJEiBAhQoQIEQEUFRXdPk2em5t7saKiIl9IbrFYdPn5+d+S14G0tLTahIQEf3l5eS1N3tjYyEDuSExMfHCKjZakpCQvksgmQUIajFJvb69mZ2fHyp4nJyd/qaqqUvF1+vv7r+Afc3h4mFNaWnqBLzcYDIzf7//J7XZLZ2ZmbuIclO+glGw22w+sc/a8v7+vRpBWrrysrMywvr5uDDwvLi52dnV1neXqhIaGtrtcLgl7RrIZYPESCQIvDLC4uPi9tbW1b7jv4OhyXV0dE3h2Op02r9d7JN/a2orv6en5MfCM3q3e3t7+nGtjYmKihWEYJXnVAOH4v93d3WN6KJN8dHTUxp6NRuMXCwsLn/DvbWxsNJlMpvcHBwc1U1NTrXw5bKg1Gk0zeQFkpwnZqZybmzPSZOghfUlJSefQ0JAFZTfw5UiMTcrV19e3s7y8fJlmA/16rrKy8p+BgQEnEYAggwhAjl76WfCiVNq6tLRUk5yc3BISEuKiqMxGRkbeQ3tsR0dH99FsHBwcKJDgLXIKBAPMy8v7DkEyNBkCWlQqlX+jtL+il84jkBNOUL6r09PTNRiIPwsKCpoxJD6aLbD7GYbsAnmZALOyshg0+vdCl8BIOxy3s70JveaUlJQ7MpnsqExo/tH09PSnkF3EXzJK+HF4eHinkL35+fkbWGURNBm1B+Pj4/9Ak39Ak8XGxg7C+W8jIyPX2Wefz3cGwR4gKPvKykoR2CUKhSIH7N31eDzPpxQTfC47O9u8urpajOE4MbnQO4s96gab9/myEww2NDR8CUdUyiUSCVGr1V8NDw//y30Pli7FxcU9QWCPweRfcrn8PIJmAnIMgxKtcC01NbWVCACBN2E3avnvjzGInpOZzWY7JpC6n7Ra7W2wIUcC1dz3YJFdznr8lJmjoqIeYfJtCErB1cE9vUql+h2D8eHe3p6abxs2wpBgrMPh+J/7/hiDGRkZtaCbIXTM6nS6W2DmKk0IBnJgPBRLOwlrJ4YvR/JkfHzcqtfrvwbLVAe4X43V9ik1wI6Ojnc2NzevEAGAvZsoUx0S0NLkERERDtx3hIWFPUKJ92g6uPsRgn8XPX5HyA+YvsGuuBMB2u12CwQ62iX03mxmZuZDXK4RMlxYWGiKiYlhh8uEUl0X0sOHQju+eq4hiS2aHP2cih+ApiPfgUNbW5sKy/ct0B+ClSDhXkLWzrGxMQ8CiMSKOYOdJoXeMZ36+vpZfCC8DQd+q9W60N3dzfBssH3mxeL2YKCcCFCBxKO4OmDYBxK8qIIbs7BKRIgQIUKECBFvHM8A3oX7lMGFKQ8AAAAASUVORK5CYII="}),after:(0,r.createElement)("div",{className:"woocommerce-label"},(0,c.__)("Promoted","woocommerce")),onClick:()=>{(0,m.recordEvent)("tasklist_product_printful_advert_click"),window.open("https://woocommerce.com/products/printful","_blank")}}),w={key:"import-csv",title:(0,r.createElement)("span",{className:"printful-sponsored__text"},(0,c.__)("Are you already selling somewhere else?","woocommerce")),content:(0,c.__)("Import your products from a CSV file.","woocommerce"),className:"woocommerce-products-list__item-advert",before:(0,r.createElement)((()=>(0,r.createElement)("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)("circle",{cx:"20",cy:"20",r:"20",fill:"#007CBA",fillOpacity:"0.05"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M26.5 23L26.5 28L28 28L28 23L26.5 23Z"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 23L12 28L13.5 28L13.5 23L12 23Z"}),(0,r.createElement)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12 28H28V26.5H12V28Z"}),(0,r.createElement)("path",{d:"M20.25 13L26 18.25M20.25 13L20.25 28M20.25 13L15 18.25",strokeWidth:"1.5",className:"stroke-admin-theme"}))),null),after:null,onClick:()=>{(0,m.recordEvent)("tasklist_add_product",{method:"import"}),window.location.href=(0,d.getAdminLink)("edit.php?post_type=product&page=product_importer&wc_onboarding_active_task=products")}},_=Object.freeze({physical:["physical","variable","grouped"],downloads:["digital"],"downloads,physical":["physical","digital"]}),h=_.physical,k=["physical","downloads"],E="experimental_woocommerce_tasklist_product_types",v="woocommerce_admin_task_products_after"},53389:(e,t,o)=>{o.d(t,{Z:()=>d});var r=o(69307),c=o(65736),a=o(86020),n=o(14812),l=o(76357),i=o(74617),s=o(14599),m=o(69987);const d=({items:e,onClickLoadSampleProduct:t=(()=>{}),showOtherOptions:o=!0,isTaskListItemClicked:d=!1})=>{const{recordCompletionTime:p}=(0,m.Z)("products");return(0,r.createElement)("div",{className:"woocommerce-products-stack"},d&&(0,r.createElement)("div",{className:"woocommerce-stack__overlay-spinner"},(0,r.createElement)(a.Spinner,{className:"list-overlay"})),(0,r.createElement)(a.List,{items:e}),o&&(0,r.createElement)(n.Text,{className:"woocommerce-stack__other-options"},(0,l.Z)({mixedString:(0,c.__)("Can’t find your product type? {{sbLink}}Start Blank{{/sbLink}} or {{LspLink}}Load Sample Products{{/LspLink}} to see what they look like in your store.","woocommerce"),components:{sbLink:(0,r.createElement)(a.Link,{onClick:()=>((0,s.recordEvent)("tasklist_add_product",{method:"manually"}),p(),window.location.href=(0,i.getAdminLink)("post-new.php?post_type=product&wc_onboarding_active_task=products&tutorial=true"),!1),href:"",type:"wc-admin"},(0,r.createElement)(r.Fragment,null)),LspLink:(0,r.createElement)(a.Link,{href:"",type:"wc-admin",onClick:()=>(t(),!1)},(0,r.createElement)(r.Fragment,null))}})))}},35231:(e,t,o)=>{o.d(t,{Z:()=>p});var r=o(69307),c=o(14599),a=o(9818),n=o(67221),l=o(10431),i=o(74617),s=o(73516),m=o(70319),d=o(17062);const p=(e,t=[],{onClick:o}={})=>{const{createProductByType:p,isRequesting:u}=(()=>{const{createProductFromTemplate:e}=(0,a.useDispatch)(n.ITEMS_STORE_NAME),[t,o]=(0,r.useState)(!1),c=async t=>{try{const o=await e({template_name:t,status:"draft"},{_fields:["id"]});if(o&&o.id)return(0,i.getAdminLink)(`post.php?post=${o.id}&action=edit&wc_onboarding_active_task=products&tutorial=true&tutorial_type=${t}`);throw new Error("Unexpected empty data response from server")}catch(e){(0,m.a)(e)}};return{createProductByType:async e=>{if(o(!0),("physical"===e||"variable"===e||"digital"===e||"grouped"===e||"external"===e)&&"treatment"===(await(0,s.loadExperimentAssignment)("woocommerce_product_creation_experience_pricing_to_general_202406")).variationName){const t=await c(e),o=(0,d.O3)("_feature_nonce");return void(window.location.href=t+`&product_block_editor=1&_feature_nonce=${o}`)}const t=await c(e);t&&(0,l.navigateTo)({url:t}),o(!1)},isRequesting:t}})();return{productTypes:(0,r.useMemo)((()=>e.map((e=>({...e,onClick:()=>{"function"==typeof e?.onClick?e.onClick():p(e.key),(0,c.recordEvent)("tasklist_add_product",{method:"product_template"}),(0,c.recordEvent)("tasklist_product_template_selection",{product_type:e.key,is_suggested:t.includes(e.key)}),"function"==typeof o&&o()}})))),[e,p,o,t]),isRequesting:u}}},73838:(e,t,o)=>{o.d(t,{Q:()=>n,r:()=>l});var r=o(92819),c=o(92694),a=o(59879);const n=({exclude:e}={})=>{const t=(0,c.applyFilters)(a.Ir,[]);let o=[...a.M5,...t];return e&&e?.length>0&&(o=o.filter((t=>!e.includes(t.key)))),o},l=e=>{const t=(0,r.intersection)(e,a.T).sort().join(",");return a.wW.hasOwnProperty(t)?a.wW[t]:a.Yc}},69987:(e,t,o)=>{o.d(t,{Z:()=>n});var r=o(69307),c=o(14599),a=o(53736);const n=(e,t)=>{const o=(0,r.useRef)(t||window.performance.now());return{recordCompletionTime:()=>{(0,c.recordEvent)("task_completion_time",{task_name:e,time:(0,a.Jm)(window.performance.now()-o.current)})}}}}}]);
/**
 * WooCommerce CSS Variables
 */

$woocommerce:   	#7F54B3 !default;
$green:         	#7ad03a !default;
$red:           	#a00 !default;
$orange:        	#ffba00 !default;
$blue:          	#2ea2cc !default;

$primary:           #7F54B3 !default;                                    // Primary color for buttons (alt)
$primarytext:       desaturate(lighten($primary, 50%), 18%) !default;    // Text on primary color bg

$secondary:         desaturate(lighten($primary, 40%), 21%) !default;    // Secondary buttons
$secondarytext:     desaturate(darken($secondary, 60%), 21%) !default;   // Text on secondary color bg

$highlight:         adjust-hue($primary, 150deg) !default;               // Prices, In stock labels, sales flash
$highlightext:      desaturate(lighten($highlight, 50%), 18%) !default;  // Text on highlight color bg

$contentbg:         #fff !default;                                     // Content BG - Tabs (active state)
$subtext:           #767676 !default;                                  // small, breadcrumbs etc

// export vars as CSS vars
:root {
	--woocommerce: #{$woocommerce};
	--wc-green: #{$green};
	--wc-red: #{$red};
	--wc-orange: #{$orange};
	--wc-blue: #{$blue};
	--wc-primary: #{$primary};
	--wc-primary-text: #{$primarytext};
	--wc-secondary: #{$secondary};
	--wc-secondary-text: #{$secondarytext};
	--wc-highlight: #{$highlight};
	--wc-highligh-text: #{$highlightext};
	--wc-content-bg: #{$contentbg};
	--wc-subtext: #{$subtext};
}

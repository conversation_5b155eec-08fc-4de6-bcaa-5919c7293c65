jQuery(function(e){jQuery.is_attribute_or_variation_empty=function(t){var a=!1;return t.each(function(){var t=e(this);t.hasClass("checkbox")||t.filter("[class*=search__field]").length||(t.is("select")?0===t.find(":selected").length:!t.val())&&(a=!0)}),a},jQuery.maybe_disable_save_button=function(){if(e(".woocommerce_variation_new_attribute_data").is(":visible"))t=e(".woocommerce_variation_new_attribute_data"),a=e("button.create-variations");else var t=e(".product_attributes"),a=e("button.save_attributes");var i=t.find("input, select, textarea");jQuery.is_attribute_or_variation_empty(i)?a.hasClass("disabled")||(a.addClass("disabled"),a.attr("aria-disabled",!0)):(a.removeClass("disabled"),a.removeAttr("aria-disabled"))},e("#tiptip_holder").removeAttr("style"),e("#tiptip_arrow").removeAttr("style"),e(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200,keepAlive:!0}),e(".save_attributes").tipTip({content:function(){return e(".save_attributes").hasClass("disabled")?woocommerce_admin_meta_boxes.i18n_save_attribute_variation_tip:""},fadeIn:50,fadeOut:50,delay:200,keepAlive:!0}),e(".create-variations").tipTip({content:function(){return e(".create-variations").hasClass("disabled")?woocommerce_admin_meta_boxes.i18n_save_attribute_variation_tip:""},fadeIn:50,fadeOut:50,delay:200,keepAlive:!0}),e(".wc-metaboxes-wrapper").on("click",".wc-metabox > h3",function(){var t=e(this).parent(".wc-metabox");t.hasClass("closed")?t.removeClass("closed"):t.addClass("closed"),t.hasClass("open")?t.removeClass("open"):t.addClass("open")}),e(document.body).on("wc-init-tabbed-panels",function(){e("ul.wc-tabs").show(),e("ul.wc-tabs a").on("click",function(t){t.preventDefault();var a=e(this).closest("div.panel-wrap");e("ul.wc-tabs li",a).removeClass("active"),e(this).parent().addClass("active"),e("div.panel",a).hide(),e(e(this).attr("href")).show(0,function(){e(this).trigger("woocommerce_tab_shown")})}),e("div.panel-wrap").each(function(){e(this).find("ul.wc-tabs li").eq(0).find("a").trigger("click")})}).trigger("wc-init-tabbed-panels"),e(document.body).on("wc-init-datepickers",function(){e(".date-picker-field, .date-picker").datepicker({dateFormat:"yy-mm-dd",numberOfMonths:1,showButtonPanel:!0})}).trigger("wc-init-datepickers"),e(".wc-metaboxes-wrapper").on("click",".wc-metabox h3",function(t){e(t.target).filter(":input, option, .sort").length||e(this).next(".wc-metabox-content").stop().slideToggle()}).on("click",".expand_all",function(){return e(this).closest(".wc-metaboxes-wrapper").find(".wc-metabox > .wc-metabox-content").show(),!1}).on("click",".close_all",function(){return e(this).closest(".wc-metaboxes-wrapper").find(".wc-metabox > .wc-metabox-content").hide(),!1}),e(".wc-metabox.closed").each(function(){e(this).find(".wc-metabox-content").hide()}),e("#product_attributes").on("change","select.attribute_values",jQuery.maybe_disable_save_button),e("#product_attributes, #variable_product_options").on("keyup","input, textarea",jQuery.maybe_disable_save_button),jQuery.maybe_disable_save_button()});
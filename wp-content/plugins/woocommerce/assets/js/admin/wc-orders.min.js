jQuery(function(e){if("undefined"==typeof wc_orders_params)return!1;var t=function(){e(document).on("click",".post-type-shop_order .wp-list-table tbody td, .woocommerce_page_wc-orders .wp-list-table.orders tbody td",this.onRowClick).on("click",".order-preview:not(.disabled)",this.onPreview)};t.prototype.onRowClick=function(t){if(e(t.target).filter("a, a *, .no-link, .no-link *, button, button *").length)return!0;if(window.getSelection&&window.getSelection().toString().length)return!0;var r=e(this).closest("tr").find("a.order-view").attr("href");r&&r.length&&(t.preventDefault(),t.metaKey||t.ctrlKey?window.open(r,"_blank"):window.location=r)},t.prototype.onPreview=function(){var t=e(this),r=t.data("orderId");return t.data("order-data")?e(this).WCBackboneModal({template:"wc-modal-view-order",variable:t.data("orderData")}):(t.addClass("disabled"),e.ajax({url:wc_orders_params.ajax_url,data:{order_id:r,action:"woocommerce_get_order_details",security:wc_orders_params.preview_nonce},type:"GET",success:function(r){e(".order-preview").removeClass("disabled"),r.success&&(t.data("orderData",r.data),e(this).WCBackboneModal({template:"wc-modal-view-order",variable:r.data}))}})),!1},new t});
!function(e,n,o,t){e(function(){var i=e(".wc-shipping-zone-methods"),s=e(".wc-shipping-zone-method-rows"),d=e(".wc-shipping-zone-method-save"),a=o.template("wc-shipping-zone-method-row"),c=o.template("wc-shipping-zone-method-row-blank"),h=Backbone.Model.extend({changes:{},logChanges:function(e){var n=this.changes||{};_.each(e.methods,function(e,o){n.methods=n.methods||{methods:{}},n.methods[o]=_.extend(n.methods[o]||{instance_id:o},e)}),"undefined"!=typeof e.zone_name&&(n.zone_name=e.zone_name),"undefined"!=typeof e.zone_locations&&(n.zone_locations=e.zone_locations),"undefined"!=typeof e.zone_postcodes&&(n.zone_postcodes=e.zone_postcodes),this.changes=n,this.trigger("change:methods")},save:function(){var o=_.clone(this.changes);_.has(o,"zone_locations")&&_.isEmpty(o.zone_locations)&&(o.zone_locations=[""]),e.post(t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_methods_save_changes",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,changes:o,zone_id:n.zone_id},this.onSaveResponse,"json")},onSaveResponse:function(e,o){"success"===o&&(e.success?(e.data.zone_id!==n.zone_id&&(n.zone_id=e.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+e.data.zone_id)),r.set("methods",e.data.methods),r.trigger("change:methods"),r.changes={},r.trigger("saved:methods"),window.onbeforeunload=null):window.alert(n.strings.save_failed))}}),p=Backbone.View.extend({rowTemplate:a,initialize:function(){this.listenTo(this.model,"change:methods",this.setUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.clearUnloadConfirmation),this.listenTo(this.model,"saved:methods",this.render),this.listenTo(this.model,"rerender",this.render),s.on("change",{view:this},this.updateModelOnChange),s.on("sortupdate",{view:this},this.updateModelOnSort),e(window).on("beforeunload",{view:this},this.unloadConfirmation),d.on("click",{view:this},this.onSubmit),e(document.body).on("input change","#zone_name, #zone_locations, #zone_postcodes",{view:this},this.onUpdateZone),e(document.body).on("click",".wc-shipping-zone-method-settings",{view:this},this.onConfigureShippingMethod),e(document.body).on("click",".wc-shipping-zone-add-method",{view:this},this.onAddShippingMethod),e(document.body).on("wc_backbone_modal_response",this.onConfigureShippingMethodSubmitted),e(document.body).on("wc_region_picker_update",this.onUpdateZoneRegionPicker),e(document.body).on("wc_backbone_modal_next_response",this.onAddShippingMethodSubmitted),e(document.body).on("wc_backbone_modal_before_remove",this.onCloseConfigureShippingMethod),e(document.body).on("wc_backbone_modal_back_response",this.onConfigureShippingMethodBack),e(document.body).on("change",".wc-shipping-zone-method-selector select",this.onChangeShippingMethodSelector),e(document.body).on("click",".wc-shipping-zone-postcodes-toggle",this.onTogglePostcodes),e(document.body).on("wc_backbone_modal_validation",{view:this},this.validateFormArguments),e(document.body).on("wc_backbone_modal_loaded",{view:this},this.onModalLoaded)},onUpdateZoneRegionPicker:function(e){var n=e.detail,o={};o.zone_locations=n,l.model.set("zone_locations",n),l.model.logChanges(o)},onUpdateZone:function(n){var o=n.data.view,t=o.model,i=e(this).val(),s=e(n.target).data("attribute"),d={};n.preventDefault(),d[s]=i,t.set(s,i),t.logChanges(d),o.render()},block:function(){e(this.el).block({message:null,overlayCSS:{background:"#fff",opacity:.6}})},unblock:function(){e(this.el).unblock()},render:function(){var o=_.indexBy(this.model.get("methods"),"instance_id"),t=this.model.get("zone_name"),i=this;e(".wc-shipping-zone-name").text(t||n.strings.default_zone_name),this.$el.empty(),this.unblock(),_.size(o)?(o=_.sortBy(o,function(e){return parseInt(e.method_order,10)}),e.each(o,function(e,o){"yes"===o.enabled?o.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--enabled">'+n.strings.yes+"</span>":o.enabled_icon='<span class="woocommerce-input-toggle woocommerce-input-toggle--disabled">'+n.strings.no+"</span>",i.$el.append(i.rowTemplate(o));var t=i.$el.find('tr[data-id="'+o.instance_id+'"]');if(!o.has_settings){t.find(".wc-shipping-zone-method-title > a").replaceWith("<span>"+t.find(".wc-shipping-zone-method-title > a").text()+"</span>");var s=t.find(".wc-shipping-zone-method-delete");t.find(".wc-shipping-zone-method-title .row-actions").empty().html(s)}}),this.$el.find(".wc-shipping-zone-method-delete").on("click",{view:this},this.onDeleteRow),this.$el.find(".wc-shipping-zone-method-enabled a").on("click",{view:this},this.onToggleEnabled)):i.$el.append(c),this.initTooltips()},initTooltips:function(){e("#tiptip_holder").removeAttr("style"),e("#tiptip_arrow").removeAttr("style"),e(".tips").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:50})},onSubmit:function(e){d.addClass("is-busy"),e.data.view.block(),e.data.view.model.save(),e.preventDefault()},onDeleteRow:function(o){var i=o.data.view,s=i.model,d=_.indexBy(s.get("methods"),"instance_id"),a={},c=e(this).closest("tr").data("id");o.preventDefault(),window.confirm(n.strings.delete_shipping_method_confirmation)&&(l.block(),e.post({url:t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_remove_method",data:{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:c,zone_id:n.zone_id},success:function({data:e}){delete d[c],a.methods=a.methods||e.methods,s.set("methods",d),s.logChanges(a),i.clearUnloadConfirmation(),i.render(),l.unblock()},error:function(e,o,t){window.alert(n.strings.remove_method_failed),l.unblock()},dataType:"json"}))},onToggleEnabled:function(n){var o=n.data.view,t=e(n.target),i=o.model,s=_.indexBy(i.get("methods"),"instance_id"),d=t.closest("tr").data("id"),a="yes"===t.closest("tr").data("enabled")?"no":"yes",c={};n.preventDefault(),s[d].enabled=a,c.methods=c.methods||{methods:{}},c.methods[d]=_.extend(c.methods[d]||{},{enabled:a}),i.set("methods",s),i.logChanges(c),o.render()},setUnloadConfirmation:function(){this.needsUnloadConfirm=!0,d.prop("disabled",!1),d.removeClass("is-busy")},clearUnloadConfirmation:function(){this.needsUnloadConfirm=!1,d.attr("disabled","disabled")},unloadConfirmation:function(e){if(e.data.view.needsUnloadConfirm)return e.returnValue=n.strings.unload_confirmation_msg,window.event.returnValue=n.strings.unload_confirmation_msg,n.strings.unload_confirmation_msg},updateModelOnChange:function(n){var o=n.data.view.model,t=e(n.target),i=t.closest("tr").data("id"),s=t.data("attribute"),d=t.val(),a=_.indexBy(o.get("methods"),"instance_id"),c={};a[i][s]!==d&&(c.methods[i]={},c.methods[i][s]=d,a[i][s]=d),o.logChanges(c)},updateModelOnSort:function(e){var n=e.data.view.model,o=_.indexBy(n.get("methods"),"instance_id"),t={};_.each(o,function(e){var n=parseInt(e.method_order,10),s=parseInt(i.find('tr[data-id="'+e.instance_id+'"]').index()+1,10);n!==s&&(o[e.instance_id].method_order=s,t.methods=t.methods||{methods:{}},t.methods[e.instance_id]=_.extend(t.methods[e.instance_id]||{},{method_order:s}))}),_.size(t)&&n.logChanges(t)},onConfigureShippingMethod:function(n){var o=e(this).closest("tr").data("id"),t=n.data.view.model,i=_.indexBy(t.get("methods"),"instance_id")[o];if(!i.settings_html)return!0;n.preventDefault(),i.settings_html=l.reformatSettingsHTML(i.settings_html),e(this).WCBackboneModal({template:"wc-modal-shipping-method-settings",variable:{instance_id:o,method:i,status:"existing"},data:{instance_id:o,method:i,status:"existing"}}),l.highlightOnFocus(".wc-shipping-modal-price"),e(document.body).trigger("init_tooltips")},onConfigureShippingMethodSubmitted:function(o,i,s){"wc-modal-shipping-method-settings"===i&&(l.block(),e.post(t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_methods_save_settings",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:s.instance_id,data:s},function(o,t){"success"===t&&o.success?(e("table.wc-shipping-zone-methods").parent().find("#woocommerce_errors").remove(),o.data.errors.length>0&&l.showErrors(o.data.errors),_.size(l.model.changes)?l.model.save():l.model.onSaveResponse(o,t)):(window.alert(n.strings.save_failed),l.unblock())},"json"))},onConfigureShippingMethodBack:function(e,n){"wc-modal-shipping-method-settings"===n&&l.onAddShippingMethod(e)},showErrors:function(n){var o='<div id="woocommerce_errors" class="error notice is-dismissible">';e(n).each(function(e,n){o=o+"<p>"+n+"</p>"}),o+="</div>",e("table.wc-shipping-zone-methods").before(o)},highlightOnFocus:function(n){e(n).focus(function(){e(this).select()})},onAddShippingMethod:function(o){o.preventDefault(),e(this).WCBackboneModal({template:"wc-modal-add-shipping-method",variable:{zone_id:n.zone_id}}),e(".wc-shipping-zone-method-selector select").trigger("change"),e(".wc-shipping-zone-method-input input").change(function(){const n=e(".wc-shipping-zone-method-input input:checked").attr("id"),o=e(`#${n}-description`);e(".wc-shipping-zone-method-input-help-text").css("display","none"),o.css("display","block")})},reformatSettingsHTML:function(e){return[this.replaceHTMLTables,this.moveAdvancedCostsHelpTip,this.moveHTMLHelpTips,this.addCurrencySymbol].reduce((e,n)=>n(e),e)},moveAdvancedCostsHelpTip:function(n){const o=e(n),t=o.find("#wc-shipping-advanced-costs-help-text");t.addClass("wc-shipping-zone-method-fields-help-text");const i=o.find("#woocommerce_flat_rate_cost").closest("fieldset");return t.appendTo(i),o.prop("outerHTML")},addCurrencySymbol:function(n){if(!window.wc.ShippingCurrencyContext||!window.wc.ShippingCurrencyNumberFormat)return n;const o=e(n),t=o.find(".wc-shipping-modal-price"),i=window.wc.ShippingCurrencyContext.getCurrencyConfig(),{symbol:s,symbolPosition:d}=i;return t.addClass(`wc-shipping-currency-size-${s.length}`),t.addClass(`wc-shipping-currency-position-${d}`),t.before(`<div class="wc-shipping-zone-method-currency wc-shipping-currency-position-${d}">${s}</div>`),t.each(n=>{const o=e(t[n]),s=o.attr("value"),d=window.wc.ShippingCurrencyNumberFormat(i,s);o.attr("value",d)}),o.prop("outerHTML")},moveHTMLHelpTips:function(n){const o=["woocommerce_flat_rate_cost","woocommerce_flat_rate_no_class_cost","woocommerce_flat_rate_class_cost_"],t=e(n),i=t.find("label");return i.each(n=>{const s=e(i[n]),d=s.find(".woocommerce-help-tip");if(0===d.length)return;const a=s.attr("for");if(o.some(e=>a.includes(e))){t.find(`label[for=${a}] span.woocommerce-help-tip`).addClass("wc-shipping-visible-help-text")}else{if("woocommerce_free_shipping_ignore_discounts"===a){t.find(`#${a}`).closest("fieldset").find("label").append(d)}else{const e=d.data("tip"),n=t.find(`#${a}`).closest("fieldset");n.length&&0===n.find(".wc-shipping-zone-method-fields-help-text").length&&n.append(`<div class="wc-shipping-zone-method-fields-help-text">${e}</div>`)}"Coupons discounts"===s.text().trim()&&s.text("")}}),t.prop("outerHTML")},replaceHTMLTables:function(n){const o=e("<div>"+n+"</div>"),t=o.find("table.form-table");return t.each(n=>{const o=e(t[n]),i=e('<div class="wc-shipping-zone-method-fields" />');i.html(o.html()),o.replaceWith(i)}),o.prop("outerHTML")},onAddShippingMethodSubmitted:function(o,i,s,d){"wc-modal-add-shipping-method"===i&&(l.block(),e("#btn-next").addClass("is-busy"),e.post(t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_add_method",{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,method_id:s.add_method_id,zone_id:n.zone_id},function(o,t){"success"===t&&o.success&&(o.data.zone_id!==n.zone_id&&(n.zone_id=o.data.zone_id,window.history.pushState&&window.history.pushState({},"","admin.php?page=wc-settings&tab=shipping&zone_id="+o.data.zone_id)),l.model.set("methods",o.data.methods),d());var i=o.data.instance_id,s=o.data.methods[i];l.unblock(),s.settings_html?(s.settings_html=l.reformatSettingsHTML(s.settings_html),e(this).WCBackboneModal({template:"wc-modal-shipping-method-settings",variable:{instance_id:i,method:s,status:"new"},data:{instance_id:i,method:s,status:"new"}}),l.highlightOnFocus(".wc-shipping-modal-price")):(l.model.trigger("change:methods"),l.model.trigger("saved:methods")),e(document.body).trigger("init_tooltips")},"json"))},possiblyHideFreeShippingRequirements:function(n){if(Object.keys(n).includes("woocommerce_free_shipping_requires")){const o=null===n.woocommerce_free_shipping_requires||""===n.woocommerce_free_shipping_requires||"coupon"===n.woocommerce_free_shipping_requires,t=e("#woocommerce_free_shipping_requires").closest("fieldset"),i=t.nextAll("label"),s=t.nextAll("fieldset");i.each(n=>{e(i[n]).css("display",o?"none":"block")}),s.each(n=>{e(s[n]).css("display",o?"none":"block")})}},onModalLoaded:function(n,o){if("wc-modal-shipping-method-settings"===o){const o=e("#woocommerce_free_shipping_requires");o.length>0&&n.data.view.possiblyHideFreeShippingRequirements({woocommerce_free_shipping_requires:o.val()}),n.data.view.possiblyAddShippingClassLink(n)}},possiblyAddShippingClassLink:function(n){const o=e("article.wc-modal-shipping-method-settings"),t=o.data("shipping-classes-count"),i=(o.data("status"),o.data("id")),s=n.data.view.model;if("flat_rate"===_.indexBy(s.get("methods"),"instance_id")[i].id&&0===t){o.find(".wc-shipping-method-add-class-costs").css("display","block")}},validateFormArguments:function(e,n,o){if("wc-modal-add-shipping-method"===n){if(o.add_method_id){const e=document.getElementById("btn-next");e.disabled=!1,e.classList.remove("disabled")}}else"wc-modal-shipping-method-settings"===n&&e.data.view.possiblyHideFreeShippingRequirements(o)},onCloseConfigureShippingMethod:function(o,i,s,d){if("wc-modal-shipping-method-settings"===i){var a=e("#btn-ok").data();if(!d&&a&&"new"===a.status){l.block();var c=l,h=c.model,p=_.indexBy(h.get("methods"),"instance_id"),r={},m=s.instance_id;e.post({url:t+(t.indexOf("?")>0?"&":"?")+"action=woocommerce_shipping_zone_remove_method",data:{wc_shipping_zones_nonce:n.wc_shipping_zones_nonce,instance_id:m,zone_id:n.zone_id},success:function({data:e}){delete p[m],r.methods=r.methods||e.methods,h.set("methods",p),h.logChanges(r),c.clearUnloadConfirmation(),c.render(),l.unblock()},error:function(e,o,t){window.alert(n.strings.remove_method_failed),l.unblock()},dataType:"json"})}}},onChangeShippingMethodSelector:function(){var n=e(this).find("option:selected").data("description");e(this).parent().find(".wc-shipping-zone-method-description").remove(),e(this).after('<div class="wc-shipping-zone-method-description">'+n+"</div>")},onTogglePostcodes:function(n){n.preventDefault();var o=e(this).closest("tr");o.find(".wc-shipping-zone-postcodes").show(),o.find(".wc-shipping-zone-postcodes-toggle").hide()}}),r=new h({methods:n.methods,zone_name:n.zone_name}),l=new p({model:r,el:s});l.render(),s.sortable({items:"tr",cursor:"move",axis:"y",handle:"td.wc-shipping-zone-method-sort",scrollSensitivity:40})})}(jQuery,shippingZoneMethodsLocalizeScript,wp,ajaxurl);
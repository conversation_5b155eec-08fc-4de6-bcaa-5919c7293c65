jQuery(function(e){var t="table.wp-list-table",n='.column-handle input[name="term_id"]';0===e(t).find(".column-handle").length&&(e(t).find("tr:not(.inline-edit-row)").append('<td class="column-handle"></td>'),n=".check-column input"),window.wcTracks=window.wcTracks||{},window.wcTracks.recordEvent=window.wcTracks.recordEvent||function(){},e(t).find(".column-handle").show(),e.wc_add_missing_sort_handles=function(){var n=e(t).find("tbody > tr"),i=e(t).find("tbody > tr > td.column-handle").parent();n.length!==i.length&&n.each(function(t,n){i.is(n)||e(n).append('<td class="column-handle"></td>')}),e(t).find(".column-handle").show()},e(document).ajaxComplete(function(t,n,i){n&&4===n.readyState&&200===n.status&&i.data&&(0<=i.data.indexOf("_inline_edit")||0<=i.data.indexOf("add-tag"))&&(e.wc_add_missing_sort_handles(),e(document.body).trigger("init_tooltips"))}),e(t).sortable({items:"tbody tr:not(.inline-edit-row)",cursor:"move",handle:".column-handle",axis:"y",forcePlaceholderSize:!0,helper:"clone",opacity:.65,placeholder:"product-cat-placeholder",scrollSensitivity:40,start:function(e,t){t.item.hasClass("alternate")||t.item.css("background-color","#ffffff"),t.item.children("td, th").css("border-bottom-width","0"),t.item.css("outline","1px solid #aaa")},stop:function(e,t){t.item.removeAttr("style"),t.item.children("td, th").css("border-bottom-width","1px")},update:function(i,d){var o,a,r=d.item.find(n).val(),c=d.item.find(".parent").html(),l=d.item.prev().find(n).val(),s=d.item.next().find(n).val();l!==undefined&&(o=d.item.prev().find(".parent").html())!==c&&(l=undefined),s!==undefined&&(a=d.item.next().find(".parent").html())!==c&&(s=undefined),l===undefined&&s===undefined||s===undefined&&a===l||s!==undefined&&o===r?e(t).sortable("cancel"):(window.wcTracks.recordEvent("product_attributes_ordering_term",{is_category:"product_cat"===woocommerce_term_ordering_params.taxonomy?"yes":"no"}),d.item.find(".check-column input").hide(),d.item.find(".check-column").append('<img alt="processing" src="images/wpspin_light.gif" class="waiting" style="margin-left: 6px;" />'),e.post(ajaxurl,{action:"woocommerce_term_ordering",id:r,nextid:s,thetaxonomy:woocommerce_term_ordering_params.taxonomy},function(e){"children"===e?window.location.reload():(d.item.find(".check-column input").show(),d.item.find(".check-column").find("img").remove())}),e("table.widefat tbody tr").each(function(){jQuery("table.widefat tbody tr").index(this)%2==0?jQuery(this).addClass("alternate"):jQuery(this).removeClass("alternate")}))}})});
!function(e,t){e(function(){if(void 0!==t){var o=e(".edit-php.post-type-product"),i=o.find(".page-title-action:first");0===o.find(".woocommerce-BlankState").length?(t.urls.add_product&&i.first().attr("href",t.urls.add_product),t.urls.export_products&&i.after('<a href="'+t.urls.export_products+'" class="page-title-action">'+t.strings.export_products+"</a>"),t.urls.import_products&&i.after('<a href="'+t.urls.import_products+'" class="page-title-action">'+t.strings.import_products+"</a>")):i.hide(),e(".woocommerce-progress-form-wrapper .button-next").on("click",function(){return e(".wc-progress-form-content").block({message:null,overlayCSS:{background:"#fff",opacity:.6}}),!0}),e(document.body).on("wc_add_error_tip",function(o,i,r){var n=i.position();0===i.parent().find(".wc_error_tip").length&&(i.after('<div class="wc_error_tip '+r+'">'+t[r]+"</div>"),i.parent().find(".wc_error_tip").css("left",n.left+i.width()-i.width()/2-e(".wc_error_tip").width()/2).css("top",n.top+i.height()).fadeIn("100"))}).on("wc_remove_error_tip",function(t,o,i){o.parent().find(".wc_error_tip."+i).fadeOut("100",function(){e(this).remove()})}).on("click",function(){e(".wc_error_tip").fadeOut("100",function(){e(this).remove()})}).on("blur",".wc_input_decimal[type=text], .wc_input_price[type=text], .wc_input_country_iso[type=text]",function(){e(".wc_error_tip").fadeOut("100",function(){e(this).remove()})}).on("change",".wc_input_price[type=text], .wc_input_decimal[type=text], .wc-order-totals #refund_amount[type=text], .wc_input_variations_price[type=text]",function(){var o,i,r=t.decimal_point;(e(this).is(".wc_input_price")||e(this).is(".wc_input_variations_price")||e(this).is("#refund_amount"))&&(r=t.mon_decimal_point),o=new RegExp("[^-0-9%\\"+r+"]+","gi"),i=new RegExp("\\"+r+"+","gi");var n=e(this).val(),c=n.replace(o,"").replace(i,r);n!==c&&e(this).val(c)}).on("keyup",".wc_input_price[type=text], .wc_input_decimal[type=text], .wc_input_country_iso[type=text], .wc-order-totals #refund_amount[type=text], .wc_input_variations_price[type=text]",function(){var o,i,r,n=!1;e(this).is(".wc_input_price")||e(this).is(".wc_input_variations_price")||e(this).is("#refund_amount")?(n=!0,o=new RegExp("[^-0-9%\\"+t.mon_decimal_point+"]+","gi"),r=new RegExp("[^\\"+t.mon_decimal_point+"]","gi"),i="i18n_mon_decimal_error"):e(this).is(".wc_input_country_iso")?(o=new RegExp("([^A-Z])+|(.){3,}","im"),i="i18n_country_iso_error"):(n=!0,o=new RegExp("[^-0-9%\\"+t.decimal_point+"]+","gi"),r=new RegExp("[^\\"+t.decimal_point+"]","gi"),i="i18n_decimal_error");var c=e(this).val(),a=c.replace(o,"");n&&1<a.replace(r,"").length&&(a=a.replace(r,"")),c!==a?e(document.body).triggerHandler("wc_add_error_tip",[e(this),i]):e(document.body).triggerHandler("wc_remove_error_tip",[e(this),i])}).on("change","#_sale_price.wc_input_price[type=text], .wc_input_price[name^=variable_sale_price]",function(){var o,i=e(this);o=-1!==i.attr("name").indexOf("variable")?i.parents(".variable_pricing").find(".wc_input_price[name^=variable_regular_price]"):e("#_regular_price"),parseFloat(window.accounting.unformat(i.val(),t.mon_decimal_point))>=parseFloat(window.accounting.unformat(o.val(),t.mon_decimal_point))&&e(this).val("")}).on("keyup","#_sale_price.wc_input_price[type=text], .wc_input_price[name^=variable_sale_price]",function(){var o,i=e(this);o=-1!==i.attr("name").indexOf("variable")?i.parents(".variable_pricing").find(".wc_input_price[name^=variable_regular_price]"):e("#_regular_price"),parseFloat(window.accounting.unformat(i.val(),t.mon_decimal_point))>=parseFloat(window.accounting.unformat(o.val(),t.mon_decimal_point))?e(document.body).triggerHandler("wc_add_error_tip",[e(this),"i18n_sale_less_than_regular_error"]):e(document.body).triggerHandler("wc_remove_error_tip",[e(this),"i18n_sale_less_than_regular_error"])}).on("keyup","input[type=text][name*=_global_unique_id]",function(){var t=e(this).val();/[^0-9\-]/.test(t)?e(document.body).triggerHandler("wc_add_error_tip",[e(this),"i18n_global_unique_id_error"]):e(document.body).triggerHandler("wc_remove_error_tip",[e(this),"i18n_global_unique_id_error"])}).on("change","input[type=text][name*=_global_unique_id]",function(){var t=e(this).val();e(this).val(t.replace(/[^0-9\-]/g,"")),e(document.body).triggerHandler("wc_remove_error_tip",[e(this),"i18n_global_unique_id_error"])}).on("init_tooltips",function(){e(".tips, .help_tip, .woocommerce-help-tip").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200,keepAlive:!0}),e(".column-wc_actions .wc-action-button").tipTip({fadeIn:50,fadeOut:50,delay:200}),e(".parent-tips").each(function(){e(this).closest("a, th").attr("data-tip",e(this).data("tip")).tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200,keepAlive:!0}).css("cursor","help")})}).on("click",".wc-confirm-delete",function(e){window.confirm(t.i18n_confirm_delete)||e.stopPropagation()}),e(document.body).trigger("init_tooltips"),e(".wc_input_table.sortable tbody").sortable({items:"tr",cursor:"move",axis:"y",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,placeholder:"wc-metabox-sortable-placeholder",start:function(e,t){t.item.css("background-color","#f6f6f6")},stop:function(e,t){t.item.removeAttr("style")}}),e(".wc_input_table.sortable tbody input").on("click",function(){e(this).trigger("focus")}),e(".wc_input_table .remove_rows").on("click",function(){var t=e(this).closest(".wc_input_table").find("tbody");t.find("tr.current").length>0&&t.find("tr.current").each(function(){e(this).remove()});return!1});var r=!1,n=!1,c=!1;e(document.body).on("keyup keydown",function(e){n=e.shiftKey,r=e.ctrlKey||e.metaKey}),e(".wc_input_table").on("focus click","input",function(t){var o=e(this).closest("table, tbody"),i=e(this).closest("tr");("focus"===t.type&&c!==i.index()||"click"===t.type&&e(this).is(":focus"))&&(c=i.index(),n||r?n?(e("tr",o).removeClass("current"),i.addClass("selected_now").addClass("current"),e("tr.last_selected",o).length>0&&(i.index()>e("tr.last_selected",o).index()?e("tr",o).slice(e("tr.last_selected",o).index(),i.index()).addClass("current"):e("tr",o).slice(i.index(),e("tr.last_selected",o).index()+1).addClass("current")),e("tr",o).removeClass("last_selected"),i.addClass("last_selected")):(e("tr",o).removeClass("last_selected"),r&&e(this).closest("tr").is(".current")?i.removeClass("current"):i.addClass("current").addClass("last_selected")):(e("tr",o).removeClass("current").removeClass("last_selected"),i.addClass("current").addClass("last_selected")),e("tr",o).removeClass("selected_now"))}).on("blur","input",function(){c=!1}),e(".woocommerce_page_wc-settings .shippingrows tbody tr:even, table.attributes-table tbody tr:nth-child(odd)").addClass("alternate"),e(document.body).on("click",".show_order_items",function(){return e(this).closest("td").find("table").toggle(),!1}),e("select.availability").on("change",function(){"all"===e(this).val()?e(this).closest("tr").next("tr").hide():e(this).closest("tr").next("tr").show()}).trigger("change"),e(".hide_options_if_checked").each(function(){e(this).find("input:eq(0)").on("change",function(){e(this).is(":checked")?e(this).closest("fieldset, tr").nextUntil(".hide_options_if_checked, .show_options_if_checked",".hidden_option").hide():e(this).closest("fieldset, tr").nextUntil(".hide_options_if_checked, .show_options_if_checked",".hidden_option").show()}).trigger("change")}),e(".show_options_if_checked").each(function(){e(this).find("input:eq(0)").on("change",function(){e(this).is(":checked")?e(this).closest("fieldset, tr").nextUntil(".hide_options_if_checked, .show_options_if_checked",".hidden_option").show():e(this).closest("fieldset, tr").nextUntil(".hide_options_if_checked, .show_options_if_checked",".hidden_option").hide()}).trigger("change")}),e("input#woocommerce_enable_reviews").on("change",function(){e(this).is(":checked")?e("#woocommerce_enable_review_rating").closest("tr").show():e("#woocommerce_enable_review_rating").closest("tr").hide()}).trigger("change"),e("table.attributes-table tbody tr:nth-child(odd)").addClass("alternate"),e(".wc_gateways").on("click",".wc-payment-gateway-method-toggle-enabled",function(){var o=e(this),i=o.closest("tr"),r=o.find(".woocommerce-input-toggle"),n={action:"woocommerce_toggle_gateway_enabled",security:t.nonces.gateway_toggle,gateway_id:i.data("gateway_id")};return r.addClass("woocommerce-input-toggle--loading"),e.ajax({url:t.ajax_url,data:n,dataType:"json",type:"POST",success:function(e){!0===e.data?(r.removeClass("woocommerce-input-toggle--enabled, woocommerce-input-toggle--disabled"),r.addClass("woocommerce-input-toggle--enabled"),r.removeClass("woocommerce-input-toggle--loading")):!1===e.data?(r.removeClass("woocommerce-input-toggle--enabled, woocommerce-input-toggle--disabled"),r.addClass("woocommerce-input-toggle--disabled"),r.removeClass("woocommerce-input-toggle--loading")):"needs_setup"===e.data&&(window.location.href=o.attr("href"))}}),!1}),e("#wpbody").on("click","#doaction, #doaction2",function(){if("remove_personal_data"===(e(this).is("#doaction")?e("#bulk-action-selector-top").val():e("#bulk-action-selector-bottom").val()))return window.confirm(t.i18n_remove_personal_data_notice)});var a=e("#marketplace-current-section-dropdown"),s=e("#marketplace-current-section-name"),l=!1;a.length&&("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0?s.on("click",function(){(l=!l)?(a.addClass("is-open"),e(document).on("click",d)):(a.removeClass("is-open"),e(document).off("click",d))}):document.body.classList.add("no-touch"))}function d(t){a.is(t.target)||0!==a.has(t.target).length||(a.removeClass("is-open"),l=!1,e(document).off("click",d))}}),e(function(){var t={init:function(){this.$lock_dialog=e(".woocommerce_page_wc-orders #post-lock-dialog.order-lock-dialog"),0!==this.$lock_dialog.length&&"undefined"!=typeof woocommerce_admin_meta_boxes&&(e(document).off("heartbeat-send.refresh-lock"),e(document).off("heartbeat-tick.refresh-lock"),e(document).on("heartbeat-send",this.refresh_order_lock),e(document).on("heartbeat-tick",this.check_order_lock)),this.$list_table=e(".woocommerce_page_wc-orders table.wc-orders-list-table"),0!==this.$list_table.length&&(e(document).on("heartbeat-send",this.send_orders_in_list),e(document).on("heartbeat-tick",this.check_orders_in_list))},refresh_order_lock:function(e,t){delete t["wp-refresh-post-lock"],t["wc-refresh-order-lock"]=woocommerce_admin_meta_boxes.post_id},check_order_lock:function(o,i){var r=i["wc-refresh-order-lock"];r&&r.error&&(t.$lock_dialog.is(":visible")||(r.error.user_avatar_src&&t.$lock_dialog.find(".post-locked-avatar").empty().append(e("<img />",{"class":"avatar avatar-64 photo",width:64,height:64,alt:"",src:r.error.user_avatar_src,srcset:r.error.user_avatar_src_2x?r.error.user_avatar_src_2x+" 2x":undefined})),t.$lock_dialog.find(".currently-editing").text(r.error.message),t.$lock_dialog.show(),t.$lock_dialog.find(".wp-tab-first").trigger("focus")))},send_orders_in_list:function(e,o){o["wc-check-locked-orders"]=t.$list_table.find('tr input[name="id[]"]').map(function(){return this.value}).get()},check_orders_in_list:function(o,i){var r=i["wc-check-locked-orders"]||{};t.$list_table.find("tr").each(function(t,o){var i=e(o),n=i.find('input[name="id[]"]').val();r[n]?i.hasClass("wp-locked")||(i.find(".check-column checkbox").prop("checked",!1),i.addClass("wp-locked")):i.removeClass("wp-locked").find(".locked-info span").empty()})}};t.init()})}(jQuery,woocommerce_admin);
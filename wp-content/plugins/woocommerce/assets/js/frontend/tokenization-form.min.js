jQuery(function(e){var t=function(t){this.$target=t,this.$formWrap=t.closest(".payment_box"),this.params=e.extend({},{is_registration_required:!1,is_logged_in:!1},wc_tokenization_form_params),this.onDisplay=this.onDisplay.bind(this),this.hideForm=this.hideForm.bind(this),this.showForm=this.showForm.bind(this),this.showSaveNewCheckbox=this.showSaveNewCheckbox.bind(this),this.hideSaveNewCheckbox=this.hideSaveNewCheckbox.bind(this),this.$target.on("click change",":input.woocommerce-SavedPaymentMethods-tokenInput",{tokenizationForm:this},this.onTokenChange),e("input#createaccount").on("change",{tokenizationForm:this},this.onCreateAccountChange),this.onDisplay()};t.prototype.onDisplay=function(){0===e(":input.woocommerce-SavedPaymentMethods-tokenInput:checked",this.$target).length&&e(":input.woocommerce-SavedPaymentMethods-tokenInput:last",this.$target).prop("checked",!0),0===this.$target.data("count")&&e(".woocommerce-SavedPaymentMethods-new",this.$target).remove(),0<e("input#createaccount").length&&e("input#createaccount").is(":checked")||this.params.is_logged_in||this.params.is_registration_required?this.showSaveNewCheckbox():this.hideSaveNewCheckbox(),e(":input.woocommerce-SavedPaymentMethods-tokenInput:checked",this.$target).trigger("change")},t.prototype.onTokenChange=function(t){"new"===e(this).val()?(t.data.tokenizationForm.showForm(),t.data.tokenizationForm.showSaveNewCheckbox()):(t.data.tokenizationForm.hideForm(),t.data.tokenizationForm.hideSaveNewCheckbox())},t.prototype.onCreateAccountChange=function(t){e(this).is(":checked")?t.data.tokenizationForm.showSaveNewCheckbox():t.data.tokenizationForm.hideSaveNewCheckbox()},t.prototype.hideForm=function(){e(".wc-payment-form",this.$formWrap).hide()},t.prototype.showForm=function(){e(".wc-payment-form",this.$formWrap).show()},t.prototype.showSaveNewCheckbox=function(){e(".woocommerce-SavedPaymentMethods-saveNew",this.$formWrap).show()},t.prototype.hideSaveNewCheckbox=function(){e(".woocommerce-SavedPaymentMethods-saveNew",this.$formWrap).hide()},e.fn.wc_tokenization_form=function(e){return new t(this,e),this},e(document.body).on("updated_checkout wc-credit-card-form-init",function(){e("ul.woocommerce-SavedPaymentMethods").each(function(){e(this).wc_tokenization_form()})})});
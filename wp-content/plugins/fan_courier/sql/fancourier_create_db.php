<?php

if (! defined( 'ABSPATH' ) ) {
	exit;
}

function fancourier_create_db() {
	global $wpdb;
	error_log("instalare");

	$charset_collate = $wpdb->get_charset_collate();

	$auth_info =  $wpdb->prefix . 'fancourier_auth_info';
	$delivery_info = $wpdb->prefix . 'fancourier_delivery_info';
	$order_info = $wpdb->prefix . 'fancourier_order_info';
    $service = $wpdb->prefix . 'fancourier_service';
	

	$createAuthInfoTable = "CREATE TABLE IF NOT EXISTS $auth_info (
		id INT(10) NOT NULL AUTO_INCREMENT,
        token VARCHAR(255) NOT NULL,
        PRIMARY KEY (id),
		UNIQUE KEY id (id)
	) $charset_collate;";

	$createDeliveryInfoTable = "CREATE TABLE IF NOT EXISTS $delivery_info (
        id INT(10) NOT NULL AUTO_INCREMENT,
		id_cart INT(10) NOT NULL,
        price DOUBLE(8, 2) NOT NULL,
        fan_order VARCHAR(32),
        PRIMARY KEY (id),
		UNIQUE KEY id (id)
	) $charset_collate;";

	$createOrderInfoTable = "CREATE TABLE IF NOT EXISTS $order_info (
        id INT(10) NOT NULL AUTO_INCREMENT,
		id_order INT(10) NOT NULL ,
        fan_AWB VARCHAR(32) NOT NULL,
        PRIMARY KEY (id),
        UNIQUE KEY id (id)
	) $charset_collate;";

    $createServiceTable = "CREATE TABLE IF NOT EXISTS $service (
        id INT(11) NOT NULL AUTO_INCREMENT,
        fancourier_id INT(11) NOT NULL,
        fancourier_name VARCHAR(255),
        name VARCHAR(255),
        price DOUBLE(10, 2),
        price_free DOUBLE(10, 2),
        status INT(11),
        PRIMARY KEY (id),
        UNIQUE KEY id (id)
    ) $charset_collate;";

    $tablesToCreate = array(
        $createAuthInfoTable, $createDeliveryInfoTable, $createOrderInfoTable,$createServiceTable
    );

    foreach ($tablesToCreate as $sql) {
        $wpdb->query($sql);
    }

    $domain = site_url();
    $url = 'https://ecommerce.fancourier.ro/authShop';
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
    curl_setopt($ch, CURLOPT_POST, true);	
    curl_setopt($ch, CURLOPT_POSTFIELDS, "domain=$domain");
    curl_setopt($ch ,CURLOPT_SSL_VERIFYPEER,0);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    $responseDecoded = json_decode($response);

    if(property_exists($responseDecoded,"token")){
        $token = $responseDecoded->token;
    } else {
        return false;
    }
   
    if(strlen($token)==128){
    //insert token in fancourier_auth_info
        $sqlAuth =  "INSERT INTO `".$wpdb->prefix."fancourier_auth_info`(`token`) VALUES ('$token')";
        $wpdb->query($sqlAuth);
    } else {
       exit;
    }

    $authorization = "Authorization: Bearer ".$token;
		
    //get services
    $url = 'https://ecommerce.fancourier.ro/get-services';
    $chservices = curl_init($url);
    curl_setopt($chservices, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt ($chservices, CURLOPT_POST, true);
    curl_setopt($chservices, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded',$authorization));
    curl_setopt ($chservices, CURLOPT_POSTFIELDS, "domain=$domain");
    curl_setopt($chservices ,CURLOPT_SSL_VERIFYPEER,0);
    
    $response = curl_exec($chservices);

    $services = json_decode($response);
    //$this->log($services,1);
    curl_close($chservices);

    if(!is_array($services)){
        exit;
    } 
    
    foreach($services as $service){
        FANCourierQueryDb::addService($service);
    }
    
}


/*! Minified and unminified versions of this file are located in the same directory */

/*! AI Image Lab by WP Zone
 * Licensed under the GNU General Public License v3 (see ../license.txt)
 */

#wpz-woocommerce-random-orders-settings {
  $color_1: #3a3a3a;
  $color_2: #3A3A3A;
  $color_3: #f46859;
  $color_4: #c3c7cb;
  $color_5: #3D3B38;
  $color_6: #D0021B;

  $background-color_1: #fff;
  $background-color_2: #E7EBEE;
  $background-color_3: #FFE5E5;
  $border-color_1: #E7EBEE;
  $border-color_2: #D0021B;

  .ags-settings-addons-list {
    margin: 30px 0 0 0px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 20px;
    }
  .ags-settings-addon {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 20px;
    align-items: center;
    background: #FFFFFF;
    box-shadow: 0 0 20px #0000000D;
    border-radius: 33px;
    padding: 57px 30px 50px;
    box-sizing: border-box;
    text-align: center;
    position: relative;
    }
  .ags-settings-addon-img {
    width: 87px;
    height: 87px;
    object-fit: cover;
    margin: 0 auto 25px;
    float: unset;
    // overwrite background: #FFFFFF;
    box-shadow: 0px 0px 10px #0000000D;
    border-radius: 11px;
    }
  .ags-settings-addon-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    h4 {
      margin: 5px 0 15px;
      font-size: 15px !important;
      color: $color_1;
      }
    p {
      margin: 0 auto 25px !important;
      line-height: 1.8em;
      }
    .ags-settings-addon-badge {
      font-weight: 600;
      line-height: 1;
      padding: 8px 23px;
      font-size: 12px;
      border-radius: 7px;
      display: inline-block;
      margin-left: 10px;
      text-transform: uppercase;
      }
    }
  .ags-settings-addon-badges-wrapper {
    position: absolute;
    top: -12px;
    right: 15px;
    }
  a.ags-settings-addon-btn {
    font-size: 14px;
    font-weight: 600;
    border: 1px solid #E7EBEE;
    border-radius: 29px;
    background-color: $background-color_1;
    padding: 15px 20px;
    cursor: pointer;
    height: auto;
    color: $color_2 !important;
    line-height: normal !important;
    box-shadow: none !important;
    outline: none;
    min-width: 150px;
    margin: 5px;
    text-align: center;
    text-decoration: none !important;
    &:hover {
      color: $color_3 !important;
      background-color: $background-color_1;
      border: 1px solid #f46859;
      box-shadow: 0 0 10px #0000000D;
      }
    }
  a.ags-settings-addon-btn-disabled {
    cursor: not-allowed;
    color: $color_4 !important;
    background-color: $background-color_2;
    border-color: $border-color_1;
    &:hover {
      cursor: not-allowed;
      color: $color_4 !important;
      background-color: $background-color_2;
      border-color: $border-color_1;
      }
    }
  p.ags-settings-addons-error {
    padding: 10px 20px;
    line-height: 1.5;
    font-size: 15px !important;
    color: $color_5 !important;
    box-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.08);
    position: relative;
    border-radius: 4px;
    background-color: $background-color_3 !important;
    border-left: 5px solid;
    border-color: $border-color_2;
    a {
      color: $color_6;
      }
    }
  @media (max-width: 768px) {
    .ags-settings-addons-list {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
    }


  }
/*
 * 11.2 - Settings Header
 */

#wpz-woocommerce-random-orders-settings-header {
  background      : $dsMediumPurple;
  position        : relative;
  padding         : 16px 23px;
  justify-content : space-between;
  display         : flex;
  flex-wrap       : wrap;
  align-items     : center;
  border-radius   : 16px 16px 0 0;
  font-family     : $fontGeneral;
  font-weight     : 600;

  .wpz-woocommerce-random-orders-settings-logo {

    display: flex;
    align-items: center;

    img {
      display        : inline-block;
      vertical-align : middle;
      margin-right   : 10px;
      width          : 35px;
      }

    h1 {
      margin         : 0;
      color          : #fff;
      font-size      : 15px;
      font-weight    : 600;
      letter-spacing: -0.2px;
      display        : inline-block;
      vertical-align : middle;
      }
    }

  @media (max-width : 660px) {
    justify-content : center;

    .wpz-woocommerce-random-orders-settings-header-links {
      width      : 100%;
      margin     : 0 auto 8px;
      text-align : center;
      }

    #wpz-woocommerce-random-orders-settings-header-links {
      text-align : center;
      }
    }
  }

#wpz-woocommerce-random-orders-settings-header-links a {
  text-decoration     : none;
  color               : #fff;
  font-size           : 12px;
  font-weight         : 500;
  margin-left         : 20px;
  padding             : 8px 16px 8px 25px;
  display             : inline-block;
  border-radius       : 4px;
  background-size     : 14px;
  background-repeat   : no-repeat;
  background-position : left center;
  box-shadow          : none;

  &:hover {
    opacity : 1;
    color   : #fff;
    }

  &.active {
    opacity          : 1;
    background-color : #5f9ea0;
    }
  }

#wpz-woocommerce-random-orders-settings-header-link-settings {
  background-image : url(../images/settings.svg);
  display          : none !important;
  }

#wpz-woocommerce-random-orders-settings-header-link-support {
  background-image : url(../images/help.svg);
  }

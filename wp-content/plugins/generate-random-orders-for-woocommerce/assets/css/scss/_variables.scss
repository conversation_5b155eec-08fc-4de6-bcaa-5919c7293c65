// ------------------------------------------------------------------------------------------- //
//                            GLOBAL VARIABLES
// ------------------------------------------------------------------------------------------- //

// Color scheme
$mainColor         : #FD925C;
$mainHoverColor    : #f86e31;

// WP Zone Color Palette
$dsPastelPurple    : #B9B9E5;
$dsLightPurple     : #6A61E6;
$dsBrightPurple    : #9068FF;
$dsMainPurple      : #340F96;
$dsDarkPurple: #260065;
$subMenuPurple     : #4E00C5;

// New
$dsMediumPurple    : #6A39E4;
$wpz_secondary     : #8251FE;

$neonBlue          : #53C8FD;
$neonBlueDark      : #429FCA;

$primary1  : #9373e0;
$primary2  : #8554FF;
$primary3  : #A43DFF;
$primary4  : #6a39e4;

$secondary : #fd925c;

// Color scheme
$white     : #fff;
$gray1     : #f9fafb; // #0000000D
$gray2     : #e7ebee;
$gray3     : #dae0e3;
$gray4     : #949fa7;
$gray5     : #3a3a3a;


// notification colors
$success: #3ebb79; // notification color: success
$warning: #fc3; // notification color: warning / info
$danger: #F35252; // notification color:error
$red : #F35252;

$whiteColor: #fff;
$blackColor: #000;

// border colors
$lightBorderColor: #f3f6f8; // color for light borders
$middleBorderColor: #e7eaec; // color for dark borders
$darkBorderColor: #dde2e5; // color for dark borders
$checkboxGrey      : #E7EAEC;

// Font
$fontGeneral: "Montserrat", sans-serif;

$fontHeading: "Montserrat", sans-serif;
$fontButton: "Montserrat", sans-serif;
$fontSize          : 12px;

$baseSize: 12px; // general font size
$baseLineHeight: 1.7; // general line height
$lineHeight : 1.7;

$baseFontWeight: normal; // general font weight
$baseHeadingWeight: bold; // h1-h6 tags font weight

$fontWeight        : normal;
$headingWeight     : bold;

$baseFontColor: #3a3a3a; // general font color
$baseHeaderColor: #3a3a3a; // h1-h6 tags font color


$fontColor         : #3D3B38;
$headingColor      : #3D3B38;

// Rounded corners
$mainRadius        : 29px;
$formRadius        : 3px;

// Responsive breakpoints
$boxShadow         : 0 0 20px 0 rgba(0, 0, 0, 0.05);

$breakSmall        : 782px;
$breakSmallMin     : 783px;
$breakLarge        : 960px;
$breakLargeMin     : 961px;